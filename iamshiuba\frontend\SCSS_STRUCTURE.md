# SCSS Component Structure Guide

This document explains the modular SCSS structure implemented in the frontend, preserving the original component-based organization while modernizing with Tailwind CSS v3 and Flowbite.

## 📁 Directory Structure

```
src/styles/scss/
├── base/
│   ├── _variables.scss      # CSS custom properties and theme variables
│   ├── _base.scss          # Base HTML elements and global styles
│   └── _components.scss    # Reusable component classes
├── container/
│   ├── _main.scss          # Main layout container styles
│   ├── _navbar.scss        # Navigation bar styles
│   └── _footer.scss        # Footer styles
├── components/
│   ├── _home.scss          # Home page specific styles
│   ├── _streaming.scss     # Streaming page styles
│   ├── _about.scss         # About page styles
│   ├── _terms.scss         # Terms page styles
│   ├── _privacy.scss       # Privacy page styles
│   ├── _updates.scss       # Updates page styles
│   ├── _admin.scss         # Admin dashboard styles
│   └── _error.scss         # Error page styles
└── utils/
    ├── _theme-selector.scss # Theme switching utilities
    └── _pwa.scss           # PWA specific styles
```

## 🎨 How It Works

### 1. Main Entry Point
The `src/index.css` file imports all SCSS modules in the correct order:

```css
/* Import Tailwind CSS v3 and Flowbite */
@tailwind base;
@tailwind components;
@tailwind utilities;

/* Import base styles */
@import "./styles/scss/base/variables";
@import "./styles/scss/base/base";
@import "./styles/scss/base/components";

/* Import container styles */
@import "./styles/scss/container/main";
@import "./styles/scss/container/navbar";
@import "./styles/scss/container/footer";

/* Import component styles */
@import "./styles/scss/components/home";
@import "./styles/scss/components/streaming";
/* ... other components */

/* Import utility styles */
@import "./styles/scss/utils/theme-selector";
@import "./styles/scss/utils/pwa";
```

### 2. CSS Custom Properties
Theme variables are defined in `base/_variables.scss`:

```scss
:root {
  --background-primary: #ffffff;
  --background-secondary: #f8fafc;
  --text-primary: #1e293b;
  --text-secondary: #64748b;
  --accent-color: #ef4444;
  --border-color: #e2e8f0;
}

[data-theme="dark"] {
  --background-primary: #0f172a;
  --background-secondary: #1e293b;
  // ... other dark theme variables
}
```

### 3. Component-Specific Styles
Each page has its own SCSS file with nested selectors:

```scss
// components/_home.scss
#home {
  @apply justify-center items-center;

  .hero-section {
    @apply animate-[fade-in_1s_ease-in-out] py-16 overflow-hidden mx-auto md:max-w-[80%];

    .hero-wrapper {
      @apply flex flex-col lg:flex-row items-center;
    }
  }
}
```

## 🚀 Usage in React Components

### Method 1: Global Classes (Recommended)
Use the CSS classes directly in your JSX:

```jsx
const Home = () => {
  return (
    <div id="home" className="justify-center items-center">
      <section className="hero-section">
        <div className="hero-wrapper">
          <div className="hero-content">
            <h1>Welcome</h1>
          </div>
        </div>
      </section>
    </div>
  )
}
```

### Method 2: CSS Modules (Optional)
For component-specific styles, you can create CSS modules:

```jsx
// Import specific SCSS module
import styles from '../styles/scss/components/_home.scss'

const Home = () => {
  return (
    <div className={styles.home}>
      {/* Component content */}
    </div>
  )
}
```

## 🎯 Benefits

1. **Modular Organization**: Each page/component has its own SCSS file
2. **Maintainable**: Easy to find and update specific styles
3. **Scalable**: Add new components without affecting existing ones
4. **Theme Support**: CSS custom properties enable dynamic theming
5. **Tailwind Integration**: Combines utility classes with component styles
6. **Flowbite Ready**: Includes Flowbite components and styling

## 🔧 Development Workflow

1. **Adding New Components**: Create a new SCSS file in `components/`
2. **Import in index.css**: Add the import statement
3. **Use in React**: Apply the CSS classes in your JSX
4. **Theme Variables**: Use CSS custom properties for consistent theming

## 📦 Dependencies

- **Tailwind CSS v3.4.0**: Utility-first CSS framework
- **Flowbite v2.5.2**: Component library
- **Flowbite React v0.10.2**: React components
- **Sass v1.83.0**: SCSS preprocessing

## 🎨 Theme System

The theme system uses CSS custom properties that can be dynamically changed:

```javascript
// Change theme
document.documentElement.setAttribute('data-theme', 'dark')
```

Available themes:
- `light` (default)
- `dark`
- `black`
- `red`

## 🔍 Best Practices

1. **Use Semantic Class Names**: Follow BEM or similar naming conventions
2. **Leverage Tailwind**: Use utility classes for common styles
3. **Component Isolation**: Keep component styles in their respective files
4. **Theme Consistency**: Use CSS custom properties for colors
5. **Performance**: Import only needed SCSS modules

This structure preserves the original design while providing a modern, maintainable foundation for the React frontend.
