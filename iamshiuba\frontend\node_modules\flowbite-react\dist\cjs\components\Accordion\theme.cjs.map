{"version": 3, "file": "theme.cjs", "sources": ["../../../../src/components/Accordion/theme.ts"], "sourcesContent": ["import { createTheme } from \"../../helpers/create-theme\";\nimport type { FlowbiteAccordionTheme } from \"./Accordion\";\n\nexport const accordionTheme: FlowbiteAccordionTheme = createTheme({\n  root: {\n    base: \"divide-y divide-gray-200 border-gray-200 dark:divide-gray-700 dark:border-gray-700\",\n    flush: {\n      off: \"rounded-lg border\",\n      on: \"border-b\",\n    },\n  },\n  content: {\n    base: \"p-5 first:rounded-t-lg last:rounded-b-lg dark:bg-gray-900\",\n  },\n  title: {\n    arrow: {\n      base: \"h-6 w-6 shrink-0\",\n      open: {\n        off: \"\",\n        on: \"rotate-180\",\n      },\n    },\n    base: \"flex w-full items-center justify-between p-5 text-left font-medium text-gray-500 first:rounded-t-lg last:rounded-b-lg dark:text-gray-400\",\n    flush: {\n      off: \"hover:bg-gray-100 focus:ring-4 focus:ring-gray-200 dark:hover:bg-gray-800 dark:focus:ring-gray-800\",\n      on: \"bg-transparent dark:bg-transparent\",\n    },\n    heading: \"\",\n    open: {\n      off: \"\",\n      on: \"bg-gray-100 text-gray-900 dark:bg-gray-800 dark:text-white\",\n    },\n  },\n});\n"], "names": ["createTheme"], "mappings": ";;;;AAEY,MAAC,cAAc,GAAGA,uBAAW,CAAC;AAC1C,EAAE,IAAI,EAAE;AACR,IAAI,IAAI,EAAE,oFAAoF;AAC9F,IAAI,KAAK,EAAE;AACX,MAAM,GAAG,EAAE,mBAAmB;AAC9B,MAAM,EAAE,EAAE,UAAU;AACpB,KAAK;AACL,GAAG;AACH,EAAE,OAAO,EAAE;AACX,IAAI,IAAI,EAAE,2DAA2D;AACrE,GAAG;AACH,EAAE,KAAK,EAAE;AACT,IAAI,KAAK,EAAE;AACX,MAAM,IAAI,EAAE,kBAAkB;AAC9B,MAAM,IAAI,EAAE;AACZ,QAAQ,GAAG,EAAE,EAAE;AACf,QAAQ,EAAE,EAAE,YAAY;AACxB,OAAO;AACP,KAAK;AACL,IAAI,IAAI,EAAE,0IAA0I;AACpJ,IAAI,KAAK,EAAE;AACX,MAAM,GAAG,EAAE,oGAAoG;AAC/G,MAAM,EAAE,EAAE,oCAAoC;AAC9C,KAAK;AACL,IAAI,OAAO,EAAE,EAAE;AACf,IAAI,IAAI,EAAE;AACV,MAAM,GAAG,EAAE,EAAE;AACb,MAAM,EAAE,EAAE,4DAA4D;AACtE,KAAK;AACL,GAAG;AACH,CAAC;;;;"}