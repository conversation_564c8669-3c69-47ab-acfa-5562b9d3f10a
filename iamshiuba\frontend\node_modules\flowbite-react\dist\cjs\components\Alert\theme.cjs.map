{"version": 3, "file": "theme.cjs", "sources": ["../../../../src/components/Alert/theme.ts"], "sourcesContent": ["import { createTheme } from \"../../helpers/create-theme\";\nimport type { FlowbiteAlertTheme } from \"./Alert\";\n\nexport const alertTheme: FlowbiteAlertTheme = createTheme({\n  base: \"flex flex-col gap-2 p-4 text-sm\",\n  borderAccent: \"border-t-4\",\n  closeButton: {\n    base: \"-m-1.5 ml-auto inline-flex h-8 w-8 rounded-lg p-1.5 focus:ring-2\",\n    icon: \"h-5 w-5\",\n    color: {\n      info: \"bg-cyan-100 text-cyan-500 hover:bg-cyan-200 focus:ring-cyan-400 dark:bg-cyan-200 dark:text-cyan-600 dark:hover:bg-cyan-300\",\n      gray: \"bg-gray-100 text-gray-500 hover:bg-gray-200 focus:ring-gray-400 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-800 dark:hover:text-white\",\n      failure:\n        \"bg-red-100 text-red-500 hover:bg-red-200 focus:ring-red-400 dark:bg-red-200 dark:text-red-600 dark:hover:bg-red-300\",\n      success:\n        \"bg-green-100 text-green-500 hover:bg-green-200 focus:ring-green-400 dark:bg-green-200 dark:text-green-600 dark:hover:bg-green-300\",\n      warning:\n        \"bg-yellow-100 text-yellow-500 hover:bg-yellow-200 focus:ring-yellow-400 dark:bg-yellow-200 dark:text-yellow-600 dark:hover:bg-yellow-300\",\n      red: \"bg-red-100 text-red-500 hover:bg-red-200 focus:ring-red-400 dark:bg-red-200 dark:text-red-600 dark:hover:bg-red-300\",\n      green:\n        \"bg-green-100 text-green-500 hover:bg-green-200 focus:ring-green-400 dark:bg-green-200 dark:text-green-600 dark:hover:bg-green-300\",\n      yellow:\n        \"bg-yellow-100 text-yellow-500 hover:bg-yellow-200 focus:ring-yellow-400 dark:bg-yellow-200 dark:text-yellow-600 dark:hover:bg-yellow-300\",\n      blue: \"bg-blue-100 text-blue-500 hover:bg-blue-200 focus:ring-blue-400 dark:bg-blue-200 dark:text-blue-600 dark:hover:bg-blue-300\",\n      cyan: \"bg-cyan-100 text-cyan-500 hover:bg-cyan-200 focus:ring-cyan-400 dark:bg-cyan-200 dark:text-cyan-600 dark:hover:bg-cyan-300\",\n      pink: \"bg-pink-100 text-pink-500 hover:bg-pink-200 focus:ring-pink-400 dark:bg-pink-200 dark:text-pink-600 dark:hover:bg-pink-300\",\n      lime: \"bg-lime-100 text-lime-500 hover:bg-lime-200 focus:ring-lime-400 dark:bg-lime-200 dark:text-lime-600 dark:hover:bg-lime-300\",\n      dark: \"bg-gray-100 text-gray-500 hover:bg-gray-200 focus:ring-gray-400 dark:bg-gray-200 dark:text-gray-600 dark:hover:bg-gray-300\",\n      indigo:\n        \"bg-indigo-100 text-indigo-500 hover:bg-indigo-200 focus:ring-indigo-400 dark:bg-indigo-200 dark:text-indigo-600 dark:hover:bg-indigo-300\",\n      purple:\n        \"bg-purple-100 text-purple-500 hover:bg-purple-200 focus:ring-purple-400 dark:bg-purple-200 dark:text-purple-600 dark:hover:bg-purple-300\",\n      teal: \"bg-teal-100 text-teal-500 hover:bg-teal-200 focus:ring-teal-400 dark:bg-teal-200 dark:text-teal-600 dark:hover:bg-teal-300\",\n      light:\n        \"bg-gray-50 text-gray-500 hover:bg-gray-100 focus:ring-gray-200 dark:bg-gray-600 dark:text-gray-200 dark:hover:bg-gray-700 dark:hover:text-white\",\n    },\n  },\n  color: {\n    info: \"border-cyan-500 bg-cyan-100 text-cyan-700 dark:bg-cyan-200 dark:text-cyan-800\",\n    gray: \"border-gray-500 bg-gray-100 text-gray-700 dark:bg-gray-700 dark:text-gray-300\",\n    failure: \"border-red-500 bg-red-100 text-red-700 dark:bg-red-200 dark:text-red-800\",\n    success: \"border-green-500 bg-green-100 text-green-700 dark:bg-green-200 dark:text-green-800\",\n    warning: \"border-yellow-500 bg-yellow-100 text-yellow-700 dark:bg-yellow-200 dark:text-yellow-800\",\n    red: \"border-red-500 bg-red-100 text-red-700 dark:bg-red-200 dark:text-red-800\",\n    green: \"border-green-500 bg-green-100 text-green-700 dark:bg-green-200 dark:text-green-800\",\n    yellow: \"border-yellow-500 bg-yellow-100 text-yellow-700 dark:bg-yellow-200 dark:text-yellow-800\",\n    blue: \"border-blue-500 bg-blue-100 text-blue-700 dark:bg-blue-200 dark:text-blue-800\",\n    cyan: \"border-cyan-500 bg-cyan-100 text-cyan-700 dark:bg-cyan-200 dark:text-cyan-800\",\n    pink: \"border-pink-500 bg-pink-100 text-pink-700 dark:bg-pink-200 dark:text-pink-800\",\n    lime: \"border-lime-500 bg-lime-100 text-lime-700 dark:bg-lime-200 dark:text-lime-800\",\n    dark: \"border-gray-600 bg-gray-800 text-gray-200 dark:bg-gray-900 dark:text-gray-300\",\n    indigo: \"border-indigo-500 bg-indigo-100 text-indigo-700 dark:bg-indigo-200 dark:text-indigo-800\",\n    purple: \"border-purple-500 bg-purple-100 text-purple-700 dark:bg-purple-200 dark:text-purple-800\",\n    teal: \"border-teal-500 bg-teal-100 text-teal-700 dark:bg-teal-200 dark:text-teal-800\",\n    light: \"border-gray-400 bg-gray-50 text-gray-600 dark:bg-gray-500 dark:text-gray-200\",\n  },\n  icon: \"mr-3 inline h-5 w-5 flex-shrink-0\",\n  rounded: \"rounded-lg\",\n  wrapper: \"flex items-center\",\n});\n"], "names": ["createTheme"], "mappings": ";;;;AAEY,MAAC,UAAU,GAAGA,uBAAW,CAAC;AACtC,EAAE,IAAI,EAAE,iCAAiC;AACzC,EAAE,YAAY,EAAE,YAAY;AAC5B,EAAE,WAAW,EAAE;AACf,IAAI,IAAI,EAAE,kEAAkE;AAC5E,IAAI,IAAI,EAAE,SAAS;AACnB,IAAI,KAAK,EAAE;AACX,MAAM,IAAI,EAAE,4HAA4H;AACxI,MAAM,IAAI,EAAE,kJAAkJ;AAC9J,MAAM,OAAO,EAAE,qHAAqH;AACpI,MAAM,OAAO,EAAE,mIAAmI;AAClJ,MAAM,OAAO,EAAE,0IAA0I;AACzJ,MAAM,GAAG,EAAE,qHAAqH;AAChI,MAAM,KAAK,EAAE,mIAAmI;AAChJ,MAAM,MAAM,EAAE,0IAA0I;AACxJ,MAAM,IAAI,EAAE,4HAA4H;AACxI,MAAM,IAAI,EAAE,4HAA4H;AACxI,MAAM,IAAI,EAAE,4HAA4H;AACxI,MAAM,IAAI,EAAE,4HAA4H;AACxI,MAAM,IAAI,EAAE,4HAA4H;AACxI,MAAM,MAAM,EAAE,0IAA0I;AACxJ,MAAM,MAAM,EAAE,0IAA0I;AACxJ,MAAM,IAAI,EAAE,4HAA4H;AACxI,MAAM,KAAK,EAAE,iJAAiJ;AAC9J,KAAK;AACL,GAAG;AACH,EAAE,KAAK,EAAE;AACT,IAAI,IAAI,EAAE,+EAA+E;AACzF,IAAI,IAAI,EAAE,+EAA+E;AACzF,IAAI,OAAO,EAAE,0EAA0E;AACvF,IAAI,OAAO,EAAE,oFAAoF;AACjG,IAAI,OAAO,EAAE,yFAAyF;AACtG,IAAI,GAAG,EAAE,0EAA0E;AACnF,IAAI,KAAK,EAAE,oFAAoF;AAC/F,IAAI,MAAM,EAAE,yFAAyF;AACrG,IAAI,IAAI,EAAE,+EAA+E;AACzF,IAAI,IAAI,EAAE,+EAA+E;AACzF,IAAI,IAAI,EAAE,+EAA+E;AACzF,IAAI,IAAI,EAAE,+EAA+E;AACzF,IAAI,IAAI,EAAE,+EAA+E;AACzF,IAAI,MAAM,EAAE,yFAAyF;AACrG,IAAI,MAAM,EAAE,yFAAyF;AACrG,IAAI,IAAI,EAAE,+EAA+E;AACzF,IAAI,KAAK,EAAE,8EAA8E;AACzF,GAAG;AACH,EAAE,IAAI,EAAE,mCAAmC;AAC3C,EAAE,OAAO,EAAE,YAAY;AACvB,EAAE,OAAO,EAAE,mBAAmB;AAC9B,CAAC;;;;"}