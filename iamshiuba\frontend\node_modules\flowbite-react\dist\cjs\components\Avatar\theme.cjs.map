{"version": 3, "file": "theme.cjs", "sources": ["../../../../src/components/Avatar/theme.ts"], "sourcesContent": ["import { createTheme } from \"../../helpers/create-theme\";\nimport type { FlowbiteAvatarTheme } from \"./Avatar\";\n\nexport const avatarTheme: FlowbiteAvatarTheme = createTheme({\n  root: {\n    base: \"flex items-center justify-center space-x-4 rounded\",\n    bordered: \"p-1 ring-2\",\n    rounded: \"rounded-full\",\n    color: {\n      dark: \"ring-gray-800 dark:ring-gray-800\",\n      failure: \"ring-red-500 dark:ring-red-700\",\n      gray: \"ring-gray-500 dark:ring-gray-400\",\n      info: \"ring-cyan-400 dark:ring-cyan-800\",\n      light: \"ring-gray-300 dark:ring-gray-500\",\n      purple: \"ring-purple-500 dark:ring-purple-600\",\n      success: \"ring-green-500 dark:ring-green-500\",\n      warning: \"ring-yellow-300 dark:ring-yellow-500\",\n      pink: \"ring-pink-500 dark:ring-pink-500\",\n    },\n    img: {\n      base: \"rounded\",\n      off: \"relative overflow-hidden bg-gray-100 dark:bg-gray-600\",\n      on: \"\",\n      placeholder: \"absolute -bottom-1 h-auto w-auto text-gray-400\",\n    },\n    size: {\n      xs: \"h-6 w-6\",\n      sm: \"h-8 w-8\",\n      md: \"h-10 w-10\",\n      lg: \"h-20 w-20\",\n      xl: \"h-36 w-36\",\n    },\n    stacked: \"ring-2 ring-gray-300 dark:ring-gray-500\",\n    statusPosition: {\n      \"bottom-left\": \"-bottom-1 -left-1\",\n      \"bottom-center\": \"-bottom-1\",\n      \"bottom-right\": \"-bottom-1 -right-1\",\n      \"top-left\": \"-left-1 -top-1\",\n      \"top-center\": \"-top-1\",\n      \"top-right\": \"-right-1 -top-1\",\n      \"center-right\": \"-right-1\",\n      center: \"\",\n      \"center-left\": \"-left-1\",\n    },\n    status: {\n      away: \"bg-yellow-400\",\n      base: \"absolute h-3.5 w-3.5 rounded-full border-2 border-white dark:border-gray-800\",\n      busy: \"bg-red-400\",\n      offline: \"bg-gray-400\",\n      online: \"bg-green-400\",\n    },\n    initials: {\n      text: \"font-medium text-gray-600 dark:text-gray-300\",\n      base: \"relative inline-flex items-center justify-center overflow-hidden bg-gray-100 dark:bg-gray-600\",\n    },\n  },\n  group: {\n    base: \"flex -space-x-4\",\n  },\n  groupCounter: {\n    base: \"relative flex h-10 w-10 items-center justify-center rounded-full bg-gray-700 text-xs font-medium text-white ring-2 ring-gray-300 hover:bg-gray-600 dark:ring-gray-500\",\n  },\n});\n"], "names": ["createTheme"], "mappings": ";;;;AAEY,MAAC,WAAW,GAAGA,uBAAW,CAAC;AACvC,EAAE,IAAI,EAAE;AACR,IAAI,IAAI,EAAE,oDAAoD;AAC9D,IAAI,QAAQ,EAAE,YAAY;AAC1B,IAAI,OAAO,EAAE,cAAc;AAC3B,IAAI,KAAK,EAAE;AACX,MAAM,IAAI,EAAE,kCAAkC;AAC9C,MAAM,OAAO,EAAE,gCAAgC;AAC/C,MAAM,IAAI,EAAE,kCAAkC;AAC9C,MAAM,IAAI,EAAE,kCAAkC;AAC9C,MAAM,KAAK,EAAE,kCAAkC;AAC/C,MAAM,MAAM,EAAE,sCAAsC;AACpD,MAAM,OAAO,EAAE,oCAAoC;AACnD,MAAM,OAAO,EAAE,sCAAsC;AACrD,MAAM,IAAI,EAAE,kCAAkC;AAC9C,KAAK;AACL,IAAI,GAAG,EAAE;AACT,MAAM,IAAI,EAAE,SAAS;AACrB,MAAM,GAAG,EAAE,uDAAuD;AAClE,MAAM,EAAE,EAAE,EAAE;AACZ,MAAM,WAAW,EAAE,gDAAgD;AACnE,KAAK;AACL,IAAI,IAAI,EAAE;AACV,MAAM,EAAE,EAAE,SAAS;AACnB,MAAM,EAAE,EAAE,SAAS;AACnB,MAAM,EAAE,EAAE,WAAW;AACrB,MAAM,EAAE,EAAE,WAAW;AACrB,MAAM,EAAE,EAAE,WAAW;AACrB,KAAK;AACL,IAAI,OAAO,EAAE,yCAAyC;AACtD,IAAI,cAAc,EAAE;AACpB,MAAM,aAAa,EAAE,mBAAmB;AACxC,MAAM,eAAe,EAAE,WAAW;AAClC,MAAM,cAAc,EAAE,oBAAoB;AAC1C,MAAM,UAAU,EAAE,gBAAgB;AAClC,MAAM,YAAY,EAAE,QAAQ;AAC5B,MAAM,WAAW,EAAE,iBAAiB;AACpC,MAAM,cAAc,EAAE,UAAU;AAChC,MAAM,MAAM,EAAE,EAAE;AAChB,MAAM,aAAa,EAAE,SAAS;AAC9B,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,MAAM,IAAI,EAAE,eAAe;AAC3B,MAAM,IAAI,EAAE,8EAA8E;AAC1F,MAAM,IAAI,EAAE,YAAY;AACxB,MAAM,OAAO,EAAE,aAAa;AAC5B,MAAM,MAAM,EAAE,cAAc;AAC5B,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,IAAI,EAAE,8CAA8C;AAC1D,MAAM,IAAI,EAAE,+FAA+F;AAC3G,KAAK;AACL,GAAG;AACH,EAAE,KAAK,EAAE;AACT,IAAI,IAAI,EAAE,iBAAiB;AAC3B,GAAG;AACH,EAAE,YAAY,EAAE;AAChB,IAAI,IAAI,EAAE,uKAAuK;AACjL,GAAG;AACH,CAAC;;;;"}