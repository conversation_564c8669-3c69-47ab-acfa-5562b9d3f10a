{"version": 3, "file": "theme.cjs", "sources": ["../../../../src/components/Blockquote/theme.ts"], "sourcesContent": ["import { createTheme } from \"../../helpers/create-theme\";\nimport type { FlowbiteBlockquoteTheme } from \"./Blockquote\";\n\nexport const blockquoteTheme: FlowbiteBlockquoteTheme = createTheme({\n  root: {\n    base: \"text-xl font-semibold italic text-gray-900 dark:text-white\",\n  },\n});\n"], "names": ["createTheme"], "mappings": ";;;;AAEY,MAAC,eAAe,GAAGA,uBAAW,CAAC;AAC3C,EAAE,IAAI,EAAE;AACR,IAAI,IAAI,EAAE,4DAA4D;AACtE,GAAG;AACH,CAAC;;;;"}