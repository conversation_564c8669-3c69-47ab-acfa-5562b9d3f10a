{"version": 3, "file": "theme.cjs", "sources": ["../../../../src/components/Card/theme.ts"], "sourcesContent": ["import { createTheme } from \"../../helpers/create-theme\";\nimport type { FlowbiteCardTheme } from \"./Card\";\n\nexport const cardTheme: FlowbiteCardTheme = createTheme({\n  root: {\n    base: \"flex rounded-lg border border-gray-200 bg-white shadow-md dark:border-gray-700 dark:bg-gray-800\",\n    children: \"flex h-full flex-col justify-center gap-4 p-6\",\n    horizontal: {\n      off: \"flex-col\",\n      on: \"flex-col md:max-w-xl md:flex-row\",\n    },\n    href: \"hover:bg-gray-100 dark:hover:bg-gray-700\",\n  },\n  img: {\n    base: \"\",\n    horizontal: {\n      off: \"rounded-t-lg\",\n      on: \"h-96 w-full rounded-t-lg object-cover md:h-auto md:w-48 md:rounded-none md:rounded-l-lg\",\n    },\n  },\n});\n"], "names": ["createTheme"], "mappings": ";;;;AAEY,MAAC,SAAS,GAAGA,uBAAW,CAAC;AACrC,EAAE,IAAI,EAAE;AACR,IAAI,IAAI,EAAE,iGAAiG;AAC3G,IAAI,QAAQ,EAAE,+CAA+C;AAC7D,IAAI,UAAU,EAAE;AAChB,MAAM,GAAG,EAAE,UAAU;AACrB,MAAM,EAAE,EAAE,kCAAkC;AAC5C,KAAK;AACL,IAAI,IAAI,EAAE,0CAA0C;AACpD,GAAG;AACH,EAAE,GAAG,EAAE;AACP,IAAI,IAAI,EAAE,EAAE;AACZ,IAAI,UAAU,EAAE;AAChB,MAAM,GAAG,EAAE,cAAc;AACzB,MAAM,EAAE,EAAE,yFAAyF;AACnG,KAAK;AACL,GAAG;AACH,CAAC;;;;"}