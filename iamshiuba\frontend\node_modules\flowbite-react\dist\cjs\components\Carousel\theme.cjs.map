{"version": 3, "file": "theme.cjs", "sources": ["../../../../src/components/Carousel/theme.ts"], "sourcesContent": ["import { createTheme } from \"../../helpers/create-theme\";\nimport type { FlowbiteCarouselTheme } from \"./Carousel\";\n\nexport const carouselTheme: FlowbiteCarouselTheme = createTheme({\n  root: {\n    base: \"relative h-full w-full\",\n    leftControl: \"absolute left-0 top-0 flex h-full items-center justify-center px-4 focus:outline-none\",\n    rightControl: \"absolute right-0 top-0 flex h-full items-center justify-center px-4 focus:outline-none\",\n  },\n  indicators: {\n    active: {\n      off: \"bg-white/50 hover:bg-white dark:bg-gray-800/50 dark:hover:bg-gray-800\",\n      on: \"bg-white dark:bg-gray-800\",\n    },\n    base: \"h-3 w-3 rounded-full\",\n    wrapper: \"absolute bottom-5 left-1/2 flex -translate-x-1/2 space-x-3\",\n  },\n  item: {\n    base: \"absolute left-1/2 top-1/2 block w-full -translate-x-1/2 -translate-y-1/2\",\n    wrapper: {\n      off: \"w-full flex-shrink-0 transform cursor-default snap-center\",\n      on: \"w-full flex-shrink-0 transform cursor-grab snap-center\",\n    },\n  },\n  control: {\n    base: \"inline-flex h-8 w-8 items-center justify-center rounded-full bg-white/30 group-hover:bg-white/50 group-focus:outline-none group-focus:ring-4 group-focus:ring-white dark:bg-gray-800/30 dark:group-hover:bg-gray-800/60 dark:group-focus:ring-gray-800/70 sm:h-10 sm:w-10\",\n    icon: \"h-5 w-5 text-white dark:text-gray-800 sm:h-6 sm:w-6\",\n  },\n  scrollContainer: {\n    base: \"flex h-full snap-mandatory overflow-y-hidden overflow-x-scroll scroll-smooth rounded-lg\",\n    snap: \"snap-x\",\n  },\n});\n"], "names": ["createTheme"], "mappings": ";;;;AAEY,MAAC,aAAa,GAAGA,uBAAW,CAAC;AACzC,EAAE,IAAI,EAAE;AACR,IAAI,IAAI,EAAE,wBAAwB;AAClC,IAAI,WAAW,EAAE,uFAAuF;AACxG,IAAI,YAAY,EAAE,wFAAwF;AAC1G,GAAG;AACH,EAAE,UAAU,EAAE;AACd,IAAI,MAAM,EAAE;AACZ,MAAM,GAAG,EAAE,uEAAuE;AAClF,MAAM,EAAE,EAAE,2BAA2B;AACrC,KAAK;AACL,IAAI,IAAI,EAAE,sBAAsB;AAChC,IAAI,OAAO,EAAE,4DAA4D;AACzE,GAAG;AACH,EAAE,IAAI,EAAE;AACR,IAAI,IAAI,EAAE,0EAA0E;AACpF,IAAI,OAAO,EAAE;AACb,MAAM,GAAG,EAAE,2DAA2D;AACtE,MAAM,EAAE,EAAE,wDAAwD;AAClE,KAAK;AACL,GAAG;AACH,EAAE,OAAO,EAAE;AACX,IAAI,IAAI,EAAE,2QAA2Q;AACrR,IAAI,IAAI,EAAE,qDAAqD;AAC/D,GAAG;AACH,EAAE,eAAe,EAAE;AACnB,IAAI,IAAI,EAAE,yFAAyF;AACnG,IAAI,IAAI,EAAE,QAAQ;AAClB,GAAG;AACH,CAAC;;;;"}