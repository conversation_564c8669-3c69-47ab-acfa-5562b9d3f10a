{"version": 3, "file": "theme.cjs", "sources": ["../../../../src/components/Checkbox/theme.ts"], "sourcesContent": ["import { createTheme } from \"../../helpers/create-theme\";\nimport type { FlowbiteCheckboxTheme } from \"./Checkbox\";\n\nexport const checkboxTheme: FlowbiteCheckboxTheme = createTheme({\n  root: {\n    base: \"h-4 w-4 rounded border border-gray-300 bg-gray-100 focus:ring-2 dark:border-gray-600 dark:bg-gray-700\",\n    color: {\n      default: \"text-cyan-600 focus:ring-cyan-600 dark:ring-offset-gray-800 dark:focus:ring-cyan-600\",\n      dark: \"text-gray-800 focus:ring-gray-800 dark:ring-offset-gray-800 dark:focus:ring-gray-800\",\n      failure: \"text-red-900 focus:ring-red-900 dark:ring-offset-red-900 dark:focus:ring-red-900\",\n      gray: \"text-gray-900 focus:ring-gray-900 dark:ring-offset-gray-900 dark:focus:ring-gray-900\",\n      info: \"text-cyan-800 focus:ring-cyan-800 dark:ring-offset-gray-800 dark:focus:ring-cyan-800\",\n      light: \"text-gray-900 focus:ring-gray-900 dark:ring-offset-gray-900 dark:focus:ring-gray-900\",\n      purple: \"text-purple-600 focus:ring-purple-600 dark:ring-offset-purple-600 dark:focus:ring-purple-600\",\n      success: \"text-green-800 focus:ring-green-800 dark:ring-offset-green-800 dark:focus:ring-green-800\",\n      warning: \"text-yellow-400 focus:ring-yellow-400 dark:ring-offset-yellow-400 dark:focus:ring-yellow-400\",\n      blue: \"text-blue-700 focus:ring-blue-600 dark:ring-offset-blue-700 dark:focus:ring-blue-700\",\n      cyan: \"text-cyan-600 focus:ring-cyan-600 dark:ring-offset-cyan-600 dark:focus:ring-cyan-600\",\n      green: \"text-green-600 focus:ring-green-600 dark:ring-offset-green-600 dark:focus:ring-green-600\",\n      indigo: \"text-indigo-700 focus:ring-indigo-700 dark:ring-offset-indigo-700 dark:focus:ring-indigo-700\",\n      lime: \"text-lime-700 focus:ring-lime-700 dark:ring-offset-lime-700 dark:focus:ring-lime-700\",\n      pink: \"text-pink-600 focus:ring-pink-600 dark:ring-offset-pink-600 dark:focus:ring-pink-600\",\n      red: \"text-red-600 focus:ring-red-600 dark:ring-offset-red-600 dark:focus:ring-red-600\",\n      teal: \"text-teal-600 focus:ring-teal-600 dark:ring-offset-teal-600 dark:focus:ring-teal-600\",\n      yellow: \"text-yellow-400 focus:ring-yellow-400 dark:ring-offset-yellow-400 dark:focus:ring-yellow-400\",\n    },\n  },\n});\n"], "names": ["createTheme"], "mappings": ";;;;AAEY,MAAC,aAAa,GAAGA,uBAAW,CAAC;AACzC,EAAE,IAAI,EAAE;AACR,IAAI,IAAI,EAAE,uGAAuG;AACjH,IAAI,KAAK,EAAE;AACX,MAAM,OAAO,EAAE,sFAAsF;AACrG,MAAM,IAAI,EAAE,sFAAsF;AAClG,MAAM,OAAO,EAAE,kFAAkF;AACjG,MAAM,IAAI,EAAE,sFAAsF;AAClG,MAAM,IAAI,EAAE,sFAAsF;AAClG,MAAM,KAAK,EAAE,sFAAsF;AACnG,MAAM,MAAM,EAAE,8FAA8F;AAC5G,MAAM,OAAO,EAAE,0FAA0F;AACzG,MAAM,OAAO,EAAE,8FAA8F;AAC7G,MAAM,IAAI,EAAE,sFAAsF;AAClG,MAAM,IAAI,EAAE,sFAAsF;AAClG,MAAM,KAAK,EAAE,0FAA0F;AACvG,MAAM,MAAM,EAAE,8FAA8F;AAC5G,MAAM,IAAI,EAAE,sFAAsF;AAClG,MAAM,IAAI,EAAE,sFAAsF;AAClG,MAAM,GAAG,EAAE,kFAAkF;AAC7F,MAAM,IAAI,EAAE,sFAAsF;AAClG,MAAM,MAAM,EAAE,8FAA8F;AAC5G,KAAK;AACL,GAAG;AACH,CAAC;;;;"}