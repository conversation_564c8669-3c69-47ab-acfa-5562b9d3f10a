{"version": 3, "file": "theme.cjs", "sources": ["../../../../src/components/DarkThemeToggle/theme.ts"], "sourcesContent": ["import { createTheme } from \"../../helpers/create-theme\";\nimport type { FlowbiteDarkThemeToggleTheme } from \"./DarkThemeToggle\";\n\nexport const darkThemeToggleTheme: FlowbiteDarkThemeToggleTheme = createTheme({\n  root: {\n    base: \"rounded-lg p-2.5 text-sm text-gray-500 hover:bg-gray-100 focus:outline-none focus:ring-4 focus:ring-gray-200 dark:text-gray-400 dark:hover:bg-gray-700 dark:focus:ring-gray-700\",\n    icon: \"h-5 w-5\",\n  },\n});\n"], "names": ["createTheme"], "mappings": ";;;;AAEY,MAAC,oBAAoB,GAAGA,uBAAW,CAAC;AAChD,EAAE,IAAI,EAAE;AACR,IAAI,IAAI,EAAE,iLAAiL;AAC3L,IAAI,IAAI,EAAE,SAAS;AACnB,GAAG;AACH,CAAC;;;;"}