{"version": 3, "file": "Years.cjs", "sources": ["../../../../../src/components/Datepicker/Views/Years.tsx"], "sourcesContent": ["\"use client\";\n\nimport type { FC } from \"react\";\nimport { twMerge } from \"tailwind-merge\";\nimport { mergeDeep } from \"../../../helpers/merge-deep\";\nimport { useDatePickerContext } from \"../DatepickerContext\";\nimport { isDateEqual, isDateInRange, startOfYearPeriod, Views } from \"../helpers\";\n\nexport interface FlowbiteDatepickerViewsYearsTheme {\n  items: {\n    base: string;\n    item: {\n      base: string;\n      disabled: string;\n      selected: string;\n    };\n  };\n}\n\nexport interface DatepickerViewsYearsProps {\n  theme?: FlowbiteDatepickerViewsYearsTheme;\n}\n\nexport const DatepickerViewsYears: FC<DatepickerViewsYearsProps> = ({ theme: customTheme = {} }) => {\n  const { theme: rootTheme, selectedDate, minDate, maxDate, viewDate, setViewDate, setView } = useDatePickerContext();\n\n  const theme = mergeDeep(rootTheme.views.years, customTheme);\n\n  return (\n    <div className={theme.items.base}>\n      {[...Array(12)].map((_year, index) => {\n        const first = startOfYearPeriod(viewDate, 10);\n        const year = first + index;\n        const newDate = new Date(viewDate.getTime());\n        newDate.setFullYear(year);\n\n        const isSelected = selectedDate && isDateEqual(selectedDate, newDate);\n        const isDisabled = !isDateInRange(newDate, minDate, maxDate);\n\n        return (\n          <button\n            disabled={isDisabled}\n            key={index}\n            type=\"button\"\n            className={twMerge(\n              theme.items.item.base,\n              isSelected && theme.items.item.selected,\n              isDisabled && theme.items.item.disabled,\n            )}\n            onClick={() => {\n              if (isDisabled) return;\n\n              setViewDate(newDate);\n              setView(Views.Months);\n            }}\n          >\n            {year}\n          </button>\n        );\n      })}\n    </div>\n  );\n};\n"], "names": ["useDatePickerContext", "mergeDeep", "jsx", "startOfYearPeriod", "isDateEqual", "isDateInRange", "twMerge", "Views"], "mappings": ";;;;;;;;AAOY,MAAC,oBAAoB,GAAG,CAAC,EAAE,KAAK,EAAE,WAAW,GAAG,EAAE,EAAE,KAAK;AACrE,EAAE,MAAM,EAAE,KAAK,EAAE,SAAS,EAAE,YAAY,EAAE,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,WAAW,EAAE,OAAO,EAAE,GAAGA,sCAAoB,EAAE,CAAC;AACtH,EAAE,MAAM,KAAK,GAAGC,mBAAS,CAAC,SAAS,CAAC,KAAK,CAAC,KAAK,EAAE,WAAW,CAAC,CAAC;AAC9D,EAAE,uBAAuBC,cAAG,CAAC,KAAK,EAAE,EAAE,SAAS,EAAE,KAAK,CAAC,KAAK,CAAC,IAAI,EAAE,QAAQ,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,KAAK,KAAK;AAClH,IAAI,MAAM,KAAK,GAAGC,yBAAiB,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC;AAClD,IAAI,MAAM,IAAI,GAAG,KAAK,GAAG,KAAK,CAAC;AAC/B,IAAI,MAAM,OAAO,GAAG,IAAI,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC,CAAC;AACjD,IAAI,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;AAC9B,IAAI,MAAM,UAAU,GAAG,YAAY,IAAIC,mBAAW,CAAC,YAAY,EAAE,OAAO,CAAC,CAAC;AAC1E,IAAI,MAAM,UAAU,GAAG,CAACC,qBAAa,CAAC,OAAO,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;AACjE,IAAI,uBAAuBH,cAAG;AAC9B,MAAM,QAAQ;AACd,MAAM;AACN,QAAQ,QAAQ,EAAE,UAAU;AAC5B,QAAQ,IAAI,EAAE,QAAQ;AACtB,QAAQ,SAAS,EAAEI,qBAAO;AAC1B,UAAU,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI;AAC/B,UAAU,UAAU,IAAI,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ;AACjD,UAAU,UAAU,IAAI,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ;AACjD,SAAS;AACT,QAAQ,OAAO,EAAE,MAAM;AACvB,UAAU,IAAI,UAAU,EAAE,OAAO;AACjC,UAAU,WAAW,CAAC,OAAO,CAAC,CAAC;AAC/B,UAAU,OAAO,CAACC,aAAK,CAAC,MAAM,CAAC,CAAC;AAChC,SAAS;AACT,QAAQ,QAAQ,EAAE,IAAI;AACtB,OAAO;AACP,MAAM,KAAK;AACX,KAAK,CAAC;AACN,GAAG,CAAC,EAAE,CAAC,CAAC;AACR;;;;"}