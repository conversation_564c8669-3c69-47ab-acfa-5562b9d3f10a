{"version": 3, "file": "theme.cjs", "sources": ["../../../../src/components/Drawer/theme.ts"], "sourcesContent": ["import { createTheme } from \"../../helpers/create-theme\";\nimport type { FlowbiteDrawerTheme } from \"./Drawer\";\n\nexport const drawerTheme: FlowbiteDrawerTheme = createTheme({\n  root: {\n    base: \"fixed z-40 overflow-y-auto bg-white p-4 transition-transform dark:bg-gray-800\",\n    backdrop: \"fixed inset-0 z-30 bg-gray-900/50 dark:bg-gray-900/80\",\n    edge: \"bottom-16\",\n    position: {\n      top: {\n        on: \"left-0 right-0 top-0 w-full transform-none\",\n        off: \"left-0 right-0 top-0 w-full -translate-y-full\",\n      },\n      right: {\n        on: \"right-0 top-0 h-screen w-80 transform-none\",\n        off: \"right-0 top-0 h-screen w-80 translate-x-full\",\n      },\n      bottom: {\n        on: \"bottom-0 left-0 right-0 w-full transform-none\",\n        off: \"bottom-0 left-0 right-0 w-full translate-y-full\",\n      },\n      left: {\n        on: \"left-0 top-0 h-screen w-80 transform-none\",\n        off: \"left-0 top-0 h-screen w-80 -translate-x-full\",\n      },\n    },\n  },\n  header: {\n    inner: {\n      closeButton:\n        \"absolute end-2.5 top-2.5 flex h-8 w-8 items-center justify-center rounded-lg bg-transparent text-sm text-gray-400 hover:bg-gray-200 hover:text-gray-900 dark:hover:bg-gray-600 dark:hover:text-white\",\n      closeIcon: \"h-4 w-4\",\n      titleIcon: \"me-2.5 h-4 w-4\",\n      titleText: \"mb-4 inline-flex items-center text-base font-semibold text-gray-500 dark:text-gray-400\",\n    },\n    collapsed: {\n      on: \"hidden\",\n      off: \"block\",\n    },\n  },\n  items: {\n    base: \"\",\n  },\n});\n"], "names": ["createTheme"], "mappings": ";;;;AAEY,MAAC,WAAW,GAAGA,uBAAW,CAAC;AACvC,EAAE,IAAI,EAAE;AACR,IAAI,IAAI,EAAE,+EAA+E;AACzF,IAAI,QAAQ,EAAE,uDAAuD;AACrE,IAAI,IAAI,EAAE,WAAW;AACrB,IAAI,QAAQ,EAAE;AACd,MAAM,GAAG,EAAE;AACX,QAAQ,EAAE,EAAE,4CAA4C;AACxD,QAAQ,GAAG,EAAE,+CAA+C;AAC5D,OAAO;AACP,MAAM,KAAK,EAAE;AACb,QAAQ,EAAE,EAAE,4CAA4C;AACxD,QAAQ,GAAG,EAAE,8CAA8C;AAC3D,OAAO;AACP,MAAM,MAAM,EAAE;AACd,QAAQ,EAAE,EAAE,+CAA+C;AAC3D,QAAQ,GAAG,EAAE,iDAAiD;AAC9D,OAAO;AACP,MAAM,IAAI,EAAE;AACZ,QAAQ,EAAE,EAAE,2CAA2C;AACvD,QAAQ,GAAG,EAAE,8CAA8C;AAC3D,OAAO;AACP,KAAK;AACL,GAAG;AACH,EAAE,MAAM,EAAE;AACV,IAAI,KAAK,EAAE;AACX,MAAM,WAAW,EAAE,sMAAsM;AACzN,MAAM,SAAS,EAAE,SAAS;AAC1B,MAAM,SAAS,EAAE,gBAAgB;AACjC,MAAM,SAAS,EAAE,wFAAwF;AACzG,KAAK;AACL,IAAI,SAAS,EAAE;AACf,MAAM,EAAE,EAAE,QAAQ;AAClB,MAAM,GAAG,EAAE,OAAO;AAClB,KAAK;AACL,GAAG;AACH,EAAE,KAAK,EAAE;AACT,IAAI,IAAI,EAAE,EAAE;AACZ,GAAG;AACH,CAAC;;;;"}