{"version": 3, "file": "theme.cjs", "sources": ["../../../../src/components/FloatingLabel/theme.ts"], "sourcesContent": ["import { createTheme } from \"../../helpers/create-theme\";\n\nexport const floatingLabelTheme = createTheme({\n  input: {\n    default: {\n      filled: {\n        sm: \"peer block w-full appearance-none rounded-t-lg border-0 border-b-2 border-gray-300 bg-gray-50 px-2.5 pb-2.5 pt-5 text-xs text-gray-900 focus:border-blue-600 focus:outline-none focus:ring-0 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:focus:border-blue-500\",\n        md: \"peer block w-full appearance-none rounded-t-lg border-0 border-b-2 border-gray-300 bg-gray-50 px-2.5 pb-2.5 pt-5 text-sm text-gray-900 focus:border-blue-600 focus:outline-none focus:ring-0 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:focus:border-blue-500\",\n      },\n      outlined: {\n        sm: \"peer block w-full appearance-none rounded-lg border border-gray-300 bg-transparent px-2.5 pb-2.5 pt-4 text-xs text-gray-900 focus:border-blue-600 focus:outline-none focus:ring-0 dark:border-gray-600 dark:text-white dark:focus:border-blue-500\",\n        md: \"peer block w-full appearance-none rounded-lg border border-gray-300 bg-transparent px-2.5 pb-2.5 pt-4 text-sm text-gray-900 focus:border-blue-600 focus:outline-none focus:ring-0 dark:border-gray-600 dark:text-white dark:focus:border-blue-500\",\n      },\n      standard: {\n        sm: \"peer block w-full appearance-none border-0 border-b-2 border-gray-300 bg-transparent px-0 py-2.5 text-xs text-gray-900 focus:border-blue-600 focus:outline-none focus:ring-0 dark:border-gray-600 dark:text-white dark:focus:border-blue-500\",\n        md: \"peer block w-full appearance-none border-0 border-b-2 border-gray-300 bg-transparent px-0 py-2.5 text-sm text-gray-900 focus:border-blue-600 focus:outline-none focus:ring-0 dark:border-gray-600 dark:text-white dark:focus:border-blue-500\",\n      },\n    },\n    success: {\n      filled: {\n        sm: \"peer block w-full appearance-none rounded-t-lg border-0 border-b-2 border-green-600 bg-gray-50 px-2.5 pb-2.5 pt-5 text-xs text-gray-900 focus:border-green-600 focus:outline-none focus:ring-0 dark:border-green-500 dark:bg-gray-700 dark:text-white dark:focus:border-green-500\",\n        md: \"peer block w-full appearance-none rounded-t-lg border-0 border-b-2 border-green-600 bg-gray-50 px-2.5 pb-2.5 pt-5 text-sm text-gray-900 focus:border-green-600 focus:outline-none focus:ring-0 dark:border-green-500 dark:bg-gray-700 dark:text-white dark:focus:border-green-500\",\n      },\n      outlined: {\n        sm: \"peer block w-full appearance-none rounded-lg border border-green-600 bg-transparent px-2.5 pb-2.5 pt-4 text-xs text-gray-900 focus:border-green-600 focus:outline-none focus:ring-0 dark:border-green-500 dark:text-white dark:focus:border-green-500\",\n        md: \"peer block w-full appearance-none rounded-lg border border-green-600 bg-transparent px-2.5 pb-2.5 pt-4 text-sm text-gray-900 focus:border-green-600 focus:outline-none focus:ring-0 dark:border-green-500 dark:text-white dark:focus:border-green-500\",\n      },\n      standard: {\n        sm: \"peer block w-full appearance-none border-0 border-b-2 border-green-600 bg-transparent px-0 py-2.5 text-xs text-gray-900 focus:border-green-600 focus:outline-none focus:ring-0 dark:border-green-500 dark:text-white dark:focus:border-green-500\",\n        md: \"peer block w-full appearance-none border-0 border-b-2 border-green-600 bg-transparent px-0 py-2.5 text-sm text-gray-900 focus:border-green-600 focus:outline-none focus:ring-0 dark:border-green-500 dark:text-white dark:focus:border-green-500\",\n      },\n    },\n    error: {\n      filled: {\n        sm: \"peer block w-full appearance-none rounded-t-lg border-0 border-b-2 border-red-600 bg-gray-50 px-2.5 pb-2.5 pt-5 text-xs text-gray-900 focus:border-red-600 focus:outline-none focus:ring-0 dark:border-red-500 dark:bg-gray-700 dark:text-white dark:focus:border-red-500\",\n        md: \"peer block w-full appearance-none rounded-t-lg border-0 border-b-2 border-red-600 bg-gray-50 px-2.5 pb-2.5 pt-5 text-sm text-gray-900 focus:border-red-600 focus:outline-none focus:ring-0 dark:border-red-500 dark:bg-gray-700 dark:text-white dark:focus:border-red-500\",\n      },\n      outlined: {\n        sm: \"peer block w-full appearance-none rounded-lg border border-red-600 bg-transparent px-2.5 pb-2.5 pt-4 text-xs text-gray-900 focus:border-red-600 focus:outline-none focus:ring-0 dark:border-red-500 dark:text-white dark:focus:border-red-500\",\n        md: \"peer block w-full appearance-none rounded-lg border border-red-600 bg-transparent px-2.5 pb-2.5 pt-4 text-sm text-gray-900 focus:border-red-600 focus:outline-none focus:ring-0 dark:border-red-500 dark:text-white dark:focus:border-red-500\",\n      },\n      standard: {\n        sm: \"peer block w-full appearance-none border-0 border-b-2 border-red-600 bg-transparent px-0 py-2.5 text-xs text-gray-900 focus:border-red-600 focus:outline-none focus:ring-0 dark:border-red-500 dark:text-white dark:focus:border-red-500\",\n        md: \"peer block w-full appearance-none border-0 border-b-2 border-red-600 bg-transparent px-0 py-2.5 text-sm text-gray-900 focus:border-red-600 focus:outline-none focus:ring-0 dark:border-red-500 dark:text-white dark:focus:border-red-500\",\n      },\n    },\n  },\n  label: {\n    default: {\n      filled: {\n        sm: \"absolute left-2.5 top-4 z-10 origin-[0] -translate-y-4 scale-75 text-xs text-gray-500 transition-transform duration-300 peer-placeholder-shown:translate-y-0 peer-placeholder-shown:scale-100 peer-focus:-translate-y-4 peer-focus:scale-75 peer-focus:text-blue-600 dark:text-gray-400 peer-focus:dark:text-blue-500\",\n        md: \"absolute left-2.5 top-4 z-10 origin-[0] -translate-y-4 scale-75 text-sm text-gray-500 transition-transform duration-300 peer-placeholder-shown:translate-y-0 peer-placeholder-shown:scale-100 peer-focus:-translate-y-4 peer-focus:scale-75 peer-focus:text-blue-600 dark:text-gray-400 peer-focus:dark:text-blue-500\",\n      },\n      outlined: {\n        sm: \"absolute left-1 top-2 z-10 origin-[0] -translate-y-4 scale-75 bg-white px-2 text-xs text-gray-500 transition-transform duration-300 peer-placeholder-shown:top-1/2 peer-placeholder-shown:-translate-y-1/2 peer-placeholder-shown:scale-100 peer-focus:top-2 peer-focus:-translate-y-4 peer-focus:scale-75 peer-focus:px-2 peer-focus:text-blue-600 dark:bg-gray-900 dark:text-gray-400 peer-focus:dark:text-blue-500\",\n        md: \"absolute left-1 top-2 z-10 origin-[0] -translate-y-4 scale-75 bg-white px-2 text-sm text-gray-500 transition-transform duration-300 peer-placeholder-shown:top-1/2 peer-placeholder-shown:-translate-y-1/2 peer-placeholder-shown:scale-100 peer-focus:top-2 peer-focus:-translate-y-4 peer-focus:scale-75 peer-focus:px-2 peer-focus:text-blue-600 dark:bg-gray-900 dark:text-gray-400 peer-focus:dark:text-blue-500\",\n      },\n      standard: {\n        sm: \"absolute top-3 -z-10 origin-[0] -translate-y-6 scale-75 text-xs text-gray-500 transition-transform duration-300 peer-placeholder-shown:translate-y-0 peer-placeholder-shown:scale-100 peer-focus:left-0 peer-focus:-translate-y-6 peer-focus:scale-75 peer-focus:text-blue-600 dark:text-gray-400 peer-focus:dark:text-blue-500\",\n        md: \"absolute top-3 -z-10 origin-[0] -translate-y-6 scale-75 text-sm text-gray-500 transition-transform duration-300 peer-placeholder-shown:translate-y-0 peer-placeholder-shown:scale-100 peer-focus:left-0 peer-focus:-translate-y-6 peer-focus:scale-75 peer-focus:text-blue-600 dark:text-gray-400 peer-focus:dark:text-blue-500\",\n      },\n    },\n    success: {\n      filled: {\n        sm: \"absolute left-2.5 top-4 z-10 origin-[0] -translate-y-4 scale-75 text-sm text-green-600 transition-transform duration-300 peer-placeholder-shown:translate-y-0 peer-placeholder-shown:scale-100 peer-focus:-translate-y-4 peer-focus:scale-75 dark:text-green-500\",\n        md: \"absolute left-2.5 top-4 z-10 origin-[0] -translate-y-4 scale-75 text-sm text-green-600 transition-transform duration-300 peer-placeholder-shown:translate-y-0 peer-placeholder-shown:scale-100 peer-focus:-translate-y-4 peer-focus:scale-75 dark:text-green-500\",\n      },\n      outlined: {\n        sm: \"absolute left-1 top-2 z-10 origin-[0] -translate-y-4 scale-75 bg-white px-2 text-sm text-green-600 transition-transform duration-300 peer-placeholder-shown:top-1/2 peer-placeholder-shown:-translate-y-1/2 peer-placeholder-shown:scale-100 peer-focus:top-2 peer-focus:-translate-y-4 peer-focus:scale-75 peer-focus:px-2 dark:bg-gray-900 dark:text-green-500\",\n        md: \"absolute left-1 top-2 z-10 origin-[0] -translate-y-4 scale-75 bg-white px-2 text-sm text-green-600 transition-transform duration-300 peer-placeholder-shown:top-1/2 peer-placeholder-shown:-translate-y-1/2 peer-placeholder-shown:scale-100 peer-focus:top-2 peer-focus:-translate-y-4 peer-focus:scale-75 peer-focus:px-2 dark:bg-gray-900 dark:text-green-500\",\n      },\n      standard: {\n        sm: \"absolute top-3 -z-10 origin-[0] -translate-y-6 scale-75 text-xs text-green-600 transition-transform duration-300 peer-placeholder-shown:translate-y-0 peer-placeholder-shown:scale-100 peer-focus:left-0 peer-focus:-translate-y-6 peer-focus:scale-75 dark:text-green-500\",\n        md: \"absolute top-3 -z-10 origin-[0] -translate-y-6 scale-75 text-sm text-green-600 transition-transform duration-300 peer-placeholder-shown:translate-y-0 peer-placeholder-shown:scale-100 peer-focus:left-0 peer-focus:-translate-y-6 peer-focus:scale-75 dark:text-green-500\",\n      },\n    },\n    error: {\n      filled: {\n        sm: \"absolute left-2.5 top-4 z-10 origin-[0] -translate-y-4 scale-75 text-xs text-red-600 transition-transform duration-300 peer-placeholder-shown:translate-y-0 peer-placeholder-shown:scale-100 peer-focus:-translate-y-4 peer-focus:scale-75 dark:text-red-500\",\n        md: \"absolute left-2.5 top-4 z-10 origin-[0] -translate-y-4 scale-75 text-xs text-red-600 transition-transform duration-300 peer-placeholder-shown:translate-y-0 peer-placeholder-shown:scale-100 peer-focus:-translate-y-4 peer-focus:scale-75 dark:text-red-500\",\n      },\n      outlined: {\n        sm: \"absolute left-1 top-2 z-10 origin-[0] -translate-y-4 scale-75 bg-white px-2 text-xs text-red-600 transition-transform duration-300 peer-placeholder-shown:top-1/2 peer-placeholder-shown:-translate-y-1/2 peer-placeholder-shown:scale-100 peer-focus:top-2 peer-focus:-translate-y-4 peer-focus:scale-75 peer-focus:px-2 dark:bg-gray-900 dark:text-red-500\",\n        md: \"absolute left-1 top-2 z-10 origin-[0] -translate-y-4 scale-75 bg-white px-2 text-xs text-red-600 transition-transform duration-300 peer-placeholder-shown:top-1/2 peer-placeholder-shown:-translate-y-1/2 peer-placeholder-shown:scale-100 peer-focus:top-2 peer-focus:-translate-y-4 peer-focus:scale-75 peer-focus:px-2 dark:bg-gray-900 dark:text-red-500\",\n      },\n      standard: {\n        sm: \"absolute top-3 -z-10 origin-[0] -translate-y-6 scale-75 text-xs text-red-600 transition-transform duration-300 peer-placeholder-shown:translate-y-0 peer-placeholder-shown:scale-100 peer-focus:left-0 peer-focus:-translate-y-6 peer-focus:scale-75 dark:text-red-500\",\n        md: \"absolute top-3 -z-10 origin-[0] -translate-y-6 scale-75 text-sm text-red-600 transition-transform duration-300 peer-placeholder-shown:translate-y-0 peer-placeholder-shown:scale-100 peer-focus:left-0 peer-focus:-translate-y-6 peer-focus:scale-75 dark:text-red-500\",\n      },\n    },\n  },\n  helperText: {\n    default: \"mt-2 text-xs text-gray-600 dark:text-gray-400\",\n    success: \"mt-2 text-xs text-green-600 dark:text-green-400\",\n    error: \"mt-2 text-xs text-red-600 dark:text-red-400\",\n  },\n});\n\nexport type FlowbiteFloatingLabelTheme = typeof floatingLabelTheme;\n"], "names": ["createTheme"], "mappings": ";;;;AAEY,MAAC,kBAAkB,GAAGA,uBAAW,CAAC;AAC9C,EAAE,KAAK,EAAE;AACT,IAAI,OAAO,EAAE;AACb,MAAM,MAAM,EAAE;AACd,QAAQ,EAAE,EAAE,+QAA+Q;AAC3R,QAAQ,EAAE,EAAE,+QAA+Q;AAC3R,OAAO;AACP,MAAM,QAAQ,EAAE;AAChB,QAAQ,EAAE,EAAE,mPAAmP;AAC/P,QAAQ,EAAE,EAAE,mPAAmP;AAC/P,OAAO;AACP,MAAM,QAAQ,EAAE;AAChB,QAAQ,EAAE,EAAE,8OAA8O;AAC1P,QAAQ,EAAE,EAAE,8OAA8O;AAC1P,OAAO;AACP,KAAK;AACL,IAAI,OAAO,EAAE;AACb,MAAM,MAAM,EAAE;AACd,QAAQ,EAAE,EAAE,mRAAmR;AAC/R,QAAQ,EAAE,EAAE,mRAAmR;AAC/R,OAAO;AACP,MAAM,QAAQ,EAAE;AAChB,QAAQ,EAAE,EAAE,uPAAuP;AACnQ,QAAQ,EAAE,EAAE,uPAAuP;AACnQ,OAAO;AACP,MAAM,QAAQ,EAAE;AAChB,QAAQ,EAAE,EAAE,kPAAkP;AAC9P,QAAQ,EAAE,EAAE,kPAAkP;AAC9P,OAAO;AACP,KAAK;AACL,IAAI,KAAK,EAAE;AACX,MAAM,MAAM,EAAE;AACd,QAAQ,EAAE,EAAE,2QAA2Q;AACvR,QAAQ,EAAE,EAAE,2QAA2Q;AACvR,OAAO;AACP,MAAM,QAAQ,EAAE;AAChB,QAAQ,EAAE,EAAE,+OAA+O;AAC3P,QAAQ,EAAE,EAAE,+OAA+O;AAC3P,OAAO;AACP,MAAM,QAAQ,EAAE;AAChB,QAAQ,EAAE,EAAE,0OAA0O;AACtP,QAAQ,EAAE,EAAE,0OAA0O;AACtP,OAAO;AACP,KAAK;AACL,GAAG;AACH,EAAE,KAAK,EAAE;AACT,IAAI,OAAO,EAAE;AACb,MAAM,MAAM,EAAE;AACd,QAAQ,EAAE,EAAE,uTAAuT;AACnU,QAAQ,EAAE,EAAE,uTAAuT;AACnU,OAAO;AACP,MAAM,QAAQ,EAAE;AAChB,QAAQ,EAAE,EAAE,uZAAuZ;AACna,QAAQ,EAAE,EAAE,uZAAuZ;AACna,OAAO;AACP,MAAM,QAAQ,EAAE;AAChB,QAAQ,EAAE,EAAE,iUAAiU;AAC7U,QAAQ,EAAE,EAAE,iUAAiU;AAC7U,OAAO;AACP,KAAK;AACL,IAAI,OAAO,EAAE;AACb,MAAM,MAAM,EAAE;AACd,QAAQ,EAAE,EAAE,kQAAkQ;AAC9Q,QAAQ,EAAE,EAAE,kQAAkQ;AAC9Q,OAAO;AACP,MAAM,QAAQ,EAAE;AAChB,QAAQ,EAAE,EAAE,kWAAkW;AAC9W,QAAQ,EAAE,EAAE,kWAAkW;AAC9W,OAAO;AACP,MAAM,QAAQ,EAAE;AAChB,QAAQ,EAAE,EAAE,4QAA4Q;AACxR,QAAQ,EAAE,EAAE,4QAA4Q;AACxR,OAAO;AACP,KAAK;AACL,IAAI,KAAK,EAAE;AACX,MAAM,MAAM,EAAE;AACd,QAAQ,EAAE,EAAE,8PAA8P;AAC1Q,QAAQ,EAAE,EAAE,8PAA8P;AAC1Q,OAAO;AACP,MAAM,QAAQ,EAAE;AAChB,QAAQ,EAAE,EAAE,8VAA8V;AAC1W,QAAQ,EAAE,EAAE,8VAA8V;AAC1W,OAAO;AACP,MAAM,QAAQ,EAAE;AAChB,QAAQ,EAAE,EAAE,wQAAwQ;AACpR,QAAQ,EAAE,EAAE,wQAAwQ;AACpR,OAAO;AACP,KAAK;AACL,GAAG;AACH,EAAE,UAAU,EAAE;AACd,IAAI,OAAO,EAAE,+CAA+C;AAC5D,IAAI,OAAO,EAAE,iDAAiD;AAC9D,IAAI,KAAK,EAAE,6CAA6C;AACxD,GAAG;AACH,CAAC;;;;"}