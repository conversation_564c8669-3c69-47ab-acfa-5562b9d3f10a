{"version": 3, "file": "theme.cjs", "sources": ["../../../../src/components/Kbd/theme.ts"], "sourcesContent": ["import { createTheme } from \"../../helpers/create-theme\";\nimport type { FlowbiteKbdTheme } from \"./Kbd\";\n\nexport const kbdTheme: FlowbiteKbdTheme = createTheme({\n  root: {\n    base: \"rounded-lg border border-gray-200 bg-gray-100 px-2 py-1.5 text-xs font-semibold text-gray-800 dark:border-gray-500 dark:bg-gray-600 dark:text-gray-100\",\n    icon: \"inline-block\",\n  },\n});\n"], "names": ["createTheme"], "mappings": ";;;;AAEY,MAAC,QAAQ,GAAGA,uBAAW,CAAC;AACpC,EAAE,IAAI,EAAE;AACR,IAAI,IAAI,EAAE,wJAAwJ;AAClK,IAAI,IAAI,EAAE,cAAc;AACxB,GAAG;AACH,CAAC;;;;"}