{"version": 3, "file": "theme.cjs", "sources": ["../../../../src/components/List/theme.ts"], "sourcesContent": ["import { createTheme } from \"../../helpers/create-theme\";\nimport type { FlowbiteListTheme } from \"./List\";\n\nexport const listTheme: FlowbiteListTheme = createTheme({\n  root: {\n    base: \"list-inside space-y-1 text-gray-500 dark:text-gray-400\",\n    ordered: {\n      off: \"list-disc\",\n      on: \"list-decimal\",\n    },\n    horizontal: \"flex list-none flex-wrap items-center justify-center space-x-4 space-y-0\",\n    unstyled: \"list-none\",\n    nested: \"mt-2 ps-5\",\n  },\n  item: {\n    withIcon: {\n      off: \"\",\n      on: \"flex items-center\",\n    },\n    icon: \"me-2 h-3.5 w-3.5 flex-shrink-0\",\n  },\n});\n"], "names": ["createTheme"], "mappings": ";;;;AAEY,MAAC,SAAS,GAAGA,uBAAW,CAAC;AACrC,EAAE,IAAI,EAAE;AACR,IAAI,IAAI,EAAE,wDAAwD;AAClE,IAAI,OAAO,EAAE;AACb,MAAM,GAAG,EAAE,WAAW;AACtB,MAAM,EAAE,EAAE,cAAc;AACxB,KAAK;AACL,IAAI,UAAU,EAAE,0EAA0E;AAC1F,IAAI,QAAQ,EAAE,WAAW;AACzB,IAAI,MAAM,EAAE,WAAW;AACvB,GAAG;AACH,EAAE,IAAI,EAAE;AACR,IAAI,QAAQ,EAAE;AACd,MAAM,GAAG,EAAE,EAAE;AACb,MAAM,EAAE,EAAE,mBAAmB;AAC7B,KAAK;AACL,IAAI,IAAI,EAAE,gCAAgC;AAC1C,GAAG;AACH,CAAC;;;;"}