{"version": 3, "file": "theme.cjs", "sources": ["../../../../src/components/MegaMenu/theme.ts"], "sourcesContent": ["import { twMerge } from \"tailwind-merge\";\nimport { createTheme } from \"../../helpers/create-theme\";\nimport { dropdownTheme } from \"../Dropdown/theme\";\nimport { navbarTheme } from \"../Navbar/theme\";\nimport type { FlowbiteMegaMenuTheme } from \"./MegaMenu\";\n\nexport const megaMenuTheme: FlowbiteMegaMenuTheme = createTheme({\n  ...navbarTheme,\n  dropdown: {\n    base: \"\",\n    toggle: {\n      ...dropdownTheme,\n      floating: {\n        ...dropdownTheme.floating,\n        base: twMerge(dropdownTheme.floating.base, \"mt-2 block\"),\n        content: twMerge(dropdownTheme.floating.content, \"text-gray-500 dark:text-gray-400\"),\n        style: {\n          ...dropdownTheme.floating.style,\n          auto: twMerge(dropdownTheme.floating.style.auto, \"text-gray-500 dark:text-gray-400\"),\n        },\n      },\n      inlineWrapper: twMerge(dropdownTheme.inlineWrapper, \"flex w-full items-center justify-between\"),\n    },\n  },\n  dropdownToggle: {\n    base: twMerge(navbarTheme.link.base, navbarTheme.link.active.off, \"flex w-full items-center justify-between\"),\n  },\n});\n"], "names": ["createTheme", "navbarTheme", "dropdownTheme", "twMerge"], "mappings": ";;;;;;;AAKY,MAAC,aAAa,GAAGA,uBAAW,CAAC;AACzC,EAAE,GAAGC,iBAAW;AAChB,EAAE,QAAQ,EAAE;AACZ,IAAI,IAAI,EAAE,EAAE;AACZ,IAAI,MAAM,EAAE;AACZ,MAAM,GAAGC,qBAAa;AACtB,MAAM,QAAQ,EAAE;AAChB,QAAQ,GAAGA,qBAAa,CAAC,QAAQ;AACjC,QAAQ,IAAI,EAAEC,qBAAO,CAACD,qBAAa,CAAC,QAAQ,CAAC,IAAI,EAAE,YAAY,CAAC;AAChE,QAAQ,OAAO,EAAEC,qBAAO,CAACD,qBAAa,CAAC,QAAQ,CAAC,OAAO,EAAE,kCAAkC,CAAC;AAC5F,QAAQ,KAAK,EAAE;AACf,UAAU,GAAGA,qBAAa,CAAC,QAAQ,CAAC,KAAK;AACzC,UAAU,IAAI,EAAEC,qBAAO,CAACD,qBAAa,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,kCAAkC,CAAC;AAC9F,SAAS;AACT,OAAO;AACP,MAAM,aAAa,EAAEC,qBAAO,CAACD,qBAAa,CAAC,aAAa,EAAE,0CAA0C,CAAC;AACrG,KAAK;AACL,GAAG;AACH,EAAE,cAAc,EAAE;AAClB,IAAI,IAAI,EAAEC,qBAAO,CAACF,iBAAW,CAAC,IAAI,CAAC,IAAI,EAAEA,iBAAW,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE,0CAA0C,CAAC;AACjH,GAAG;AACH,CAAC;;;;"}