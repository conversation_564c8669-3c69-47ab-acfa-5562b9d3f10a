{"version": 3, "file": "theme.cjs", "sources": ["../../../../src/components/Modal/theme.ts"], "sourcesContent": ["import { createTheme } from \"../../helpers/create-theme\";\nimport type { FlowbiteModalTheme } from \"./Modal\";\n\nexport const modalTheme: FlowbiteModalTheme = createTheme({\n  root: {\n    base: \"fixed inset-x-0 top-0 z-50 h-screen overflow-y-auto overflow-x-hidden md:inset-0 md:h-full\",\n    show: {\n      on: \"flex bg-gray-900 bg-opacity-50 dark:bg-opacity-80\",\n      off: \"hidden\",\n    },\n    sizes: {\n      sm: \"max-w-sm\",\n      md: \"max-w-md\",\n      lg: \"max-w-lg\",\n      xl: \"max-w-xl\",\n      \"2xl\": \"max-w-2xl\",\n      \"3xl\": \"max-w-3xl\",\n      \"4xl\": \"max-w-4xl\",\n      \"5xl\": \"max-w-5xl\",\n      \"6xl\": \"max-w-6xl\",\n      \"7xl\": \"max-w-7xl\",\n    },\n    positions: {\n      \"top-left\": \"items-start justify-start\",\n      \"top-center\": \"items-start justify-center\",\n      \"top-right\": \"items-start justify-end\",\n      \"center-left\": \"items-center justify-start\",\n      center: \"items-center justify-center\",\n      \"center-right\": \"items-center justify-end\",\n      \"bottom-right\": \"items-end justify-end\",\n      \"bottom-center\": \"items-end justify-center\",\n      \"bottom-left\": \"items-end justify-start\",\n    },\n  },\n  content: {\n    base: \"relative h-full w-full p-4 md:h-auto\",\n    inner: \"relative flex max-h-[90dvh] flex-col rounded-lg bg-white shadow dark:bg-gray-700\",\n  },\n  body: {\n    base: \"flex-1 overflow-auto p-6\",\n    popup: \"pt-0\",\n  },\n  header: {\n    base: \"flex items-start justify-between rounded-t border-b p-5 dark:border-gray-600\",\n    popup: \"border-b-0 p-2\",\n    title: \"text-xl font-medium text-gray-900 dark:text-white\",\n    close: {\n      base: \"ml-auto inline-flex items-center rounded-lg bg-transparent p-1.5 text-sm text-gray-400 hover:bg-gray-200 hover:text-gray-900 dark:hover:bg-gray-600 dark:hover:text-white\",\n      icon: \"h-5 w-5\",\n    },\n  },\n  footer: {\n    base: \"flex items-center space-x-2 rounded-b border-gray-200 p-6 dark:border-gray-600\",\n    popup: \"border-t\",\n  },\n});\n"], "names": ["createTheme"], "mappings": ";;;;AAEY,MAAC,UAAU,GAAGA,uBAAW,CAAC;AACtC,EAAE,IAAI,EAAE;AACR,IAAI,IAAI,EAAE,4FAA4F;AACtG,IAAI,IAAI,EAAE;AACV,MAAM,EAAE,EAAE,mDAAmD;AAC7D,MAAM,GAAG,EAAE,QAAQ;AACnB,KAAK;AACL,IAAI,KAAK,EAAE;AACX,MAAM,EAAE,EAAE,UAAU;AACpB,MAAM,EAAE,EAAE,UAAU;AACpB,MAAM,EAAE,EAAE,UAAU;AACpB,MAAM,EAAE,EAAE,UAAU;AACpB,MAAM,KAAK,EAAE,WAAW;AACxB,MAAM,KAAK,EAAE,WAAW;AACxB,MAAM,KAAK,EAAE,WAAW;AACxB,MAAM,KAAK,EAAE,WAAW;AACxB,MAAM,KAAK,EAAE,WAAW;AACxB,MAAM,KAAK,EAAE,WAAW;AACxB,KAAK;AACL,IAAI,SAAS,EAAE;AACf,MAAM,UAAU,EAAE,2BAA2B;AAC7C,MAAM,YAAY,EAAE,4BAA4B;AAChD,MAAM,WAAW,EAAE,yBAAyB;AAC5C,MAAM,aAAa,EAAE,4BAA4B;AACjD,MAAM,MAAM,EAAE,6BAA6B;AAC3C,MAAM,cAAc,EAAE,0BAA0B;AAChD,MAAM,cAAc,EAAE,uBAAuB;AAC7C,MAAM,eAAe,EAAE,0BAA0B;AACjD,MAAM,aAAa,EAAE,yBAAyB;AAC9C,KAAK;AACL,GAAG;AACH,EAAE,OAAO,EAAE;AACX,IAAI,IAAI,EAAE,sCAAsC;AAChD,IAAI,KAAK,EAAE,kFAAkF;AAC7F,GAAG;AACH,EAAE,IAAI,EAAE;AACR,IAAI,IAAI,EAAE,0BAA0B;AACpC,IAAI,KAAK,EAAE,MAAM;AACjB,GAAG;AACH,EAAE,MAAM,EAAE;AACV,IAAI,IAAI,EAAE,8EAA8E;AACxF,IAAI,KAAK,EAAE,gBAAgB;AAC3B,IAAI,KAAK,EAAE,mDAAmD;AAC9D,IAAI,KAAK,EAAE;AACX,MAAM,IAAI,EAAE,2KAA2K;AACvL,MAAM,IAAI,EAAE,SAAS;AACrB,KAAK;AACL,GAAG;AACH,EAAE,MAAM,EAAE;AACV,IAAI,IAAI,EAAE,gFAAgF;AAC1F,IAAI,KAAK,EAAE,UAAU;AACrB,GAAG;AACH,CAAC;;;;"}