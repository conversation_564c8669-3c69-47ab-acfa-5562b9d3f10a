{"version": 3, "file": "theme.cjs", "sources": ["../../../../src/components/Pagination/theme.ts"], "sourcesContent": ["import { createTheme } from \"../../helpers/create-theme\";\nimport type { FlowbitePaginationTheme } from \"./Pagination\";\n\nexport const paginationTheme: FlowbitePaginationTheme = createTheme({\n  base: \"\",\n  layout: {\n    table: {\n      base: \"text-sm text-gray-700 dark:text-gray-400\",\n      span: \"font-semibold text-gray-900 dark:text-white\",\n    },\n  },\n  pages: {\n    base: \"xs:mt-0 mt-2 inline-flex items-center -space-x-px\",\n    showIcon: \"inline-flex\",\n    previous: {\n      base: \"ml-0 rounded-l-lg border border-gray-300 bg-white px-3 py-2 leading-tight text-gray-500 enabled:hover:bg-gray-100 enabled:hover:text-gray-700 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-400 enabled:dark:hover:bg-gray-700 enabled:dark:hover:text-white\",\n      icon: \"h-5 w-5\",\n    },\n    next: {\n      base: \"rounded-r-lg border border-gray-300 bg-white px-3 py-2 leading-tight text-gray-500 enabled:hover:bg-gray-100 enabled:hover:text-gray-700 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-400 enabled:dark:hover:bg-gray-700 enabled:dark:hover:text-white\",\n      icon: \"h-5 w-5\",\n    },\n    selector: {\n      base: \"w-12 border border-gray-300 bg-white py-2 leading-tight text-gray-500 enabled:hover:bg-gray-100 enabled:hover:text-gray-700 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-400 enabled:dark:hover:bg-gray-700 enabled:dark:hover:text-white\",\n      active:\n        \"bg-cyan-50 text-cyan-600 hover:bg-cyan-100 hover:text-cyan-700 dark:border-gray-700 dark:bg-gray-700 dark:text-white\",\n      disabled: \"cursor-not-allowed opacity-50\",\n    },\n  },\n});\n"], "names": ["createTheme"], "mappings": ";;;;AAEY,MAAC,eAAe,GAAGA,uBAAW,CAAC;AAC3C,EAAE,IAAI,EAAE,EAAE;AACV,EAAE,MAAM,EAAE;AACV,IAAI,KAAK,EAAE;AACX,MAAM,IAAI,EAAE,0CAA0C;AACtD,MAAM,IAAI,EAAE,6CAA6C;AACzD,KAAK;AACL,GAAG;AACH,EAAE,KAAK,EAAE;AACT,IAAI,IAAI,EAAE,mDAAmD;AAC7D,IAAI,QAAQ,EAAE,aAAa;AAC3B,IAAI,QAAQ,EAAE;AACd,MAAM,IAAI,EAAE,qQAAqQ;AACjR,MAAM,IAAI,EAAE,SAAS;AACrB,KAAK;AACL,IAAI,IAAI,EAAE;AACV,MAAM,IAAI,EAAE,gQAAgQ;AAC5Q,MAAM,IAAI,EAAE,SAAS;AACrB,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,IAAI,EAAE,mPAAmP;AAC/P,MAAM,MAAM,EAAE,sHAAsH;AACpI,MAAM,QAAQ,EAAE,+BAA+B;AAC/C,KAAK;AACL,GAAG;AACH,CAAC;;;;"}