{"version": 3, "file": "theme.cjs", "sources": ["../../../../src/components/Progress/theme.ts"], "sourcesContent": ["import { createTheme } from \"../../helpers/create-theme\";\nimport type { FlowbiteProgressTheme } from \"./Progress\";\n\nexport const progressTheme: FlowbiteProgressTheme = createTheme({\n  base: \"w-full overflow-hidden rounded-full bg-gray-200 dark:bg-gray-700\",\n  label: \"mb-1 flex justify-between font-medium dark:text-white\",\n  bar: \"space-x-2 rounded-full text-center font-medium leading-none text-cyan-300 dark:text-cyan-100\",\n  color: {\n    dark: \"bg-gray-600 dark:bg-gray-300\",\n    blue: \"bg-blue-600\",\n    red: \"bg-red-600 dark:bg-red-500\",\n    green: \"bg-green-600 dark:bg-green-500\",\n    yellow: \"bg-yellow-400\",\n    indigo: \"bg-indigo-600 dark:bg-indigo-500\",\n    purple: \"bg-purple-600 dark:bg-purple-500\",\n    cyan: \"bg-cyan-600\",\n    gray: \"bg-gray-500\",\n    lime: \"bg-lime-600\",\n    pink: \"bg-pink-500\",\n    teal: \"bg-teal-600\",\n  },\n  size: {\n    sm: \"h-1.5\",\n    md: \"h-2.5\",\n    lg: \"h-4\",\n    xl: \"h-6\",\n  },\n});\n"], "names": ["createTheme"], "mappings": ";;;;AAEY,MAAC,aAAa,GAAGA,uBAAW,CAAC;AACzC,EAAE,IAAI,EAAE,kEAAkE;AAC1E,EAAE,KAAK,EAAE,uDAAuD;AAChE,EAAE,GAAG,EAAE,8FAA8F;AACrG,EAAE,KAAK,EAAE;AACT,IAAI,IAAI,EAAE,8BAA8B;AACxC,IAAI,IAAI,EAAE,aAAa;AACvB,IAAI,GAAG,EAAE,4BAA4B;AACrC,IAAI,KAAK,EAAE,gCAAgC;AAC3C,IAAI,MAAM,EAAE,eAAe;AAC3B,IAAI,MAAM,EAAE,kCAAkC;AAC9C,IAAI,MAAM,EAAE,kCAAkC;AAC9C,IAAI,IAAI,EAAE,aAAa;AACvB,IAAI,IAAI,EAAE,aAAa;AACvB,IAAI,IAAI,EAAE,aAAa;AACvB,IAAI,IAAI,EAAE,aAAa;AACvB,IAAI,IAAI,EAAE,aAAa;AACvB,GAAG;AACH,EAAE,IAAI,EAAE;AACR,IAAI,EAAE,EAAE,OAAO;AACf,IAAI,EAAE,EAAE,OAAO;AACf,IAAI,EAAE,EAAE,KAAK;AACb,IAAI,EAAE,EAAE,KAAK;AACb,GAAG;AACH,CAAC;;;;"}