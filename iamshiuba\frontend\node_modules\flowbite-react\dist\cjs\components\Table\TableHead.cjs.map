{"version": 3, "file": "TableHead.cjs", "sources": ["../../../../src/components/Table/TableHead.tsx"], "sourcesContent": ["\"use client\";\n\nimport { forwardRef, type ComponentPropsWithRef } from \"react\";\nimport { twMerge } from \"tailwind-merge\";\nimport { mergeDeep } from \"../../helpers/merge-deep\";\nimport type { DeepPartial } from \"../../types\";\nimport { useTableContext } from \"./TableContext\";\nimport type { FlowbiteTableHeadCellTheme } from \"./TableHeadCell\";\nimport { TableHeadContext } from \"./TableHeadContext\";\n\nexport interface FlowbiteTableHeadTheme {\n  base: string;\n  cell: FlowbiteTableHeadCellTheme;\n}\n\nexport interface TableHeadProps extends ComponentPropsWithRef<\"thead\"> {\n  theme?: DeepPartial<FlowbiteTableHeadTheme>;\n}\n\nexport const TableHead = forwardRef<HTMLTableSectionElement, TableHeadProps>(\n  ({ children, className, theme: customTheme = {}, ...props }, ref) => {\n    const { theme: rootTheme } = useTableContext();\n\n    const theme = mergeDeep(rootTheme.head, customTheme);\n\n    return (\n      <TableHeadContext.Provider value={{ theme }}>\n        <thead className={twMerge(theme.base, className)} ref={ref} {...props}>\n          <tr>{children}</tr>\n        </thead>\n      </TableHeadContext.Provider>\n    );\n  },\n);\n\nTableHead.displayName = \"Table.Head\";\n"], "names": ["forwardRef", "useTableContext", "mergeDeep", "jsx", "TableHeadContext", "twMerge"], "mappings": ";;;;;;;;;AAQY,MAAC,SAAS,GAAGA,gBAAU;AACnC,EAAE,CAAC,EAAE,QAAQ,EAAE,SAAS,EAAE,KAAK,EAAE,WAAW,GAAG,EAAE,EAAE,GAAG,KAAK,EAAE,EAAE,GAAG,KAAK;AACvE,IAAI,MAAM,EAAE,KAAK,EAAE,SAAS,EAAE,GAAGC,4BAAe,EAAE,CAAC;AACnD,IAAI,MAAM,KAAK,GAAGC,mBAAS,CAAC,SAAS,CAAC,IAAI,EAAE,WAAW,CAAC,CAAC;AACzD,IAAI,uBAAuBC,cAAG,CAACC,iCAAgB,CAAC,QAAQ,EAAE,EAAE,KAAK,EAAE,EAAE,KAAK,EAAE,EAAE,QAAQ,kBAAkBD,cAAG,CAAC,OAAO,EAAE,EAAE,SAAS,EAAEE,qBAAO,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC,EAAE,GAAG,EAAE,GAAG,KAAK,EAAE,QAAQ,kBAAkBF,cAAG,CAAC,IAAI,EAAE,EAAE,QAAQ,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;AACzO,GAAG;AACH,EAAE;AACF,SAAS,CAAC,WAAW,GAAG,YAAY;;;;"}