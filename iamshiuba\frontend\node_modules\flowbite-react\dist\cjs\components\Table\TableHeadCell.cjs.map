{"version": 3, "file": "TableHeadCell.cjs", "sources": ["../../../../src/components/Table/TableHeadCell.tsx"], "sourcesContent": ["\"use client\";\n\nimport { forwardRef, type ComponentPropsWithRef } from \"react\";\nimport { twMerge } from \"tailwind-merge\";\nimport { mergeDeep } from \"../../helpers/merge-deep\";\nimport type { DeepPartial } from \"../../types\";\nimport { useTableHeadContext } from \"./TableHeadContext\";\n\nexport interface FlowbiteTableHeadCellTheme {\n  base: string;\n}\n\nexport interface TableHeadCellProps extends ComponentPropsWithRef<\"th\"> {\n  theme?: DeepPartial<FlowbiteTableHeadCellTheme>;\n}\n\nexport const TableHeadCell = forwardRef<HTMLTableCellElement, TableHeadCellProps>(\n  ({ children, className, theme: customTheme = {}, ...props }, ref) => {\n    const { theme: headTheme } = useTableHeadContext();\n\n    const theme = mergeDeep(headTheme.cell, customTheme);\n\n    return (\n      <th className={twMerge(theme.base, className)} ref={ref} {...props}>\n        {children}\n      </th>\n    );\n  },\n);\n\nTableHeadCell.displayName = \"Table.HeadCell\";\n"], "names": ["forwardRef", "useTableHeadContext", "mergeDeep", "jsx", "twMerge"], "mappings": ";;;;;;;;AAOY,MAAC,aAAa,GAAGA,gBAAU;AACvC,EAAE,CAAC,EAAE,QAAQ,EAAE,SAAS,EAAE,KAAK,EAAE,WAAW,GAAG,EAAE,EAAE,GAAG,KAAK,EAAE,EAAE,GAAG,KAAK;AACvE,IAAI,MAAM,EAAE,KAAK,EAAE,SAAS,EAAE,GAAGC,oCAAmB,EAAE,CAAC;AACvD,IAAI,MAAM,KAAK,GAAGC,mBAAS,CAAC,SAAS,CAAC,IAAI,EAAE,WAAW,CAAC,CAAC;AACzD,IAAI,uBAAuBC,cAAG,CAAC,IAAI,EAAE,EAAE,SAAS,EAAEC,qBAAO,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC,EAAE,GAAG,EAAE,GAAG,KAAK,EAAE,QAAQ,EAAE,CAAC,CAAC;AAC7G,GAAG;AACH,EAAE;AACF,aAAa,CAAC,WAAW,GAAG,gBAAgB;;;;"}