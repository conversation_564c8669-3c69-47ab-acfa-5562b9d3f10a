{"version": 3, "file": "TableHeadContext.cjs", "sources": ["../../../../src/components/Table/TableHeadContext.tsx"], "sourcesContent": ["\"use client\";\n\nimport { createContext, useContext } from \"react\";\nimport type { FlowbiteTableHeadTheme } from \"./TableHead\";\n\nexport type TableHeadContext = {\n  theme: FlowbiteTableHeadTheme;\n};\n\nexport const TableHeadContext = createContext<TableHeadContext | undefined>(undefined);\n\nexport function useTableHeadContext(): TableHeadContext {\n  const context = useContext(TableHeadContext);\n\n  if (!context) {\n    throw new Error(\"useTableHeadContext should be used within the TableHeadContext provider!\");\n  }\n\n  return context;\n}\n"], "names": ["createContext", "useContext"], "mappings": ";;;;AAGY,MAAC,gBAAgB,GAAGA,mBAAa,CAAC,KAAK,CAAC,EAAE;AAC/C,SAAS,mBAAmB,GAAG;AACtC,EAAE,MAAM,OAAO,GAAGC,gBAAU,CAAC,gBAAgB,CAAC,CAAC;AAC/C,EAAE,IAAI,CAAC,OAAO,EAAE;AAChB,IAAI,MAAM,IAAI,KAAK,CAAC,0EAA0E,CAAC,CAAC;AAChG,GAAG;AACH,EAAE,OAAO,OAAO,CAAC;AACjB;;;;;"}