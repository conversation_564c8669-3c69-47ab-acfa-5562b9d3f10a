{"version": 3, "file": "theme.cjs", "sources": ["../../../../src/components/Table/theme.ts"], "sourcesContent": ["import { createTheme } from \"../../helpers/create-theme\";\nimport type { FlowbiteTableTheme } from \"./Table\";\n\nexport const tableTheme: FlowbiteTableTheme = createTheme({\n  root: {\n    base: \"w-full text-left text-sm text-gray-500 dark:text-gray-400\",\n    shadow: \"absolute left-0 top-0 -z-10 h-full w-full rounded-lg bg-white drop-shadow-md dark:bg-black\",\n    wrapper: \"relative\",\n  },\n  body: {\n    base: \"group/body\",\n    cell: {\n      base: \"px-6 py-4 group-first/body:group-first/row:first:rounded-tl-lg group-first/body:group-first/row:last:rounded-tr-lg group-last/body:group-last/row:first:rounded-bl-lg group-last/body:group-last/row:last:rounded-br-lg\",\n    },\n  },\n  head: {\n    base: \"group/head text-xs uppercase text-gray-700 dark:text-gray-400\",\n    cell: {\n      base: \"bg-gray-50 px-6 py-3 group-first/head:first:rounded-tl-lg group-first/head:last:rounded-tr-lg dark:bg-gray-700\",\n    },\n  },\n  row: {\n    base: \"group/row\",\n    hovered: \"hover:bg-gray-50 dark:hover:bg-gray-600\",\n    striped: \"odd:bg-white even:bg-gray-50 odd:dark:bg-gray-800 even:dark:bg-gray-700\",\n  },\n});\n"], "names": ["createTheme"], "mappings": ";;;;AAEY,MAAC,UAAU,GAAGA,uBAAW,CAAC;AACtC,EAAE,IAAI,EAAE;AACR,IAAI,IAAI,EAAE,2DAA2D;AACrE,IAAI,MAAM,EAAE,4FAA4F;AACxG,IAAI,OAAO,EAAE,UAAU;AACvB,GAAG;AACH,EAAE,IAAI,EAAE;AACR,IAAI,IAAI,EAAE,YAAY;AACtB,IAAI,IAAI,EAAE;AACV,MAAM,IAAI,EAAE,yNAAyN;AACrO,KAAK;AACL,GAAG;AACH,EAAE,IAAI,EAAE;AACR,IAAI,IAAI,EAAE,+DAA+D;AACzE,IAAI,IAAI,EAAE;AACV,MAAM,IAAI,EAAE,gHAAgH;AAC5H,KAAK;AACL,GAAG;AACH,EAAE,GAAG,EAAE;AACP,IAAI,IAAI,EAAE,WAAW;AACrB,IAAI,OAAO,EAAE,yCAAyC;AACtD,IAAI,OAAO,EAAE,yEAAyE;AACtF,GAAG;AACH,CAAC;;;;"}