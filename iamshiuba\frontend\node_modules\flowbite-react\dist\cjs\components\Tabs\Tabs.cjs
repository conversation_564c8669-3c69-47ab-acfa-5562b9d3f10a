'use client';
'use strict';

var jsxRuntime = require('react/jsx-runtime');
var React = require('react');
var tailwindMerge = require('tailwind-merge');
var mergeDeep = require('../../helpers/merge-deep.cjs');
var index = require('../../theme-store/index.cjs');
var TabItem = require('./TabItem.cjs');

const TabsComponent = React.forwardRef(
  ({ children, className, onActiveTabChange, variant = "default", theme: customTheme = {}, ...props }, ref) => {
    const theme = mergeDeep.mergeDeep(index.getTheme().tabs, customTheme);
    const id = React.useId();
    const tabs = React.useMemo(
      () => React.Children.map(
        React.Children.toArray(children),
        ({ props: props2 }) => props2
      ),
      [children]
    );
    const tabRefs = React.useRef([]);
    const [activeTab, setActiveTab] = React.useState(
      Math.max(
        0,
        tabs.findIndex((tab) => tab.active)
      )
    );
    const [focusedTab, setFocusedTab] = React.useState(-1);
    const setActiveTabWithCallback = (activeTab2) => {
      setActiveTab(activeTab2);
      if (onActiveTabChange) onActiveTabChange(activeTab2);
    };
    const handleClick = ({ target }) => {
      setActiveTabWithCallback(target);
      setFocusedTab(target);
    };
    const handleKeyboard = ({ event, target }) => {
      if (event.key === "ArrowLeft") {
        setFocusedTab(Math.max(0, focusedTab - 1));
      }
      if (event.key === "ArrowRight") {
        setFocusedTab(Math.min(tabs.length - 1, focusedTab + 1));
      }
      if (event.key === "Enter") {
        setActiveTabWithCallback(target);
        setFocusedTab(target);
      }
    };
    const tabItemStyle = theme.tablist.tabitem.variant[variant];
    const tabItemContainerStyle = theme.tabitemcontainer.variant[variant];
    React.useEffect(() => {
      tabRefs.current[focusedTab]?.focus();
    }, [focusedTab]);
    React.useImperativeHandle(ref, () => ({
      setActiveTab: setActiveTabWithCallback
    }));
    return /* @__PURE__ */ jsxRuntime.jsxs("div", { className: tailwindMerge.twMerge(theme.base, className), children: [
      /* @__PURE__ */ jsxRuntime.jsx(
        "div",
        {
          "aria-label": "Tabs",
          role: "tablist",
          className: tailwindMerge.twMerge(theme.tablist.base, theme.tablist.variant[variant], className),
          ...props,
          children: tabs.map((tab, index) => /* @__PURE__ */ jsxRuntime.jsxs(
            "button",
            {
              type: "button",
              "aria-controls": `${id}-tabpanel-${index}`,
              "aria-selected": index === activeTab,
              className: tailwindMerge.twMerge(
                theme.tablist.tabitem.base,
                tabItemStyle.base,
                index === activeTab && tabItemStyle.active.on,
                index !== activeTab && !tab.disabled && tabItemStyle.active.off
              ),
              disabled: tab.disabled,
              id: `${id}-tab-${index}`,
              onClick: () => handleClick({ target: index }),
              onKeyDown: (event) => handleKeyboard({ event, target: index }),
              ref: (element) => tabRefs.current[index] = element,
              role: "tab",
              tabIndex: index === focusedTab ? 0 : -1,
              style: { zIndex: index === focusedTab ? 2 : 1 },
              children: [
                tab.icon && /* @__PURE__ */ jsxRuntime.jsx(tab.icon, { className: theme.tablist.tabitem.icon }),
                tab.title
              ]
            },
            index
          ))
        }
      ),
      /* @__PURE__ */ jsxRuntime.jsx("div", { className: tailwindMerge.twMerge(theme.tabitemcontainer.base, tabItemContainerStyle), children: tabs.map((tab, index) => /* @__PURE__ */ jsxRuntime.jsx(
        "div",
        {
          "aria-labelledby": `${id}-tab-${index}`,
          className: theme.tabpanel,
          hidden: index !== activeTab,
          id: `${id}-tabpanel-${index}`,
          role: "tabpanel",
          tabIndex: 0,
          children: tab.children
        },
        index
      )) })
    ] });
  }
);
TabsComponent.displayName = "Tabs";
const Tabs = Object.assign(TabsComponent, {
  Item: TabItem.TabItem
});

exports.Tabs = Tabs;
//# sourceMappingURL=Tabs.cjs.map
