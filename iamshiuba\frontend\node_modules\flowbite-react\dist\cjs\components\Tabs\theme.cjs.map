{"version": 3, "file": "theme.cjs", "sources": ["../../../../src/components/Tabs/theme.ts"], "sourcesContent": ["import { createTheme } from \"../../helpers/create-theme\";\nimport type { FlowbiteTabsTheme } from \"./Tabs\";\n\nexport const tabTheme: FlowbiteTabsTheme = createTheme({\n  base: \"flex flex-col gap-2\",\n  tablist: {\n    base: \"flex text-center\",\n    variant: {\n      default: \"flex-wrap border-b border-gray-200 dark:border-gray-700\",\n      underline: \"-mb-px flex-wrap border-b border-gray-200 dark:border-gray-700\",\n      pills: \"flex-wrap space-x-2 text-sm font-medium text-gray-500 dark:text-gray-400\",\n      fullWidth:\n        \"grid w-full grid-flow-col divide-x divide-gray-200 rounded-none text-sm font-medium shadow dark:divide-gray-700 dark:text-gray-400\",\n    },\n    tabitem: {\n      base: \"flex items-center justify-center rounded-t-lg p-4 text-sm font-medium first:ml-0 focus:outline-none focus:ring-4 focus:ring-cyan-300 disabled:cursor-not-allowed disabled:text-gray-400 disabled:dark:text-gray-500\",\n      variant: {\n        default: {\n          base: \"rounded-t-lg\",\n          active: {\n            on: \"bg-gray-100 text-cyan-600 dark:bg-gray-800 dark:text-cyan-500\",\n            off: \"text-gray-500 hover:bg-gray-50 hover:text-gray-600 dark:text-gray-400 dark:hover:bg-gray-800 dark:hover:text-gray-300\",\n          },\n        },\n        underline: {\n          base: \"rounded-t-lg\",\n          active: {\n            on: \"active rounded-t-lg border-b-2 border-cyan-600 text-cyan-600 dark:border-cyan-500 dark:text-cyan-500\",\n            off: \"border-b-2 border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-600 dark:text-gray-400 dark:hover:text-gray-300\",\n          },\n        },\n        pills: {\n          base: \"\",\n          active: {\n            on: \"rounded-lg bg-cyan-600 text-white\",\n            off: \"rounded-lg hover:bg-gray-100 hover:text-gray-900 dark:hover:bg-gray-800 dark:hover:text-white\",\n          },\n        },\n        fullWidth: {\n          base: \"ml-0 flex w-full rounded-none first:ml-0\",\n          active: {\n            on: \"active rounded-none bg-gray-100 p-4 text-gray-900 dark:bg-gray-700 dark:text-white\",\n            off: \"rounded-none bg-white hover:bg-gray-50 hover:text-gray-700 dark:bg-gray-800 dark:hover:bg-gray-700 dark:hover:text-white\",\n          },\n        },\n      },\n      icon: \"mr-2 h-5 w-5\",\n    },\n  },\n  tabitemcontainer: {\n    base: \"\",\n    variant: {\n      default: \"\",\n      underline: \"\",\n      pills: \"\",\n      fullWidth: \"\",\n    },\n  },\n  tabpanel: \"py-3\",\n});\n"], "names": ["createTheme"], "mappings": ";;;;AAEY,MAAC,QAAQ,GAAGA,uBAAW,CAAC;AACpC,EAAE,IAAI,EAAE,qBAAqB;AAC7B,EAAE,OAAO,EAAE;AACX,IAAI,IAAI,EAAE,kBAAkB;AAC5B,IAAI,OAAO,EAAE;AACb,MAAM,OAAO,EAAE,yDAAyD;AACxE,MAAM,SAAS,EAAE,gEAAgE;AACjF,MAAM,KAAK,EAAE,0EAA0E;AACvF,MAAM,SAAS,EAAE,oIAAoI;AACrJ,KAAK;AACL,IAAI,OAAO,EAAE;AACb,MAAM,IAAI,EAAE,qNAAqN;AACjO,MAAM,OAAO,EAAE;AACf,QAAQ,OAAO,EAAE;AACjB,UAAU,IAAI,EAAE,cAAc;AAC9B,UAAU,MAAM,EAAE;AAClB,YAAY,EAAE,EAAE,+DAA+D;AAC/E,YAAY,GAAG,EAAE,uHAAuH;AACxI,WAAW;AACX,SAAS;AACT,QAAQ,SAAS,EAAE;AACnB,UAAU,IAAI,EAAE,cAAc;AAC9B,UAAU,MAAM,EAAE;AAClB,YAAY,EAAE,EAAE,sGAAsG;AACtH,YAAY,GAAG,EAAE,mIAAmI;AACpJ,WAAW;AACX,SAAS;AACT,QAAQ,KAAK,EAAE;AACf,UAAU,IAAI,EAAE,EAAE;AAClB,UAAU,MAAM,EAAE;AAClB,YAAY,EAAE,EAAE,mCAAmC;AACnD,YAAY,GAAG,EAAE,+FAA+F;AAChH,WAAW;AACX,SAAS;AACT,QAAQ,SAAS,EAAE;AACnB,UAAU,IAAI,EAAE,0CAA0C;AAC1D,UAAU,MAAM,EAAE;AAClB,YAAY,EAAE,EAAE,oFAAoF;AACpG,YAAY,GAAG,EAAE,0HAA0H;AAC3I,WAAW;AACX,SAAS;AACT,OAAO;AACP,MAAM,IAAI,EAAE,cAAc;AAC1B,KAAK;AACL,GAAG;AACH,EAAE,gBAAgB,EAAE;AACpB,IAAI,IAAI,EAAE,EAAE;AACZ,IAAI,OAAO,EAAE;AACb,MAAM,OAAO,EAAE,EAAE;AACjB,MAAM,SAAS,EAAE,EAAE;AACnB,MAAM,KAAK,EAAE,EAAE;AACf,MAAM,SAAS,EAAE,EAAE;AACnB,KAAK;AACL,GAAG;AACH,EAAE,QAAQ,EAAE,MAAM;AAClB,CAAC;;;;"}