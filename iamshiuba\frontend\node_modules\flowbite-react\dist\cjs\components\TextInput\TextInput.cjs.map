{"version": 3, "file": "TextInput.cjs", "sources": ["../../../../src/components/TextInput/TextInput.tsx"], "sourcesContent": ["import type { ComponentProps, FC, ReactNode } from \"react\";\nimport { forwardRef } from \"react\";\nimport { twMerge } from \"tailwind-merge\";\nimport { mergeDeep } from \"../../helpers/merge-deep\";\nimport { getTheme } from \"../../theme-store\";\nimport type { DeepPartial, DynamicStringEnumKeysOf } from \"../../types\";\nimport type { FlowbiteBoolean, FlowbiteColors, FlowbiteSizes } from \"../Flowbite\";\nimport { HelperText } from \"../HelperText\";\n\nexport interface FlowbiteTextInputTheme {\n  base: string;\n  addon: string;\n  field: {\n    base: string;\n    icon: {\n      base: string;\n      svg: string;\n    };\n    rightIcon: {\n      base: string;\n      svg: string;\n    };\n    input: {\n      base: string;\n      sizes: FlowbiteTextInputSizes;\n      colors: FlowbiteTextInputColors;\n      withIcon: FlowbiteBoolean;\n      withRightIcon: FlowbiteBoolean;\n      withAddon: FlowbiteBoolean;\n      withShadow: FlowbiteBoolean;\n    };\n  };\n}\n\nexport interface FlowbiteTextInputColors\n  extends Pick<FlowbiteColors, \"gray\" | \"info\" | \"failure\" | \"warning\" | \"success\"> {\n  [key: string]: string;\n}\n\nexport interface FlowbiteTextInputSizes extends Pick<FlowbiteSizes, \"sm\" | \"md\" | \"lg\"> {\n  [key: string]: string;\n}\n\nexport interface TextInputProps extends Omit<ComponentProps<\"input\">, \"ref\" | \"color\"> {\n  addon?: ReactNode;\n  color?: DynamicStringEnumKeysOf<FlowbiteTextInputColors>;\n  helperText?: ReactNode;\n  icon?: FC<ComponentProps<\"svg\">>;\n  rightIcon?: FC<ComponentProps<\"svg\">>;\n  shadow?: boolean;\n  sizing?: DynamicStringEnumKeysOf<FlowbiteTextInputSizes>;\n  theme?: DeepPartial<FlowbiteTextInputTheme>;\n}\n\nexport const TextInput = forwardRef<HTMLInputElement, TextInputProps>(\n  (\n    {\n      addon,\n      className,\n      color = \"gray\",\n      helperText,\n      icon: Icon,\n      rightIcon: RightIcon,\n      shadow,\n      sizing = \"md\",\n      theme: customTheme = {},\n      type = \"text\",\n      ...props\n    },\n    ref,\n  ) => {\n    const theme = mergeDeep(getTheme().textInput, customTheme);\n\n    return (\n      <>\n        <div className={twMerge(theme.base, className)}>\n          {addon && <span className={theme.addon}>{addon}</span>}\n          <div className={theme.field.base}>\n            {Icon && (\n              <div className={theme.field.icon.base}>\n                <Icon className={theme.field.icon.svg} />\n              </div>\n            )}\n            {RightIcon && (\n              <div data-testid=\"right-icon\" className={theme.field.rightIcon.base}>\n                <RightIcon className={theme.field.rightIcon.svg} />\n              </div>\n            )}\n            <input\n              className={twMerge(\n                theme.field.input.base,\n                theme.field.input.colors[color],\n                theme.field.input.sizes[sizing],\n                theme.field.input.withIcon[Icon ? \"on\" : \"off\"],\n                theme.field.input.withRightIcon[RightIcon ? \"on\" : \"off\"],\n                theme.field.input.withAddon[addon ? \"on\" : \"off\"],\n                theme.field.input.withShadow[shadow ? \"on\" : \"off\"],\n              )}\n              type={type}\n              {...props}\n              ref={ref}\n            />\n          </div>\n        </div>\n        {helperText && <HelperText color={color}>{helperText}</HelperText>}\n      </>\n    );\n  },\n);\n\nTextInput.displayName = \"TextInput\";\n"], "names": ["forwardRef", "mergeDeep", "getTheme", "jsxs", "Fragment", "twMerge", "jsx", "HelperText"], "mappings": ";;;;;;;;;AAOY,MAAC,SAAS,GAAGA,gBAAU;AACnC,EAAE,CAAC;AACH,IAAI,KAAK;AACT,IAAI,SAAS;AACb,IAAI,KAAK,GAAG,MAAM;AAClB,IAAI,UAAU;AACd,IAAI,IAAI,EAAE,IAAI;AACd,IAAI,SAAS,EAAE,SAAS;AACxB,IAAI,MAAM;AACV,IAAI,MAAM,GAAG,IAAI;AACjB,IAAI,KAAK,EAAE,WAAW,GAAG,EAAE;AAC3B,IAAI,IAAI,GAAG,MAAM;AACjB,IAAI,GAAG,KAAK;AACZ,GAAG,EAAE,GAAG,KAAK;AACb,IAAI,MAAM,KAAK,GAAGC,mBAAS,CAACC,cAAQ,EAAE,CAAC,SAAS,EAAE,WAAW,CAAC,CAAC;AAC/D,IAAI,uBAAuBC,eAAI,CAACC,mBAAQ,EAAE,EAAE,QAAQ,EAAE;AACtD,sBAAsBD,eAAI,CAAC,KAAK,EAAE,EAAE,SAAS,EAAEE,qBAAO,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC,EAAE,QAAQ,EAAE;AACzF,QAAQ,KAAK,oBAAoBC,cAAG,CAAC,MAAM,EAAE,EAAE,SAAS,EAAE,KAAK,CAAC,KAAK,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC;AACzF,wBAAwBH,eAAI,CAAC,KAAK,EAAE,EAAE,SAAS,EAAE,KAAK,CAAC,KAAK,CAAC,IAAI,EAAE,QAAQ,EAAE;AAC7E,UAAU,IAAI,oBAAoBG,cAAG,CAAC,KAAK,EAAE,EAAE,SAAS,EAAE,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,EAAE,QAAQ,kBAAkBA,cAAG,CAAC,IAAI,EAAE,EAAE,SAAS,EAAE,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC;AAC5J,UAAU,SAAS,oBAAoBA,cAAG,CAAC,KAAK,EAAE,EAAE,aAAa,EAAE,YAAY,EAAE,SAAS,EAAE,KAAK,CAAC,KAAK,CAAC,SAAS,CAAC,IAAI,EAAE,QAAQ,kBAAkBA,cAAG,CAAC,SAAS,EAAE,EAAE,SAAS,EAAE,KAAK,CAAC,KAAK,CAAC,SAAS,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC;AAC7M,0BAA0BA,cAAG;AAC7B,YAAY,OAAO;AACnB,YAAY;AACZ,cAAc,SAAS,EAAED,qBAAO;AAChC,gBAAgB,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI;AACtC,gBAAgB,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC;AAC/C,gBAAgB,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC;AAC/C,gBAAgB,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,QAAQ,CAAC,IAAI,GAAG,IAAI,GAAG,KAAK,CAAC;AAC/D,gBAAgB,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,aAAa,CAAC,SAAS,GAAG,IAAI,GAAG,KAAK,CAAC;AACzE,gBAAgB,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,SAAS,CAAC,KAAK,GAAG,IAAI,GAAG,KAAK,CAAC;AACjE,gBAAgB,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,UAAU,CAAC,MAAM,GAAG,IAAI,GAAG,KAAK,CAAC;AACnE,eAAe;AACf,cAAc,IAAI;AAClB,cAAc,GAAG,KAAK;AACtB,cAAc,GAAG;AACjB,aAAa;AACb,WAAW;AACX,SAAS,EAAE,CAAC;AACZ,OAAO,EAAE,CAAC;AACV,MAAM,UAAU,oBAAoBC,cAAG,CAACC,qBAAU,EAAE,EAAE,KAAK,EAAE,QAAQ,EAAE,UAAU,EAAE,CAAC;AACpF,KAAK,EAAE,CAAC,CAAC;AACT,GAAG;AACH,EAAE;AACF,SAAS,CAAC,WAAW,GAAG,WAAW;;;;"}