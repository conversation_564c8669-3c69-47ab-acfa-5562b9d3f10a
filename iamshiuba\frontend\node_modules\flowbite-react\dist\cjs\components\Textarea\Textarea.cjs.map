{"version": 3, "file": "Textarea.cjs", "sources": ["../../../../src/components/Textarea/Textarea.tsx"], "sourcesContent": ["import type { ComponentProps, ReactNode } from \"react\";\nimport { forwardRef } from \"react\";\nimport { twMerge } from \"tailwind-merge\";\nimport { mergeDeep } from \"../../helpers/merge-deep\";\nimport { getTheme } from \"../../theme-store\";\nimport type { DeepPartial, DynamicStringEnumKeysOf } from \"../../types\";\nimport type { FlowbiteBoolean, FlowbiteColors } from \"../Flowbite\";\nimport { HelperText } from \"../HelperText\";\n\nexport interface FlowbiteTextareaTheme {\n  base: string;\n  colors: TextareaColors;\n  withShadow: FlowbiteBoolean;\n}\n\nexport interface TextareaColors extends Pick<FlowbiteColors, \"gray\" | \"info\" | \"failure\" | \"warning\" | \"success\"> {\n  [key: string]: string;\n}\n\nexport interface TextareaProps extends Omit<ComponentProps<\"textarea\">, \"color\" | \"ref\"> {\n  color?: DynamicStringEnumKeysOf<TextareaColors>;\n  helperText?: ReactNode;\n  shadow?: boolean;\n  theme?: DeepPartial<FlowbiteTextareaTheme>;\n}\n\nexport const Textarea = forwardRef<HTMLTextAreaElement, TextareaProps>(\n  ({ className, color = \"gray\", helperText, shadow, theme: customTheme = {}, ...props }, ref) => {\n    const theme = mergeDeep(getTheme().textarea, customTheme);\n\n    return (\n      <>\n        <textarea\n          ref={ref}\n          className={twMerge(theme.base, theme.colors[color], theme.withShadow[shadow ? \"on\" : \"off\"], className)}\n          {...props}\n        />\n        {helperText && <HelperText color={color}>{helperText}</HelperText>}\n      </>\n    );\n  },\n);\n\nTextarea.displayName = \"Textarea\";\n"], "names": ["forwardRef", "mergeDeep", "getTheme", "jsxs", "Fragment", "jsx", "twMerge", "HelperText"], "mappings": ";;;;;;;;;AAOY,MAAC,QAAQ,GAAGA,gBAAU;AAClC,EAAE,CAAC,EAAE,SAAS,EAAE,KAAK,GAAG,MAAM,EAAE,UAAU,EAAE,MAAM,EAAE,KAAK,EAAE,WAAW,GAAG,EAAE,EAAE,GAAG,KAAK,EAAE,EAAE,GAAG,KAAK;AACjG,IAAI,MAAM,KAAK,GAAGC,mBAAS,CAACC,cAAQ,EAAE,CAAC,QAAQ,EAAE,WAAW,CAAC,CAAC;AAC9D,IAAI,uBAAuBC,eAAI,CAACC,mBAAQ,EAAE,EAAE,QAAQ,EAAE;AACtD,sBAAsBC,cAAG;AACzB,QAAQ,UAAU;AAClB,QAAQ;AACR,UAAU,GAAG;AACb,UAAU,SAAS,EAAEC,qBAAO,CAAC,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,KAAK,CAAC,UAAU,CAAC,MAAM,GAAG,IAAI,GAAG,KAAK,CAAC,EAAE,SAAS,CAAC;AACjH,UAAU,GAAG,KAAK;AAClB,SAAS;AACT,OAAO;AACP,MAAM,UAAU,oBAAoBD,cAAG,CAACE,qBAAU,EAAE,EAAE,KAAK,EAAE,QAAQ,EAAE,UAAU,EAAE,CAAC;AACpF,KAAK,EAAE,CAAC,CAAC;AACT,GAAG;AACH,EAAE;AACF,QAAQ,CAAC,WAAW,GAAG,UAAU;;;;"}