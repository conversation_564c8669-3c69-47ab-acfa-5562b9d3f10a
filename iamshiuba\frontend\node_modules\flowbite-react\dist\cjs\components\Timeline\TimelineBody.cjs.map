{"version": 3, "file": "TimelineBody.cjs", "sources": ["../../../../src/components/Timeline/TimelineBody.tsx"], "sourcesContent": ["\"use client\";\n\nimport type { ComponentProps, FC } from \"react\";\nimport { twMerge } from \"tailwind-merge\";\nimport { mergeDeep } from \"../../helpers/merge-deep\";\nimport type { DeepPartial } from \"../../types\";\nimport { useTimelineContentContext } from \"./TimelineContentContext\";\n\nexport interface FlowbiteTimelineBodyTheme {\n  base: string;\n}\n\nexport interface TimelineBodyProps extends ComponentProps<\"p\"> {\n  theme?: DeepPartial<FlowbiteTimelineBodyTheme>;\n}\n\nexport const TimelineBody: FC<TimelineBodyProps> = ({ children, className, theme: customTheme = {}, ...props }) => {\n  const { theme: contentTheme } = useTimelineContentContext();\n\n  const theme = mergeDeep(contentTheme.body, customTheme);\n\n  return (\n    <div className={twMerge(theme.base, className)} {...props}>\n      {children}\n    </div>\n  );\n};\n"], "names": ["useTimelineContentContext", "mergeDeep", "jsx", "twMerge"], "mappings": ";;;;;;;AAMY,MAAC,YAAY,GAAG,CAAC,EAAE,QAAQ,EAAE,SAAS,EAAE,KAAK,EAAE,WAAW,GAAG,EAAE,EAAE,GAAG,KAAK,EAAE,KAAK;AAC5F,EAAE,MAAM,EAAE,KAAK,EAAE,YAAY,EAAE,GAAGA,gDAAyB,EAAE,CAAC;AAC9D,EAAE,MAAM,KAAK,GAAGC,mBAAS,CAAC,YAAY,CAAC,IAAI,EAAE,WAAW,CAAC,CAAC;AAC1D,EAAE,uBAAuBC,cAAG,CAAC,KAAK,EAAE,EAAE,SAAS,EAAEC,qBAAO,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC,EAAE,GAAG,KAAK,EAAE,QAAQ,EAAE,CAAC,CAAC;AACvG;;;;"}