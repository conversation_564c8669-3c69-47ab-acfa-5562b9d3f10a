'use client';
'use strict';

var jsxRuntime = require('react/jsx-runtime');
var tailwindMerge = require('tailwind-merge');
var mergeDeep = require('../../helpers/merge-deep.cjs');
var TimelineContentContext = require('./TimelineContentContext.cjs');
var TimelineContext = require('./TimelineContext.cjs');
var TimelineItemContext = require('./TimelineItemContext.cjs');

const TimelineContent = ({
  children,
  className,
  theme: customTheme = {},
  ...props
}) => {
  const { horizontal } = TimelineContext.useTimelineContext();
  const { theme: itemTheme } = TimelineItemContext.useTimelineItemContext();
  const theme = mergeDeep.mergeDeep(itemTheme.content, customTheme);
  return /* @__PURE__ */ jsxRuntime.jsx(TimelineContentContext.TimelineContentContext.Provider, { value: { theme }, children: /* @__PURE__ */ jsxRuntime.jsx(
    "div",
    {
      "data-testid": "timeline-content",
      className: tailwindMerge.twMerge(theme.root.base, horizontal ? theme.root.horizontal : theme.root.vertical, className),
      ...props,
      children
    }
  ) });
};

exports.TimelineContent = TimelineContent;
//# sourceMappingURL=TimelineContent.cjs.map
