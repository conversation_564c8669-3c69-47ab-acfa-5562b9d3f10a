{"version": 3, "file": "TimelineContent.cjs", "sources": ["../../../../src/components/Timeline/TimelineContent.tsx"], "sourcesContent": ["\"use client\";\n\nimport type { ComponentProps, FC } from \"react\";\nimport { twMerge } from \"tailwind-merge\";\nimport { mergeDeep } from \"../../helpers/merge-deep\";\nimport type { DeepPartial } from \"../../types\";\nimport type { FlowbiteTimelineBodyTheme } from \"./TimelineBody\";\nimport { TimelineContentContext } from \"./TimelineContentContext\";\nimport { useTimelineContext } from \"./TimelineContext\";\nimport { useTimelineItemContext } from \"./TimelineItemContext\";\nimport type { FlowbiteTimelineTimeTheme } from \"./TimelineTime\";\nimport type { FlowbiteTimelineTitleTheme } from \"./TimelineTitle\";\n\nexport interface FlowbiteTimelineContentTheme {\n  root: {\n    base: string;\n    horizontal: string;\n    vertical: string;\n  };\n  time: FlowbiteTimelineTitleTheme;\n  title: FlowbiteTimelineTimeTheme;\n  body: FlowbiteTimelineBodyTheme;\n}\n\nexport interface TimelineContentProps extends ComponentProps<\"div\"> {\n  theme?: DeepPartial<FlowbiteTimelineContentTheme>;\n}\n\nexport const TimelineContent: FC<TimelineContentProps> = ({\n  children,\n  className,\n  theme: customTheme = {},\n  ...props\n}) => {\n  const { horizontal } = useTimelineContext();\n  const { theme: itemTheme } = useTimelineItemContext();\n\n  const theme = mergeDeep(itemTheme.content, customTheme);\n\n  return (\n    <TimelineContentContext.Provider value={{ theme }}>\n      <div\n        data-testid=\"timeline-content\"\n        className={twMerge(theme.root.base, horizontal ? theme.root.horizontal : theme.root.vertical, className)}\n        {...props}\n      >\n        {children}\n      </div>\n    </TimelineContentContext.Provider>\n  );\n};\n"], "names": ["useTimelineContext", "useTimelineItemContext", "mergeDeep", "jsx", "TimelineContentContext", "twMerge"], "mappings": ";;;;;;;;;AAQY,MAAC,eAAe,GAAG,CAAC;AAChC,EAAE,QAAQ;AACV,EAAE,SAAS;AACX,EAAE,KAAK,EAAE,WAAW,GAAG,EAAE;AACzB,EAAE,GAAG,KAAK;AACV,CAAC,KAAK;AACN,EAAE,MAAM,EAAE,UAAU,EAAE,GAAGA,kCAAkB,EAAE,CAAC;AAC9C,EAAE,MAAM,EAAE,KAAK,EAAE,SAAS,EAAE,GAAGC,0CAAsB,EAAE,CAAC;AACxD,EAAE,MAAM,KAAK,GAAGC,mBAAS,CAAC,SAAS,CAAC,OAAO,EAAE,WAAW,CAAC,CAAC;AAC1D,EAAE,uBAAuBC,cAAG,CAACC,6CAAsB,CAAC,QAAQ,EAAE,EAAE,KAAK,EAAE,EAAE,KAAK,EAAE,EAAE,QAAQ,kBAAkBD,cAAG;AAC/G,IAAI,KAAK;AACT,IAAI;AACJ,MAAM,aAAa,EAAE,kBAAkB;AACvC,MAAM,SAAS,EAAEE,qBAAO,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,EAAE,UAAU,GAAG,KAAK,CAAC,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC,IAAI,CAAC,QAAQ,EAAE,SAAS,CAAC;AAC9G,MAAM,GAAG,KAAK;AACd,MAAM,QAAQ;AACd,KAAK;AACL,GAAG,EAAE,CAAC,CAAC;AACP;;;;"}