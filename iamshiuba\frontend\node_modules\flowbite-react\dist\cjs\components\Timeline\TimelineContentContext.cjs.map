{"version": 3, "file": "TimelineContentContext.cjs", "sources": ["../../../../src/components/Timeline/TimelineContentContext.tsx"], "sourcesContent": ["\"use client\";\n\nimport { createContext, useContext } from \"react\";\nimport type { FlowbiteTimelineContentTheme } from \"./TimelineContent\";\n\nexport type TimelineContentContext = {\n  theme: FlowbiteTimelineContentTheme;\n};\n\nexport const TimelineContentContext = createContext<TimelineContentContext | undefined>(undefined);\n\nexport function useTimelineContentContext(): TimelineContentContext {\n  const context = useContext(TimelineContentContext);\n\n  if (!context) {\n    throw new Error(\"useTimelineContentContext should be used within the TimelineContentContext provider!\");\n  }\n\n  return context;\n}\n"], "names": ["createContext", "useContext"], "mappings": ";;;;AAGY,MAAC,sBAAsB,GAAGA,mBAAa,CAAC,KAAK,CAAC,EAAE;AACrD,SAAS,yBAAyB,GAAG;AAC5C,EAAE,MAAM,OAAO,GAAGC,gBAAU,CAAC,sBAAsB,CAAC,CAAC;AACrD,EAAE,IAAI,CAAC,OAAO,EAAE;AAChB,IAAI,MAAM,IAAI,KAAK,CAAC,sFAAsF,CAAC,CAAC;AAC5G,GAAG;AACH,EAAE,OAAO,OAAO,CAAC;AACjB;;;;;"}