{"version": 3, "file": "TimelineContext.cjs", "sources": ["../../../../src/components/Timeline/TimelineContext.tsx"], "sourcesContent": ["\"use client\";\n\nimport { createContext, useContext } from \"react\";\nimport type { FlowbiteTimelineTheme } from \"./Timeline\";\n\nexport type TimelineContext = {\n  theme: FlowbiteTimelineTheme;\n  horizontal?: boolean;\n};\n\nexport const TimelineContext = createContext<TimelineContext | undefined>(undefined);\n\nexport function useTimelineContext(): TimelineContext {\n  const context = useContext(TimelineContext);\n\n  if (!context) {\n    throw new Error(\"useTimelineContext should be used within the TimelineContext provider!\");\n  }\n\n  return context;\n}\n"], "names": ["createContext", "useContext"], "mappings": ";;;;AAGY,MAAC,eAAe,GAAGA,mBAAa,CAAC,KAAK,CAAC,EAAE;AAC9C,SAAS,kBAAkB,GAAG;AACrC,EAAE,MAAM,OAAO,GAAGC,gBAAU,CAAC,eAAe,CAAC,CAAC;AAC9C,EAAE,IAAI,CAAC,OAAO,EAAE;AAChB,IAAI,MAAM,IAAI,KAAK,CAAC,wEAAwE,CAAC,CAAC;AAC9F,GAAG;AACH,EAAE,OAAO,OAAO,CAAC;AACjB;;;;;"}