{"version": 3, "file": "TimelineItem.cjs", "sources": ["../../../../src/components/Timeline/TimelineItem.tsx"], "sourcesContent": ["\"use client\";\n\nimport type { ComponentProps, FC } from \"react\";\nimport { twMerge } from \"tailwind-merge\";\nimport { mergeDeep } from \"../../helpers/merge-deep\";\nimport type { DeepPartial } from \"../../types\";\nimport type { FlowbiteTimelineContentTheme } from \"./TimelineContent\";\nimport { useTimelineContext } from \"./TimelineContext\";\nimport { TimelineItemContext } from \"./TimelineItemContext\";\nimport type { FlowbiteTimelinePointTheme } from \"./TimelinePoint\";\n\nexport interface FlowbiteTimelineItemTheme {\n  root: {\n    horizontal: string;\n    vertical: string;\n  };\n  content: FlowbiteTimelineContentTheme;\n  point: FlowbiteTimelinePointTheme;\n}\n\nexport interface TimelineItemProps extends ComponentProps<\"li\"> {\n  theme?: DeepPartial<FlowbiteTimelineItemTheme>;\n}\n\nexport const TimelineItem: FC<TimelineItemProps> = ({ children, className, theme: customTheme = {}, ...props }) => {\n  const { theme: rootTheme, horizontal } = useTimelineContext();\n\n  const theme = mergeDeep(rootTheme.item, customTheme);\n\n  return (\n    <TimelineItemContext.Provider value={{ theme }}>\n      <li\n        data-testid=\"timeline-item\"\n        className={twMerge(horizontal && theme.root.horizontal, !horizontal && theme.root.vertical, className)}\n        {...props}\n      >\n        {children}\n      </li>\n    </TimelineItemContext.Provider>\n  );\n};\n"], "names": ["useTimelineContext", "mergeDeep", "jsx", "TimelineItemContext", "twMerge"], "mappings": ";;;;;;;;AAOY,MAAC,YAAY,GAAG,CAAC,EAAE,QAAQ,EAAE,SAAS,EAAE,KAAK,EAAE,WAAW,GAAG,EAAE,EAAE,GAAG,KAAK,EAAE,KAAK;AAC5F,EAAE,MAAM,EAAE,KAAK,EAAE,SAAS,EAAE,UAAU,EAAE,GAAGA,kCAAkB,EAAE,CAAC;AAChE,EAAE,MAAM,KAAK,GAAGC,mBAAS,CAAC,SAAS,CAAC,IAAI,EAAE,WAAW,CAAC,CAAC;AACvD,EAAE,uBAAuBC,cAAG,CAACC,uCAAmB,CAAC,QAAQ,EAAE,EAAE,KAAK,EAAE,EAAE,KAAK,EAAE,EAAE,QAAQ,kBAAkBD,cAAG;AAC5G,IAAI,IAAI;AACR,IAAI;AACJ,MAAM,aAAa,EAAE,eAAe;AACpC,MAAM,SAAS,EAAEE,qBAAO,CAAC,UAAU,IAAI,KAAK,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC,UAAU,IAAI,KAAK,CAAC,IAAI,CAAC,QAAQ,EAAE,SAAS,CAAC;AAC5G,MAAM,GAAG,KAAK;AACd,MAAM,QAAQ;AACd,KAAK;AACL,GAAG,EAAE,CAAC,CAAC;AACP;;;;"}