{"version": 3, "file": "TimelineTitle.cjs", "sources": ["../../../../src/components/Timeline/TimelineTitle.tsx"], "sourcesContent": ["\"use client\";\n\nimport type { ComponentProps, FC } from \"react\";\nimport { twMerge } from \"tailwind-merge\";\nimport { mergeDeep } from \"../../helpers/merge-deep\";\nimport type { DeepPartial } from \"../../types\";\nimport type { FlowbiteHeadingLevel } from \"../Flowbite\";\nimport { useTimelineContentContext } from \"./TimelineContentContext\";\n\nexport interface FlowbiteTimelineTitleTheme {\n  base: string;\n}\n\nexport interface TimelineTitleProps extends ComponentProps<\"h1\"> {\n  as?: FlowbiteHeadingLevel;\n  theme?: DeepPartial<FlowbiteTimelineTitleTheme>;\n}\n\nexport const TimelineTitle: FC<TimelineTitleProps> = ({\n  as: Tag = \"h3\",\n  children,\n  className,\n  theme: customTheme = {},\n  ...props\n}) => {\n  const { theme: contentTheme } = useTimelineContentContext();\n\n  const theme = mergeDeep(contentTheme.title, customTheme);\n\n  return (\n    <Tag className={twMerge(theme.base, className)} {...props}>\n      {children}\n    </Tag>\n  );\n};\n"], "names": ["useTimelineContentContext", "mergeDeep", "jsx", "twMerge"], "mappings": ";;;;;;;AAMY,MAAC,aAAa,GAAG,CAAC;AAC9B,EAAE,EAAE,EAAE,GAAG,GAAG,IAAI;AAChB,EAAE,QAAQ;AACV,EAAE,SAAS;AACX,EAAE,KAAK,EAAE,WAAW,GAAG,EAAE;AACzB,EAAE,GAAG,KAAK;AACV,CAAC,KAAK;AACN,EAAE,MAAM,EAAE,KAAK,EAAE,YAAY,EAAE,GAAGA,gDAAyB,EAAE,CAAC;AAC9D,EAAE,MAAM,KAAK,GAAGC,mBAAS,CAAC,YAAY,CAAC,KAAK,EAAE,WAAW,CAAC,CAAC;AAC3D,EAAE,uBAAuBC,cAAG,CAAC,GAAG,EAAE,EAAE,SAAS,EAAEC,qBAAO,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC,EAAE,GAAG,KAAK,EAAE,QAAQ,EAAE,CAAC,CAAC;AACrG;;;;"}