'use client';
'use strict';

var jsxRuntime = require('react/jsx-runtime');
var hi = require('react-icons/hi');
var tailwindMerge = require('tailwind-merge');
var mergeDeep = require('../../helpers/merge-deep.cjs');
var ToastContext = require('./ToastContext.cjs');

const ToastToggle = ({
  className,
  onClick,
  theme: customTheme = {},
  xIcon: XIcon = hi.HiX,
  onDismiss,
  ...props
}) => {
  const { theme: rootTheme, duration, isClosed, isRemoved, setIsClosed, setIsRemoved } = ToastContext.useToastContext();
  const theme = mergeDeep.mergeDeep(rootTheme.toggle, customTheme);
  const handleClick = (e) => {
    if (onClick) onClick(e);
    if (onDismiss) {
      onDismiss();
      return;
    }
    setIsClosed(!isClosed);
    setTimeout(() => setIsRemoved(!isRemoved), duration);
  };
  return /* @__PURE__ */ jsxRuntime.jsx(
    "button",
    {
      "aria-label": "Close",
      onClick: handleClick,
      type: "button",
      className: tailwindMerge.twMerge(theme.base, className),
      ...props,
      children: /* @__PURE__ */ jsxRuntime.jsx(XIcon, { "aria-hidden": true, className: theme.icon })
    }
  );
};

exports.ToastToggle = ToastToggle;
//# sourceMappingURL=ToastToggle.cjs.map
