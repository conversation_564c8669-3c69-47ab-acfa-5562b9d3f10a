{"version": 3, "file": "ToastToggle.cjs", "sources": ["../../../../src/components/Toast/ToastToggle.tsx"], "sourcesContent": ["\"use client\";\n\nimport type { ComponentProps, FC, MouseEvent } from \"react\";\nimport { HiX } from \"react-icons/hi\";\nimport { twMerge } from \"tailwind-merge\";\nimport { mergeDeep } from \"../../helpers/merge-deep\";\nimport type { DeepPartial } from \"../../types\";\nimport { useToastContext } from \"./ToastContext\";\n\nexport interface FlowbiteToastToggleTheme {\n  base: string;\n  icon: string;\n}\n\nexport interface ToastToggleProps extends ComponentProps<\"button\"> {\n  theme?: DeepPartial<FlowbiteToastToggleTheme>;\n  xIcon?: FC<ComponentProps<\"svg\">>;\n  onDismiss?: () => void;\n}\n\nexport const ToastToggle: FC<ToastToggleProps> = ({\n  className,\n  onClick,\n  theme: customTheme = {},\n  xIcon: XIcon = HiX,\n  onDismiss,\n  ...props\n}) => {\n  const { theme: rootTheme, duration, isClosed, isRemoved, setIsClosed, setIsRemoved } = useToastContext();\n\n  const theme = mergeDeep(rootTheme.toggle, customTheme);\n\n  const handleClick = (e: MouseEvent<HTMLButtonElement>) => {\n    if (onClick) onClick(e);\n\n    if (onDismiss) {\n      onDismiss();\n      return;\n    }\n\n    setIsClosed(!isClosed);\n    setTimeout(() => setIsRemoved(!isRemoved), duration);\n  };\n\n  return (\n    <button\n      aria-label=\"Close\"\n      onClick={handleClick}\n      type=\"button\"\n      className={twMerge(theme.base, className)}\n      {...props}\n    >\n      <XIcon aria-hidden className={theme.icon} />\n    </button>\n  );\n};\n"], "names": ["HiX", "useToastContext", "mergeDeep", "jsx", "twMerge"], "mappings": ";;;;;;;;AAOY,MAAC,WAAW,GAAG,CAAC;AAC5B,EAAE,SAAS;AACX,EAAE,OAAO;AACT,EAAE,KAAK,EAAE,WAAW,GAAG,EAAE;AACzB,EAAE,KAAK,EAAE,KAAK,GAAGA,MAAG;AACpB,EAAE,SAAS;AACX,EAAE,GAAG,KAAK;AACV,CAAC,KAAK;AACN,EAAE,MAAM,EAAE,KAAK,EAAE,SAAS,EAAE,QAAQ,EAAE,QAAQ,EAAE,SAAS,EAAE,WAAW,EAAE,YAAY,EAAE,GAAGC,4BAAe,EAAE,CAAC;AAC3G,EAAE,MAAM,KAAK,GAAGC,mBAAS,CAAC,SAAS,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC;AACzD,EAAE,MAAM,WAAW,GAAG,CAAC,CAAC,KAAK;AAC7B,IAAI,IAAI,OAAO,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC;AAC5B,IAAI,IAAI,SAAS,EAAE;AACnB,MAAM,SAAS,EAAE,CAAC;AAClB,MAAM,OAAO;AACb,KAAK;AACL,IAAI,WAAW,CAAC,CAAC,QAAQ,CAAC,CAAC;AAC3B,IAAI,UAAU,CAAC,MAAM,YAAY,CAAC,CAAC,SAAS,CAAC,EAAE,QAAQ,CAAC,CAAC;AACzD,GAAG,CAAC;AACJ,EAAE,uBAAuBC,cAAG;AAC5B,IAAI,QAAQ;AACZ,IAAI;AACJ,MAAM,YAAY,EAAE,OAAO;AAC3B,MAAM,OAAO,EAAE,WAAW;AAC1B,MAAM,IAAI,EAAE,QAAQ;AACpB,MAAM,SAAS,EAAEC,qBAAO,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC;AAC/C,MAAM,GAAG,KAAK;AACd,MAAM,QAAQ,kBAAkBD,cAAG,CAAC,KAAK,EAAE,EAAE,aAAa,EAAE,IAAI,EAAE,SAAS,EAAE,KAAK,CAAC,IAAI,EAAE,CAAC;AAC1F,KAAK;AACL,GAAG,CAAC;AACJ;;;;"}