{"version": 3, "file": "theme.cjs", "sources": ["../../../../src/components/Tooltip/theme.ts"], "sourcesContent": ["import { createTheme } from \"../../helpers/create-theme\";\nimport type { FlowbiteTooltipTheme } from \"./Tooltip\";\n\nexport const tooltipTheme: FlowbiteTooltipTheme = createTheme({\n  target: \"w-fit\",\n  animation: \"transition-opacity\",\n  arrow: {\n    base: \"absolute z-10 h-2 w-2 rotate-45\",\n    style: {\n      dark: \"bg-gray-900 dark:bg-gray-700\",\n      light: \"bg-white\",\n      auto: \"bg-white dark:bg-gray-700\",\n    },\n    placement: \"-4px\",\n  },\n  base: \"absolute z-10 inline-block rounded-lg px-3 py-2 text-sm font-medium shadow-sm\",\n  hidden: \"invisible opacity-0\",\n  style: {\n    dark: \"bg-gray-900 text-white dark:bg-gray-700\",\n    light: \"border border-gray-200 bg-white text-gray-900\",\n    auto: \"border border-gray-200 bg-white text-gray-900 dark:border-none dark:bg-gray-700 dark:text-white\",\n  },\n  content: \"relative z-20\",\n});\n"], "names": ["createTheme"], "mappings": ";;;;AAEY,MAAC,YAAY,GAAGA,uBAAW,CAAC;AACxC,EAAE,MAAM,EAAE,OAAO;AACjB,EAAE,SAAS,EAAE,oBAAoB;AACjC,EAAE,KAAK,EAAE;AACT,IAAI,IAAI,EAAE,iCAAiC;AAC3C,IAAI,KAAK,EAAE;AACX,MAAM,IAAI,EAAE,8BAA8B;AAC1C,MAAM,KAAK,EAAE,UAAU;AACvB,MAAM,IAAI,EAAE,2BAA2B;AACvC,KAAK;AACL,IAAI,SAAS,EAAE,MAAM;AACrB,GAAG;AACH,EAAE,IAAI,EAAE,+EAA+E;AACvF,EAAE,MAAM,EAAE,qBAAqB;AAC/B,EAAE,KAAK,EAAE;AACT,IAAI,IAAI,EAAE,yCAAyC;AACnD,IAAI,KAAK,EAAE,+CAA+C;AAC1D,IAAI,IAAI,EAAE,iGAAiG;AAC3G,GAAG;AACH,EAAE,OAAO,EAAE,eAAe;AAC1B,CAAC;;;;"}