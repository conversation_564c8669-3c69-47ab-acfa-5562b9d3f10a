{"version": 3, "file": "merge-deep.cjs", "sources": ["../../../src/helpers/merge-deep.ts"], "sourcesContent": ["import { cloneDeep } from \"./clone-deep\";\nimport { isObject } from \"./is-object\";\n\n/**\n * Merge and deep copy the values of all of the enumerable own properties of target object from source object to a new object\n * @param target The target object to get properties from.\n * @param source The source object from which to copy properties.\n * @return A new merged and deep copied object.\n */\nexport function mergeDeep<T extends object, S extends object>(target: T, source: S): T & S {\n  if (isObject(source) && Object.keys(source).length === 0) {\n    return cloneDeep({ ...target, ...source });\n  }\n\n  const output = { ...target, ...source };\n\n  if (isObject(source) && isObject(target)) {\n    for (const key in source) {\n      if (isObject(source[key]) && key in target && isObject(target[key])) {\n        (output as Record<string, unknown>)[key] = mergeDeep(target[key] as object, source[key] as object);\n      } else {\n        (output as Record<string, unknown>)[key] = isObject(source[key]) ? cloneDeep(source[key]) : source[key];\n      }\n    }\n  }\n\n  return output;\n}\n"], "names": ["isObject", "cloneDeep"], "mappings": ";;;;;AAGO,SAAS,SAAS,CAAC,MAAM,EAAE,MAAM,EAAE;AAC1C,EAAE,IAAIA,iBAAQ,CAAC,MAAM,CAAC,IAAI,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,MAAM,KAAK,CAAC,EAAE;AAC5D,IAAI,OAAOC,mBAAS,CAAC,EAAE,GAAG,MAAM,EAAE,GAAG,MAAM,EAAE,CAAC,CAAC;AAC/C,GAAG;AACH,EAAE,MAAM,MAAM,GAAG,EAAE,GAAG,MAAM,EAAE,GAAG,MAAM,EAAE,CAAC;AAC1C,EAAE,IAAID,iBAAQ,CAAC,MAAM,CAAC,IAAIA,iBAAQ,CAAC,MAAM,CAAC,EAAE;AAC5C,IAAI,KAAK,MAAM,GAAG,IAAI,MAAM,EAAE;AAC9B,MAAM,IAAIA,iBAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,GAAG,IAAI,MAAM,IAAIA,iBAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,EAAE;AAC3E,QAAQ,MAAM,CAAC,GAAG,CAAC,GAAG,SAAS,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC;AAC1D,OAAO,MAAM;AACb,QAAQ,MAAM,CAAC,GAAG,CAAC,GAAGA,iBAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,GAAGC,mBAAS,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC;AACnF,OAAO;AACP,KAAK;AACL,GAAG;AACH,EAAE,OAAO,MAAM,CAAC;AAChB;;;;"}