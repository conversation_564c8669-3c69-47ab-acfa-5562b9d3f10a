{"version": 3, "file": "omit.cjs", "sources": ["../../../src/helpers/omit.ts"], "sourcesContent": ["export const omit =\n  <T extends object, K extends string>(keys: readonly K[]) =>\n  (obj: T): Omit<T, K> => {\n    const result = {} as Omit<T, K>;\n\n    for (const key in obj) {\n      // @ts-expect-error - bypass\n      if (keys.includes(key)) {\n        continue;\n      }\n      // @ts-expect-error - bypass\n      result[key] = obj[key];\n    }\n\n    return result;\n  };\n"], "names": [], "mappings": ";;AACY,MAAC,IAAI,GAAG,CAAC,IAAI,KAAK,CAAC,GAAG,KAAK;AACvC,EAAE,MAAM,MAAM,GAAG,EAAE,CAAC;AACpB,EAAE,KAAK,MAAM,GAAG,IAAI,GAAG,EAAE;AACzB,IAAI,IAAI,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE;AAC5B,MAAM,SAAS;AACf,KAAK;AACL,IAAI,MAAM,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC;AAC3B,GAAG;AACH,EAAE,OAAO,MAAM,CAAC;AAChB;;;;"}