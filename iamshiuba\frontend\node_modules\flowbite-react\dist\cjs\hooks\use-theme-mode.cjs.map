{"version": 3, "file": "use-theme-mode.cjs", "sources": ["../../../src/hooks/use-theme-mode.ts"], "sourcesContent": ["\"use client\";\n\nimport { useEffect, useState } from \"react\";\nimport { isClient } from \"../helpers/is-client\";\nimport { useWatchLocalStorageValue } from \"../hooks/use-watch-localstorage-value\";\nimport { getThemeMode } from \"../theme-store\";\n\nconst DEFAULT_MODE: ThemeMode = \"light\";\nconst LS_THEME_MODE = \"flowbite-theme-mode\";\nconst SYNC_THEME_MODE = \"flowbite-theme-mode-sync\";\n\nexport type ThemeMode = \"light\" | \"dark\" | \"auto\";\n\nexport const useThemeMode = () => {\n  const [mode, setMode] = useState<ThemeMode>(getInitialMode(getThemeMode()));\n\n  /**\n   * Persist `mode` in local storage and add/remove `dark` class on `html`\n   */\n  useEffect(() => {\n    setModeInLS(mode);\n    setModeInDOM(mode);\n  }, []);\n\n  /**\n   * Sync all tabs with the latest theme mode value\n   */\n  useWatchLocalStorageValue({\n    key: LS_THEME_MODE,\n    onChange(newValue) {\n      if (newValue) return handleSetMode(newValue as ThemeMode);\n    },\n  });\n\n  /**\n   * Keep the other instances of the hook in sync (bi-directional)\n   */\n  useSyncMode((mode) => setMode(mode));\n\n  /**\n   * Sets `mode` to a given value: `light | dark` | `auto`\n   */\n  const handleSetMode = (mode: ThemeMode) => {\n    setMode(mode);\n    setModeInLS(mode);\n    setModeInDOM(mode);\n    document.dispatchEvent(new CustomEvent(SYNC_THEME_MODE, { detail: mode }));\n  };\n\n  /**\n   * Toggles between: `light | dark`\n   */\n  const toggleMode = () => {\n    let newMode = mode;\n\n    if (newMode === \"auto\") newMode = computeModeValue(newMode);\n\n    newMode = newMode === \"dark\" ? \"light\" : \"dark\";\n\n    handleSetMode(newMode);\n  };\n\n  /**\n   * Sets the value to `<Flowbite theme={{ mode }}>` prop\n   */\n  const clearMode = () => {\n    const newMode = getThemeMode() ?? DEFAULT_MODE;\n\n    handleSetMode(newMode);\n  };\n\n  return { mode, computedMode: computeModeValue(mode), setMode: handleSetMode, toggleMode, clearMode };\n};\n\n/**\n * Custom event listener on `SYNC_THEME_MODE`\n */\nconst useSyncMode = (onChange: (mode: ThemeMode) => void) => {\n  useEffect(() => {\n    function handleSync(e: Event) {\n      const mode = (e as CustomEvent<ThemeMode>).detail;\n\n      onChange(mode);\n    }\n\n    document.addEventListener(SYNC_THEME_MODE, handleSync);\n    return () => document.removeEventListener(SYNC_THEME_MODE, handleSync);\n  }, []);\n};\n\n/**\n * Sets the give value in local storage\n */\nconst setModeInLS = (mode: ThemeMode) => localStorage.setItem(LS_THEME_MODE, mode);\n\n/**\n * Add or remove class `dark` on `html` element\n */\nconst setModeInDOM = (mode: ThemeMode) => {\n  const computedMode = computeModeValue(mode);\n\n  if (computedMode === \"dark\") {\n    document.documentElement.classList.add(\"dark\");\n  } else {\n    document.documentElement.classList.remove(\"dark\");\n  }\n};\n\nconst getInitialMode = (defaultMode?: ThemeMode): ThemeMode => {\n  if (!isClient()) return DEFAULT_MODE;\n\n  const LSMode = localStorage.getItem(LS_THEME_MODE) as ThemeMode | undefined;\n\n  return LSMode ?? defaultMode ?? DEFAULT_MODE;\n};\n\n/**\n * Parse `auto` mode value to either `light` or `dark`\n * @returns `light` | `dark`\n */\nconst computeModeValue = (mode: ThemeMode): ThemeMode => {\n  return mode === \"auto\" ? prefersColorScheme() : mode;\n};\n\n/**\n * Get browser prefered color scheme\n * @returns `light` | `dark`\n */\nconst prefersColorScheme = (): ThemeMode => {\n  return window.matchMedia?.(\"(prefers-color-scheme: dark)\").matches ? \"dark\" : \"light\";\n};\n"], "names": ["useState", "getThemeMode", "useEffect", "useWatchLocalStorageValue", "isClient"], "mappings": ";;;;;;;AAMA,MAAM,YAAY,GAAG,OAAO,CAAC;AAC7B,MAAM,aAAa,GAAG,qBAAqB,CAAC;AAC5C,MAAM,eAAe,GAAG,0BAA0B,CAAC;AACvC,MAAC,YAAY,GAAG,MAAM;AAClC,EAAE,MAAM,CAAC,IAAI,EAAE,OAAO,CAAC,GAAGA,cAAQ,CAAC,cAAc,CAACC,kBAAY,EAAE,CAAC,CAAC,CAAC;AACnE,EAAEC,eAAS,CAAC,MAAM;AAClB,IAAI,WAAW,CAAC,IAAI,CAAC,CAAC;AACtB,IAAI,YAAY,CAAC,IAAI,CAAC,CAAC;AACvB,GAAG,EAAE,EAAE,CAAC,CAAC;AACT,EAAEC,mDAAyB,CAAC;AAC5B,IAAI,GAAG,EAAE,aAAa;AACtB,IAAI,QAAQ,CAAC,QAAQ,EAAE;AACvB,MAAM,IAAI,QAAQ,EAAE,OAAO,aAAa,CAAC,QAAQ,CAAC,CAAC;AACnD,KAAK;AACL,GAAG,CAAC,CAAC;AACL,EAAE,WAAW,CAAC,CAAC,KAAK,KAAK,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC;AACzC,EAAE,MAAM,aAAa,GAAG,CAAC,KAAK,KAAK;AACnC,IAAI,OAAO,CAAC,KAAK,CAAC,CAAC;AACnB,IAAI,WAAW,CAAC,KAAK,CAAC,CAAC;AACvB,IAAI,YAAY,CAAC,KAAK,CAAC,CAAC;AACxB,IAAI,QAAQ,CAAC,aAAa,CAAC,IAAI,WAAW,CAAC,eAAe,EAAE,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC;AAChF,GAAG,CAAC;AACJ,EAAE,MAAM,UAAU,GAAG,MAAM;AAC3B,IAAI,IAAI,OAAO,GAAG,IAAI,CAAC;AACvB,IAAI,IAAI,OAAO,KAAK,MAAM,EAAE,OAAO,GAAG,gBAAgB,CAAC,OAAO,CAAC,CAAC;AAChE,IAAI,OAAO,GAAG,OAAO,KAAK,MAAM,GAAG,OAAO,GAAG,MAAM,CAAC;AACpD,IAAI,aAAa,CAAC,OAAO,CAAC,CAAC;AAC3B,GAAG,CAAC;AACJ,EAAE,MAAM,SAAS,GAAG,MAAM;AAC1B,IAAI,MAAM,OAAO,GAAGF,kBAAY,EAAE,IAAI,YAAY,CAAC;AACnD,IAAI,aAAa,CAAC,OAAO,CAAC,CAAC;AAC3B,GAAG,CAAC;AACJ,EAAE,OAAO,EAAE,IAAI,EAAE,YAAY,EAAE,gBAAgB,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,aAAa,EAAE,UAAU,EAAE,SAAS,EAAE,CAAC;AACvG,EAAE;AACF,MAAM,WAAW,GAAG,CAAC,QAAQ,KAAK;AAClC,EAAEC,eAAS,CAAC,MAAM;AAClB,IAAI,SAAS,UAAU,CAAC,CAAC,EAAE;AAC3B,MAAM,MAAM,IAAI,GAAG,CAAC,CAAC,MAAM,CAAC;AAC5B,MAAM,QAAQ,CAAC,IAAI,CAAC,CAAC;AACrB,KAAK;AACL,IAAI,QAAQ,CAAC,gBAAgB,CAAC,eAAe,EAAE,UAAU,CAAC,CAAC;AAC3D,IAAI,OAAO,MAAM,QAAQ,CAAC,mBAAmB,CAAC,eAAe,EAAE,UAAU,CAAC,CAAC;AAC3E,GAAG,EAAE,EAAE,CAAC,CAAC;AACT,CAAC,CAAC;AACF,MAAM,WAAW,GAAG,CAAC,IAAI,KAAK,YAAY,CAAC,OAAO,CAAC,aAAa,EAAE,IAAI,CAAC,CAAC;AACxE,MAAM,YAAY,GAAG,CAAC,IAAI,KAAK;AAC/B,EAAE,MAAM,YAAY,GAAG,gBAAgB,CAAC,IAAI,CAAC,CAAC;AAC9C,EAAE,IAAI,YAAY,KAAK,MAAM,EAAE;AAC/B,IAAI,QAAQ,CAAC,eAAe,CAAC,SAAS,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;AACnD,GAAG,MAAM;AACT,IAAI,QAAQ,CAAC,eAAe,CAAC,SAAS,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;AACtD,GAAG;AACH,CAAC,CAAC;AACF,MAAM,cAAc,GAAG,CAAC,WAAW,KAAK;AACxC,EAAE,IAAI,CAACE,iBAAQ,EAAE,EAAE,OAAO,YAAY,CAAC;AACvC,EAAE,MAAM,MAAM,GAAG,YAAY,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;AACrD,EAAE,OAAO,MAAM,IAAI,WAAW,IAAI,YAAY,CAAC;AAC/C,CAAC,CAAC;AACF,MAAM,gBAAgB,GAAG,CAAC,IAAI,KAAK;AACnC,EAAE,OAAO,IAAI,KAAK,MAAM,GAAG,kBAAkB,EAAE,GAAG,IAAI,CAAC;AACvD,CAAC,CAAC;AACF,MAAM,kBAAkB,GAAG,MAAM;AACjC,EAAE,OAAO,MAAM,CAAC,UAAU,GAAG,8BAA8B,CAAC,CAAC,OAAO,GAAG,MAAM,GAAG,OAAO,CAAC;AACxF,CAAC;;;;"}