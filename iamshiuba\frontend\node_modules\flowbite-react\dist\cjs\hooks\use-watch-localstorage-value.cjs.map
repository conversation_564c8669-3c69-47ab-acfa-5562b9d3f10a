{"version": 3, "file": "use-watch-localstorage-value.cjs", "sources": ["../../../src/hooks/use-watch-localstorage-value.ts"], "sourcesContent": ["\"use client\";\n\nimport { useEffect } from \"react\";\n\n/**\n * Triggers `onChange` when another browser tab instance mutates the LS value.\n */\nexport const useWatchLocalStorageValue = ({\n  key: watchKey,\n  onChange,\n}: {\n  key: string;\n  onChange(newValue: string | null): void;\n}) => {\n  function handleStorageChange({ key, newValue }: StorageEvent) {\n    if (key === watchKey) onChange(newValue);\n  }\n\n  useEffect(() => {\n    window.addEventListener(\"storage\", handleStorageChange);\n    return () => window.removeEventListener(\"storage\", handleStorageChange);\n  }, []);\n};\n"], "names": ["useEffect"], "mappings": ";;;;AAGY,MAAC,yBAAyB,GAAG,CAAC;AAC1C,EAAE,GAAG,EAAE,QAAQ;AACf,EAAE,QAAQ;AACV,CAAC,KAAK;AACN,EAAE,SAAS,mBAAmB,CAAC,EAAE,GAAG,EAAE,QAAQ,EAAE,EAAE;AAClD,IAAI,IAAI,GAAG,KAAK,QAAQ,EAAE,QAAQ,CAAC,QAAQ,CAAC,CAAC;AAC7C,GAAG;AACH,EAAEA,eAAS,CAAC,MAAM;AAClB,IAAI,MAAM,CAAC,gBAAgB,CAAC,SAAS,EAAE,mBAAmB,CAAC,CAAC;AAC5D,IAAI,OAAO,MAAM,MAAM,CAAC,mBAAmB,CAAC,SAAS,EAAE,mBAAmB,CAAC,CAAC;AAC5E,GAAG,EAAE,EAAE,CAAC,CAAC;AACT;;;;"}