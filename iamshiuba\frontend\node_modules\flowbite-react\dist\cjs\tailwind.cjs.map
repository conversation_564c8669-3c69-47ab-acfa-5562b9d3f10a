{"version": 3, "file": "tailwind.cjs", "sources": ["../../src/tailwind.ts"], "sourcesContent": ["import flowbitePlugin from \"flowbite/plugin\";\n\ninterface Content {\n  /**\n   * Path to `node_modules` where `flowbite-react` is installed\n   *\n   * ===============================================\n   *\n   * For monorepo setup where `flowbite-react` is installed in the root `node_modules` but used in `apps/web` directory\n   * @example\n   * ```\n   * // tailwind.config.(js|cjs|mjs) file\n   *\n   * // cjs\n   * const flowbite = require(\"flowbite-react/tailwind\");\n   * // esm\n   * import flowbite from \"flowbite-react/tailwind\";\n   *\n   * {\n   *   content: [\n   *     // ...\n   *     flowbite.content({ base: \"../../\" })\n   *   ],\n   *   plugins: [\n   *     // ...\n   *     flowbite.plugin()\n   *   ]\n   * }\n   * ```\n   *\n   * @default \"./\"\n   */\n  base?: string;\n}\n\nexport function content({ base = \"./\" }: Content = {}) {\n  const path = \"node_modules/flowbite-react/dist/esm/**/*.mjs\";\n\n  return `${base}${path}`;\n}\n\nexport function plugin() {\n  return flowbitePlugin;\n}\n"], "names": [], "mappings": ";;;;AAEO,SAAS,OAAO,CAAC,EAAE,IAAI,GAAG,IAAI,EAAE,GAAG,EAAE,EAAE;AAC9C,EAAE,MAAM,IAAI,GAAG,+CAA+C,CAAC;AAC/D,EAAE,OAAO,CAAC,EAAE,IAAI,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;AAC1B,CAAC;AACM,SAAS,MAAM,GAAG;AACzB,EAAE,OAAO,cAAc,CAAC;AACxB;;;;;"}