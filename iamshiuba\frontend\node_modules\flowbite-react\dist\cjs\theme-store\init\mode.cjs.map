{"version": 3, "file": "mode.cjs", "sources": ["../../../../src/theme-store/init/mode.tsx"], "sourcesContent": ["\"use client\";\n\nimport { setThemeMode } from \"..\";\nimport { useThemeMode, type ThemeMode } from \"../../hooks/use-theme-mode\";\n\ninterface Props {\n  mode?: ThemeMode;\n}\n\nexport function ThemeModeInit({ mode }: Props) {\n  if (mode) setThemeMode(mode);\n\n  useThemeMode();\n\n  return null;\n}\n"], "names": ["setThemeMode", "useThemeMode"], "mappings": ";;;;;AAIO,SAAS,aAAa,CAAC,EAAE,IAAI,EAAE,EAAE;AACxC,EAAE,IAAI,IAAI,EAAEA,kBAAY,CAAC,IAAI,CAAC,CAAC;AAC/B,EAAEC,yBAAY,EAAE,CAAC;AACjB,EAAE,OAAO,IAAI,CAAC;AACd;;;;"}