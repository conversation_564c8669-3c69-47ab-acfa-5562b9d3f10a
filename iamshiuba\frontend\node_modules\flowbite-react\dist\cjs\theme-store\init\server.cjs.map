{"version": 3, "file": "server.cjs", "sources": ["../../../../src/theme-store/init/server.tsx"], "sourcesContent": ["import { setTheme } from \"..\";\nimport type { CustomFlowbiteTheme } from \"../../components/Flowbite\";\n\ninterface Props {\n  theme?: CustomFlowbiteTheme;\n}\n\nexport function ThemeServerInit({ theme }: Props) {\n  setTheme(theme);\n\n  return null;\n}\n"], "names": ["setTheme"], "mappings": ";;;;AAEO,SAAS,eAAe,CAAC,EAAE,KAAK,EAAE,EAAE;AAC3C,EAAEA,cAAQ,CAAC,KAAK,CAAC,CAAC;AAClB,EAAE,OAAO,IAAI,CAAC;AACd;;;;"}