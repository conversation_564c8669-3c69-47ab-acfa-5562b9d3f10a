{"version": 3, "file": "theme.cjs", "sources": ["../../src/theme.ts"], "sourcesContent": ["import type { FlowbiteTheme } from \".\";\nimport { accordionTheme } from \"./components/Accordion/theme\";\nimport { alertTheme } from \"./components/Alert/theme\";\nimport { avatarTheme } from \"./components/Avatar/theme\";\nimport { badgeTheme } from \"./components/Badge/theme\";\nimport { blockquoteTheme } from \"./components/Blockquote/theme\";\nimport { breadcrumbTheme } from \"./components/Breadcrumb/theme\";\nimport { buttonGroupTheme, buttonTheme } from \"./components/Button/theme\";\nimport { cardTheme } from \"./components/Card/theme\";\nimport { carouselTheme } from \"./components/Carousel/theme\";\nimport { checkboxTheme } from \"./components/Checkbox/theme\";\nimport { clipboardTheme } from \"./components/Clipboard/theme\";\nimport { darkThemeToggleTheme } from \"./components/DarkThemeToggle/theme\";\nimport { datePickerTheme } from \"./components/Datepicker/theme\";\nimport { drawerTheme } from \"./components/Drawer/theme\";\nimport { dropdownTheme } from \"./components/Dropdown/theme\";\nimport { fileInputTheme } from \"./components/FileInput/theme\";\nimport { floatingLabelTheme } from \"./components/FloatingLabel/theme\";\nimport { footerTheme } from \"./components/Footer/theme\";\nimport { helperTextTheme } from \"./components/HelperText/theme\";\nimport { hrTheme } from \"./components/HR/theme\";\nimport { kbdTheme } from \"./components/Kbd/theme\";\nimport { labelTheme } from \"./components/Label/theme\";\nimport { listTheme } from \"./components/List/theme\";\nimport { listGroupTheme } from \"./components/ListGroup/theme\";\nimport { megaMenuTheme } from \"./components/MegaMenu/theme\";\nimport { modalTheme } from \"./components/Modal/theme\";\nimport { navbarTheme } from \"./components/Navbar/theme\";\nimport { paginationTheme } from \"./components/Pagination/theme\";\nimport { popoverTheme } from \"./components/Popover/theme\";\nimport { progressTheme } from \"./components/Progress/theme\";\nimport { radioTheme } from \"./components/Radio/theme\";\nimport { rangeSliderTheme } from \"./components/RangeSlider/theme\";\nimport { ratingAdvancedTheme, ratingTheme } from \"./components/Rating/theme\";\nimport { selectTheme } from \"./components/Select/theme\";\nimport { sidebarTheme } from \"./components/Sidebar/theme\";\nimport { spinnerTheme } from \"./components/Spinner/theme\";\nimport { tableTheme } from \"./components/Table/theme\";\nimport { tabTheme } from \"./components/Tabs/theme\";\nimport { textareaTheme } from \"./components/Textarea/theme\";\nimport { textInputTheme } from \"./components/TextInput/theme\";\nimport { timelineTheme } from \"./components/Timeline/theme\";\nimport { toastTheme } from \"./components/Toast/theme\";\nimport { toggleSwitchTheme } from \"./components/ToggleSwitch/theme\";\nimport { tooltipTheme } from \"./components/Tooltip/theme\";\n\nexport const theme: FlowbiteTheme = {\n  accordion: accordionTheme,\n  alert: alertTheme,\n  avatar: avatarTheme,\n  badge: badgeTheme,\n  blockquote: blockquoteTheme,\n  breadcrumb: breadcrumbTheme,\n  button: buttonTheme,\n  buttonGroup: buttonGroupTheme,\n  card: cardTheme,\n  carousel: carouselTheme,\n  checkbox: checkboxTheme,\n  clipboard: clipboardTheme,\n  datepicker: datePickerTheme,\n  darkThemeToggle: darkThemeToggleTheme,\n  drawer: drawerTheme,\n  dropdown: dropdownTheme,\n  fileInput: fileInputTheme,\n  floatingLabel: floatingLabelTheme,\n  footer: footerTheme,\n  helperText: helperTextTheme,\n  hr: hrTheme,\n  kbd: kbdTheme,\n  label: labelTheme,\n  listGroup: listGroupTheme,\n  list: listTheme,\n  megaMenu: megaMenuTheme,\n  modal: modalTheme,\n  navbar: navbarTheme,\n  pagination: paginationTheme,\n  popover: popoverTheme,\n  progress: progressTheme,\n  radio: radioTheme,\n  rangeSlider: rangeSliderTheme,\n  rating: ratingTheme,\n  ratingAdvanced: ratingAdvancedTheme,\n  select: selectTheme,\n  textInput: textInputTheme,\n  textarea: textareaTheme,\n  toggleSwitch: toggleSwitchTheme,\n  sidebar: sidebarTheme,\n  spinner: spinnerTheme,\n  table: tableTheme,\n  tabs: tabTheme,\n  timeline: timelineTheme,\n  toast: toastTheme,\n  tooltip: tooltipTheme,\n};\n"], "names": ["accordionTheme", "alertTheme", "avatar<PERSON><PERSON>e", "badgeTheme", "blockquoteTheme", "breadcrumbTheme", "buttonTheme", "buttonGroupTheme", "cardTheme", "carouselTheme", "checkboxTheme", "clipboardTheme", "datePickerTheme", "darkThemeToggleTheme", "drawerTheme", "dropdownTheme", "fileInputTheme", "floatingLabelTheme", "footerTheme", "helperTextTheme", "hrTheme", "kbdTheme", "labelTheme", "listGroupTheme", "listTheme", "megaMenuTheme", "modalTheme", "navbarTheme", "paginationTheme", "popoverTheme", "progressTheme", "radioTheme", "rangeSliderTheme", "ratingTheme", "ratingAdvancedTheme", "selectTheme", "textInputTheme", "textareaTheme", "toggleSwitchTheme", "sidebarTheme", "spinnerTheme", "tableTheme", "tabTheme", "timelineTheme", "toastTheme", "tooltipTheme"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6CY,MAAC,KAAK,GAAG;AACrB,EAAE,SAAS,EAAEA,sBAAc;AAC3B,EAAE,KAAK,EAAEC,kBAAU;AACnB,EAAE,MAAM,EAAEC,mBAAW;AACrB,EAAE,KAAK,EAAEC,kBAAU;AACnB,EAAE,UAAU,EAAEC,uBAAe;AAC7B,EAAE,UAAU,EAAEC,uBAAe;AAC7B,EAAE,MAAM,EAAEC,mBAAW;AACrB,EAAE,WAAW,EAAEC,wBAAgB;AAC/B,EAAE,IAAI,EAAEC,iBAAS;AACjB,EAAE,QAAQ,EAAEC,qBAAa;AACzB,EAAE,QAAQ,EAAEC,qBAAa;AACzB,EAAE,SAAS,EAAEC,sBAAc;AAC3B,EAAE,UAAU,EAAEC,uBAAe;AAC7B,EAAE,eAAe,EAAEC,4BAAoB;AACvC,EAAE,MAAM,EAAEC,mBAAW;AACrB,EAAE,QAAQ,EAAEC,qBAAa;AACzB,EAAE,SAAS,EAAEC,sBAAc;AAC3B,EAAE,aAAa,EAAEC,0BAAkB;AACnC,EAAE,MAAM,EAAEC,mBAAW;AACrB,EAAE,UAAU,EAAEC,uBAAe;AAC7B,EAAE,EAAE,EAAEC,eAAO;AACb,EAAE,GAAG,EAAEC,gBAAQ;AACf,EAAE,KAAK,EAAEC,kBAAU;AACnB,EAAE,SAAS,EAAEC,sBAAc;AAC3B,EAAE,IAAI,EAAEC,iBAAS;AACjB,EAAE,QAAQ,EAAEC,qBAAa;AACzB,EAAE,KAAK,EAAEC,kBAAU;AACnB,EAAE,MAAM,EAAEC,mBAAW;AACrB,EAAE,UAAU,EAAEC,uBAAe;AAC7B,EAAE,OAAO,EAAEC,oBAAY;AACvB,EAAE,QAAQ,EAAEC,qBAAa;AACzB,EAAE,KAAK,EAAEC,kBAAU;AACnB,EAAE,WAAW,EAAEC,wBAAgB;AAC/B,EAAE,MAAM,EAAEC,mBAAW;AACrB,EAAE,cAAc,EAAEC,2BAAmB;AACrC,EAAE,MAAM,EAAEC,mBAAW;AACrB,EAAE,SAAS,EAAEC,sBAAc;AAC3B,EAAE,QAAQ,EAAEC,qBAAa;AACzB,EAAE,YAAY,EAAEC,yBAAiB;AACjC,EAAE,OAAO,EAAEC,oBAAY;AACvB,EAAE,OAAO,EAAEC,oBAAY;AACvB,EAAE,KAAK,EAAEC,kBAAU;AACnB,EAAE,IAAI,EAAEC,gBAAQ;AAChB,EAAE,QAAQ,EAAEC,qBAAa;AACzB,EAAE,KAAK,EAAEC,kBAAU;AACnB,EAAE,OAAO,EAAEC,oBAAY;AACvB;;;;"}