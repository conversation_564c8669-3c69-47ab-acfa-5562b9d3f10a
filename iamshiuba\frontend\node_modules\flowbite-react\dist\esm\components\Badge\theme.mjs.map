{"version": 3, "file": "theme.mjs", "sources": ["../../../../src/components/Badge/theme.ts"], "sourcesContent": ["import { createTheme } from \"../../helpers/create-theme\";\nimport type { FlowbiteBadgeTheme } from \"./Badge\";\n\nexport const badgeTheme: FlowbiteBadgeTheme = createTheme({\n  root: {\n    base: \"flex h-fit items-center gap-1 font-semibold\",\n    color: {\n      info: \"bg-cyan-100 text-cyan-800 group-hover:bg-cyan-200 dark:bg-cyan-200 dark:text-cyan-800 dark:group-hover:bg-cyan-300\",\n      gray: \"bg-gray-100 text-gray-800 group-hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-300 dark:group-hover:bg-gray-600\",\n      failure:\n        \"bg-red-100 text-red-800 group-hover:bg-red-200 dark:bg-red-200 dark:text-red-900 dark:group-hover:bg-red-300\",\n      success:\n        \"bg-green-100 text-green-800 group-hover:bg-green-200 dark:bg-green-200 dark:text-green-900 dark:group-hover:bg-green-300\",\n      warning:\n        \"bg-yellow-100 text-yellow-800 group-hover:bg-yellow-200 dark:bg-yellow-200 dark:text-yellow-900 dark:group-hover:bg-yellow-300\",\n      indigo:\n        \"bg-indigo-100 text-indigo-800 group-hover:bg-indigo-200 dark:bg-indigo-200 dark:text-indigo-900 dark:group-hover:bg-indigo-300\",\n      purple:\n        \"bg-purple-100 text-purple-800 group-hover:bg-purple-200 dark:bg-purple-200 dark:text-purple-900 dark:group-hover:bg-purple-300\",\n      pink: \"bg-pink-100 text-pink-800 group-hover:bg-pink-200 dark:bg-pink-200 dark:text-pink-900 dark:group-hover:bg-pink-300\",\n      blue: \"bg-blue-100 text-blue-800 group-hover:bg-blue-200 dark:bg-blue-200 dark:text-blue-900 dark:group-hover:bg-blue-300\",\n      cyan: \"bg-cyan-100 text-cyan-800 group-hover:bg-cyan-200 dark:bg-cyan-200 dark:text-cyan-900 dark:group-hover:bg-cyan-300\",\n      dark: \"bg-gray-600 text-gray-100 group-hover:bg-gray-500 dark:bg-gray-900 dark:text-gray-200 dark:group-hover:bg-gray-700\",\n      light:\n        \"bg-gray-200 text-gray-800 group-hover:bg-gray-300 dark:bg-gray-400 dark:text-gray-900 dark:group-hover:bg-gray-500\",\n      green:\n        \"bg-green-100 text-green-800 group-hover:bg-green-200 dark:bg-green-200 dark:text-green-900 dark:group-hover:bg-green-300\",\n      lime: \"bg-lime-100 text-lime-800 group-hover:bg-lime-200 dark:bg-lime-200 dark:text-lime-900 dark:group-hover:bg-lime-300\",\n      red: \"bg-red-100 text-red-800 group-hover:bg-red-200 dark:bg-red-200 dark:text-red-900 dark:group-hover:bg-red-300\",\n      teal: \"bg-teal-100 text-teal-800 group-hover:bg-teal-200 dark:bg-teal-200 dark:text-teal-900 dark:group-hover:bg-teal-300\",\n      yellow:\n        \"bg-yellow-100 text-yellow-800 group-hover:bg-yellow-200 dark:bg-yellow-200 dark:text-yellow-900 dark:group-hover:bg-yellow-300\",\n    },\n    href: \"group\",\n    size: {\n      xs: \"p-1 text-xs\",\n      sm: \"p-1.5 text-sm\",\n    },\n  },\n  icon: {\n    off: \"rounded px-2 py-0.5\",\n    on: \"rounded-full p-1.5\",\n    size: {\n      xs: \"h-3 w-3\",\n      sm: \"h-3.5 w-3.5\",\n    },\n  },\n});\n"], "names": [], "mappings": ";;AAEY,MAAC,UAAU,GAAG,WAAW,CAAC;AACtC,EAAE,IAAI,EAAE;AACR,IAAI,IAAI,EAAE,6CAA6C;AACvD,IAAI,KAAK,EAAE;AACX,MAAM,IAAI,EAAE,oHAAoH;AAChI,MAAM,IAAI,EAAE,oHAAoH;AAChI,MAAM,OAAO,EAAE,8GAA8G;AAC7H,MAAM,OAAO,EAAE,0HAA0H;AACzI,MAAM,OAAO,EAAE,gIAAgI;AAC/I,MAAM,MAAM,EAAE,gIAAgI;AAC9I,MAAM,MAAM,EAAE,gIAAgI;AAC9I,MAAM,IAAI,EAAE,oHAAoH;AAChI,MAAM,IAAI,EAAE,oHAAoH;AAChI,MAAM,IAAI,EAAE,oHAAoH;AAChI,MAAM,IAAI,EAAE,oHAAoH;AAChI,MAAM,KAAK,EAAE,oHAAoH;AACjI,MAAM,KAAK,EAAE,0HAA0H;AACvI,MAAM,IAAI,EAAE,oHAAoH;AAChI,MAAM,GAAG,EAAE,8GAA8G;AACzH,MAAM,IAAI,EAAE,oHAAoH;AAChI,MAAM,MAAM,EAAE,gIAAgI;AAC9I,KAAK;AACL,IAAI,IAAI,EAAE,OAAO;AACjB,IAAI,IAAI,EAAE;AACV,MAAM,EAAE,EAAE,aAAa;AACvB,MAAM,EAAE,EAAE,eAAe;AACzB,KAAK;AACL,GAAG;AACH,EAAE,IAAI,EAAE;AACR,IAAI,GAAG,EAAE,qBAAqB;AAC9B,IAAI,EAAE,EAAE,oBAAoB;AAC5B,IAAI,IAAI,EAAE;AACV,MAAM,EAAE,EAAE,SAAS;AACnB,MAAM,EAAE,EAAE,aAAa;AACvB,KAAK;AACL,GAAG;AACH,CAAC;;;;"}