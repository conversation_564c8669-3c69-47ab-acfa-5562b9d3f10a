{"version": 3, "file": "theme.mjs", "sources": ["../../../../src/components/Blockquote/theme.ts"], "sourcesContent": ["import { createTheme } from \"../../helpers/create-theme\";\nimport type { FlowbiteBlockquoteTheme } from \"./Blockquote\";\n\nexport const blockquoteTheme: FlowbiteBlockquoteTheme = createTheme({\n  root: {\n    base: \"text-xl font-semibold italic text-gray-900 dark:text-white\",\n  },\n});\n"], "names": [], "mappings": ";;AAEY,MAAC,eAAe,GAAG,WAAW,CAAC;AAC3C,EAAE,IAAI,EAAE;AACR,IAAI,IAAI,EAAE,4DAA4D;AACtE,GAAG;AACH,CAAC;;;;"}