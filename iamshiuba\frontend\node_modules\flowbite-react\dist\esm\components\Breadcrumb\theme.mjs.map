{"version": 3, "file": "theme.mjs", "sources": ["../../../../src/components/Breadcrumb/theme.ts"], "sourcesContent": ["import { createTheme } from \"../../helpers/create-theme\";\nimport type { FlowbiteBreadcrumbTheme } from \"./Breadcrumb\";\n\nexport const breadcrumbTheme: FlowbiteBreadcrumbTheme = createTheme({\n  root: {\n    base: \"\",\n    list: \"flex items-center\",\n  },\n  item: {\n    base: \"group flex items-center\",\n    chevron: \"mx-1 h-4 w-4 text-gray-400 group-first:hidden md:mx-2\",\n    href: {\n      off: \"flex items-center text-sm font-medium text-gray-500 dark:text-gray-400\",\n      on: \"flex items-center text-sm font-medium text-gray-700 hover:text-gray-900 dark:text-gray-400 dark:hover:text-white\",\n    },\n    icon: \"mr-2 h-4 w-4\",\n  },\n});\n"], "names": [], "mappings": ";;AAEY,MAAC,eAAe,GAAG,WAAW,CAAC;AAC3C,EAAE,IAAI,EAAE;AACR,IAAI,IAAI,EAAE,EAAE;AACZ,IAAI,IAAI,EAAE,mBAAmB;AAC7B,GAAG;AACH,EAAE,IAAI,EAAE;AACR,IAAI,IAAI,EAAE,yBAAyB;AACnC,IAAI,OAAO,EAAE,uDAAuD;AACpE,IAAI,IAAI,EAAE;AACV,MAAM,GAAG,EAAE,wEAAwE;AACnF,MAAM,EAAE,EAAE,kHAAkH;AAC5H,KAAK;AACL,IAAI,IAAI,EAAE,cAAc;AACxB,GAAG;AACH,CAAC;;;;"}