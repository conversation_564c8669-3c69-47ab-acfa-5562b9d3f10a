{"version": 3, "file": "theme.mjs", "sources": ["../../../../src/components/Button/theme.ts"], "sourcesContent": ["import { createTheme } from \"../../helpers/create-theme\";\nimport type { FlowbiteButtonTheme } from \"./Button\";\nimport type { FlowbiteButtonGroupTheme } from \"./ButtonGroup\";\n\nexport const buttonTheme: FlowbiteButtonTheme = createTheme({\n  base: \"group relative flex items-stretch justify-center p-0.5 text-center font-medium transition-[color,background-color,border-color,text-decoration-color,fill,stroke,box-shadow] focus:z-10 focus:outline-none\",\n  fullSized: \"w-full\",\n  color: {\n    dark: \"border border-transparent bg-gray-800 text-white focus:ring-4 focus:ring-gray-300 enabled:hover:bg-gray-900 dark:border-gray-700 dark:bg-gray-800 dark:focus:ring-gray-800 dark:enabled:hover:bg-gray-700\",\n    failure:\n      \"border border-transparent bg-red-700 text-white focus:ring-4 focus:ring-red-300 enabled:hover:bg-red-800 dark:bg-red-600 dark:focus:ring-red-900 dark:enabled:hover:bg-red-700\",\n    gray: \":ring-cyan-700 border border-gray-200 bg-white text-gray-900 focus:text-cyan-700 focus:ring-4 enabled:hover:bg-gray-100 enabled:hover:text-cyan-700 dark:border-gray-600 dark:bg-transparent dark:text-gray-400 dark:enabled:hover:bg-gray-700 dark:enabled:hover:text-white\",\n    info: \"border border-transparent bg-cyan-700 text-white focus:ring-4 focus:ring-cyan-300 enabled:hover:bg-cyan-800 dark:bg-cyan-600 dark:focus:ring-cyan-800 dark:enabled:hover:bg-cyan-700\",\n    light:\n      \"border border-gray-300 bg-white text-gray-900 focus:ring-4 focus:ring-cyan-300 enabled:hover:bg-gray-100 dark:border-gray-600 dark:bg-gray-600 dark:text-white dark:focus:ring-gray-700 dark:enabled:hover:border-gray-700 dark:enabled:hover:bg-gray-700\",\n    purple:\n      \"border border-transparent bg-purple-700 text-white focus:ring-4 focus:ring-purple-300 enabled:hover:bg-purple-800 dark:bg-purple-600 dark:focus:ring-purple-900 dark:enabled:hover:bg-purple-700\",\n    success:\n      \"border border-transparent bg-green-700 text-white focus:ring-4 focus:ring-green-300 enabled:hover:bg-green-800 dark:bg-green-600 dark:focus:ring-green-800 dark:enabled:hover:bg-green-700\",\n    warning:\n      \"border border-transparent bg-yellow-400 text-white focus:ring-4 focus:ring-yellow-300 enabled:hover:bg-yellow-500 dark:focus:ring-yellow-900\",\n    blue: \"border border-transparent bg-blue-700 text-white focus:ring-4 focus:ring-blue-300 enabled:hover:bg-blue-800 dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800\",\n    cyan: \"border border-cyan-300 bg-white text-cyan-900 focus:ring-4 focus:ring-cyan-300 enabled:hover:bg-cyan-100 dark:border-cyan-600 dark:bg-cyan-600 dark:text-white dark:focus:ring-cyan-700 dark:enabled:hover:border-cyan-700 dark:enabled:hover:bg-cyan-700\",\n    green:\n      \"border border-green-300 bg-white text-green-900 focus:ring-4 focus:ring-green-300 enabled:hover:bg-green-100 dark:border-green-600 dark:bg-green-600 dark:text-white dark:focus:ring-green-700 dark:enabled:hover:border-green-700 dark:enabled:hover:bg-green-700\",\n    indigo:\n      \"border border-indigo-300 bg-white text-indigo-900 focus:ring-4 focus:ring-indigo-300 enabled:hover:bg-indigo-100 dark:border-indigo-600 dark:bg-indigo-600 dark:text-white dark:focus:ring-indigo-700 dark:enabled:hover:border-indigo-700 dark:enabled:hover:bg-indigo-700\",\n    lime: \"border border-lime-300 bg-white text-lime-900 focus:ring-4 focus:ring-lime-300 enabled:hover:bg-lime-100 dark:border-lime-600 dark:bg-lime-600 dark:text-white dark:focus:ring-lime-700 dark:enabled:hover:border-lime-700 dark:enabled:hover:bg-lime-700\",\n    pink: \"border border-pink-300 bg-white text-pink-900 focus:ring-4 focus:ring-pink-300 enabled:hover:bg-pink-100 dark:border-pink-600 dark:bg-pink-600 dark:text-white dark:focus:ring-pink-700 dark:enabled:hover:border-pink-700 dark:enabled:hover:bg-pink-700\",\n    red: \"border border-red-300 bg-white text-red-900 focus:ring-4 focus:ring-red-300 enabled:hover:bg-red-100 dark:border-red-600 dark:bg-red-600 dark:text-white dark:focus:ring-red-700 dark:enabled:hover:border-red-700 dark:enabled:hover:bg-red-700\",\n    teal: \"border border-teal-300 bg-white text-teal-900 focus:ring-4 focus:ring-teal-300 enabled:hover:bg-teal-100 dark:border-teal-600 dark:bg-teal-600 dark:text-white dark:focus:ring-teal-700 dark:enabled:hover:border-teal-700 dark:enabled:hover:bg-teal-700\",\n    yellow:\n      \"border border-yellow-300 bg-white text-yellow-900 focus:ring-4 focus:ring-yellow-300 enabled:hover:bg-yellow-100 dark:border-yellow-600 dark:bg-yellow-600 dark:text-white dark:focus:ring-yellow-700 dark:enabled:hover:border-yellow-700 dark:enabled:hover:bg-yellow-700\",\n  },\n  disabled: \"cursor-not-allowed opacity-50\",\n  isProcessing: \"cursor-wait\",\n  spinnerSlot: \"absolute top-0 flex h-full items-center\",\n  spinnerLeftPosition: {\n    xs: \"left-2\",\n    sm: \"left-3\",\n    md: \"left-4\",\n    lg: \"left-5\",\n    xl: \"left-6\",\n  },\n  gradient: {\n    cyan: \"bg-gradient-to-r from-cyan-400 via-cyan-500 to-cyan-600 text-white focus:ring-4 focus:ring-cyan-300 enabled:hover:bg-gradient-to-br dark:focus:ring-cyan-800\",\n    failure:\n      \"bg-gradient-to-r from-red-400 via-red-500 to-red-600 text-white focus:ring-4 focus:ring-red-300 enabled:hover:bg-gradient-to-br dark:focus:ring-red-800\",\n    info: \"bg-gradient-to-r from-cyan-500 via-cyan-600 to-cyan-700 text-white focus:ring-4 focus:ring-cyan-300 enabled:hover:bg-gradient-to-br dark:focus:ring-cyan-800\",\n    lime: \"bg-gradient-to-r from-lime-200 via-lime-400 to-lime-500 text-gray-900 focus:ring-4 focus:ring-lime-300 enabled:hover:bg-gradient-to-br dark:focus:ring-lime-800\",\n\n    pink: \"bg-gradient-to-r from-pink-400 via-pink-500 to-pink-600 text-white focus:ring-4 focus:ring-pink-300 enabled:hover:bg-gradient-to-br dark:focus:ring-pink-800\",\n    purple:\n      \"bg-gradient-to-r from-purple-500 via-purple-600 to-purple-700 text-white focus:ring-4 focus:ring-purple-300 enabled:hover:bg-gradient-to-br dark:focus:ring-purple-800\",\n    success:\n      \"bg-gradient-to-r from-green-400 via-green-500 to-green-600 text-white focus:ring-4 focus:ring-green-300 enabled:hover:bg-gradient-to-br dark:focus:ring-green-800\",\n    teal: \"bg-gradient-to-r from-teal-400 via-teal-500 to-teal-600 text-white focus:ring-4 focus:ring-teal-300 enabled:hover:bg-gradient-to-br dark:focus:ring-teal-800\",\n  },\n  gradientDuoTone: {\n    cyanToBlue:\n      \"bg-gradient-to-r from-cyan-500 to-cyan-500 text-white focus:ring-4 focus:ring-cyan-300 enabled:hover:bg-gradient-to-bl dark:focus:ring-cyan-800\",\n    greenToBlue:\n      \"bg-gradient-to-br from-green-400 to-cyan-600 text-white focus:ring-4 focus:ring-green-200 enabled:hover:bg-gradient-to-bl dark:focus:ring-green-800\",\n    pinkToOrange:\n      \"bg-gradient-to-br from-pink-500 to-orange-400 text-white focus:ring-4 focus:ring-pink-200 enabled:hover:bg-gradient-to-bl dark:focus:ring-pink-800\",\n    purpleToBlue:\n      \"bg-gradient-to-br from-purple-600 to-cyan-500 text-white focus:ring-4 focus:ring-cyan-300 enabled:hover:bg-gradient-to-bl dark:focus:ring-cyan-800\",\n    purpleToPink:\n      \"bg-gradient-to-r from-purple-500 to-pink-500 text-white focus:ring-4 focus:ring-purple-200 enabled:hover:bg-gradient-to-l dark:focus:ring-purple-800\",\n    redToYellow:\n      \"bg-gradient-to-r from-red-200 via-red-300 to-yellow-200 text-gray-900 focus:ring-4 focus:ring-red-100 enabled:hover:bg-gradient-to-bl dark:focus:ring-red-400\",\n    tealToLime:\n      \"bg-gradient-to-r from-teal-200 to-lime-200 text-gray-900 focus:ring-4 focus:ring-lime-200 enabled:hover:bg-gradient-to-l enabled:hover:from-teal-200 enabled:hover:to-lime-200 enabled:hover:text-gray-900 dark:focus:ring-teal-700\",\n  },\n  inner: {\n    base: \"flex items-stretch transition-all duration-200\",\n    position: {\n      none: \"\",\n      start: \"rounded-r-none\",\n      middle: \"rounded-none\",\n      end: \"rounded-l-none\",\n    },\n    outline: \"border border-transparent\",\n    isProcessingPadding: {\n      xs: \"pl-8\",\n      sm: \"pl-10\",\n      md: \"pl-12\",\n      lg: \"pl-16\",\n      xl: \"pl-20\",\n    },\n  },\n  label:\n    \"ml-2 inline-flex h-4 w-4 items-center justify-center rounded-full bg-cyan-200 text-xs font-semibold text-cyan-800\",\n  outline: {\n    color: {\n      gray: \"border border-gray-900 dark:border-white\",\n      default: \"border-0\",\n      light: \"\",\n    },\n    off: \"\",\n    on: \"flex w-full justify-center bg-white text-gray-900 transition-all duration-75 ease-in group-enabled:group-hover:bg-opacity-0 group-enabled:group-hover:text-inherit dark:bg-gray-900 dark:text-white\",\n    pill: {\n      off: \"rounded-md\",\n      on: \"rounded-full\",\n    },\n  },\n  pill: {\n    off: \"rounded-lg\",\n    on: \"rounded-full\",\n  },\n  size: {\n    xs: \"px-2 py-1 text-xs\",\n    sm: \"px-3 py-1.5 text-sm\",\n    md: \"px-4 py-2 text-sm\",\n    lg: \"px-5 py-2.5 text-base\",\n    xl: \"px-6 py-3 text-base\",\n  },\n});\n\nexport const buttonGroupTheme: FlowbiteButtonGroupTheme = createTheme({\n  base: \"inline-flex\",\n  position: {\n    none: \"\",\n    start: \"rounded-r-none focus:ring-2\",\n    middle: \"rounded-none border-l-0 pl-0 focus:ring-2\",\n    end: \"rounded-l-none border-l-0 pl-0 focus:ring-2\",\n  },\n});\n"], "names": [], "mappings": ";;AAEY,MAAC,WAAW,GAAG,WAAW,CAAC;AACvC,EAAE,IAAI,EAAE,4MAA4M;AACpN,EAAE,SAAS,EAAE,QAAQ;AACrB,EAAE,KAAK,EAAE;AACT,IAAI,IAAI,EAAE,2MAA2M;AACrN,IAAI,OAAO,EAAE,gLAAgL;AAC7L,IAAI,IAAI,EAAE,8QAA8Q;AACxR,IAAI,IAAI,EAAE,sLAAsL;AAChM,IAAI,KAAK,EAAE,2PAA2P;AACtQ,IAAI,MAAM,EAAE,kMAAkM;AAC9M,IAAI,OAAO,EAAE,4LAA4L;AACzM,IAAI,OAAO,EAAE,8IAA8I;AAC3J,IAAI,IAAI,EAAE,8KAA8K;AACxL,IAAI,IAAI,EAAE,2PAA2P;AACrQ,IAAI,KAAK,EAAE,oQAAoQ;AAC/Q,IAAI,MAAM,EAAE,6QAA6Q;AACzR,IAAI,IAAI,EAAE,2PAA2P;AACrQ,IAAI,IAAI,EAAE,2PAA2P;AACrQ,IAAI,GAAG,EAAE,kPAAkP;AAC3P,IAAI,IAAI,EAAE,2PAA2P;AACrQ,IAAI,MAAM,EAAE,6QAA6Q;AACzR,GAAG;AACH,EAAE,QAAQ,EAAE,+BAA+B;AAC3C,EAAE,YAAY,EAAE,aAAa;AAC7B,EAAE,WAAW,EAAE,yCAAyC;AACxD,EAAE,mBAAmB,EAAE;AACvB,IAAI,EAAE,EAAE,QAAQ;AAChB,IAAI,EAAE,EAAE,QAAQ;AAChB,IAAI,EAAE,EAAE,QAAQ;AAChB,IAAI,EAAE,EAAE,QAAQ;AAChB,IAAI,EAAE,EAAE,QAAQ;AAChB,GAAG;AACH,EAAE,QAAQ,EAAE;AACZ,IAAI,IAAI,EAAE,8JAA8J;AACxK,IAAI,OAAO,EAAE,yJAAyJ;AACtK,IAAI,IAAI,EAAE,8JAA8J;AACxK,IAAI,IAAI,EAAE,iKAAiK;AAC3K,IAAI,IAAI,EAAE,8JAA8J;AACxK,IAAI,MAAM,EAAE,wKAAwK;AACpL,IAAI,OAAO,EAAE,mKAAmK;AAChL,IAAI,IAAI,EAAE,8JAA8J;AACxK,GAAG;AACH,EAAE,eAAe,EAAE;AACnB,IAAI,UAAU,EAAE,iJAAiJ;AACjK,IAAI,WAAW,EAAE,qJAAqJ;AACtK,IAAI,YAAY,EAAE,oJAAoJ;AACtK,IAAI,YAAY,EAAE,oJAAoJ;AACtK,IAAI,YAAY,EAAE,sJAAsJ;AACxK,IAAI,WAAW,EAAE,+JAA+J;AAChL,IAAI,UAAU,EAAE,qOAAqO;AACrP,GAAG;AACH,EAAE,KAAK,EAAE;AACT,IAAI,IAAI,EAAE,gDAAgD;AAC1D,IAAI,QAAQ,EAAE;AACd,MAAM,IAAI,EAAE,EAAE;AACd,MAAM,KAAK,EAAE,gBAAgB;AAC7B,MAAM,MAAM,EAAE,cAAc;AAC5B,MAAM,GAAG,EAAE,gBAAgB;AAC3B,KAAK;AACL,IAAI,OAAO,EAAE,2BAA2B;AACxC,IAAI,mBAAmB,EAAE;AACzB,MAAM,EAAE,EAAE,MAAM;AAChB,MAAM,EAAE,EAAE,OAAO;AACjB,MAAM,EAAE,EAAE,OAAO;AACjB,MAAM,EAAE,EAAE,OAAO;AACjB,MAAM,EAAE,EAAE,OAAO;AACjB,KAAK;AACL,GAAG;AACH,EAAE,KAAK,EAAE,mHAAmH;AAC5H,EAAE,OAAO,EAAE;AACX,IAAI,KAAK,EAAE;AACX,MAAM,IAAI,EAAE,0CAA0C;AACtD,MAAM,OAAO,EAAE,UAAU;AACzB,MAAM,KAAK,EAAE,EAAE;AACf,KAAK;AACL,IAAI,GAAG,EAAE,EAAE;AACX,IAAI,EAAE,EAAE,qMAAqM;AAC7M,IAAI,IAAI,EAAE;AACV,MAAM,GAAG,EAAE,YAAY;AACvB,MAAM,EAAE,EAAE,cAAc;AACxB,KAAK;AACL,GAAG;AACH,EAAE,IAAI,EAAE;AACR,IAAI,GAAG,EAAE,YAAY;AACrB,IAAI,EAAE,EAAE,cAAc;AACtB,GAAG;AACH,EAAE,IAAI,EAAE;AACR,IAAI,EAAE,EAAE,mBAAmB;AAC3B,IAAI,EAAE,EAAE,qBAAqB;AAC7B,IAAI,EAAE,EAAE,mBAAmB;AAC3B,IAAI,EAAE,EAAE,uBAAuB;AAC/B,IAAI,EAAE,EAAE,qBAAqB;AAC7B,GAAG;AACH,CAAC,EAAE;AACS,MAAC,gBAAgB,GAAG,WAAW,CAAC;AAC5C,EAAE,IAAI,EAAE,aAAa;AACrB,EAAE,QAAQ,EAAE;AACZ,IAAI,IAAI,EAAE,EAAE;AACZ,IAAI,KAAK,EAAE,6BAA6B;AACxC,IAAI,MAAM,EAAE,2CAA2C;AACvD,IAAI,GAAG,EAAE,6CAA6C;AACtD,GAAG;AACH,CAAC;;;;"}