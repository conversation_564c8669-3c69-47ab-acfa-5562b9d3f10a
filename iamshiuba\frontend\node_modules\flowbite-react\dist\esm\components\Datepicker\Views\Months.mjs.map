{"version": 3, "file": "Months.mjs", "sources": ["../../../../../src/components/Datepicker/Views/Months.tsx"], "sourcesContent": ["import type { FC } from \"react\";\nimport { twMerge } from \"tailwind-merge\";\nimport { mergeDeep } from \"../../../helpers/merge-deep\";\nimport { useDatePickerContext } from \"../DatepickerContext\";\nimport { getFormattedDate, isDateEqual, isDateInRange, Views } from \"../helpers\";\n\nexport interface FlowbiteDatepickerViewsMonthsTheme {\n  items: {\n    base: string;\n    item: {\n      base: string;\n      selected: string;\n      disabled: string;\n    };\n  };\n}\n\nexport interface DatepickerViewsMonthsProps {\n  theme?: FlowbiteDatepickerViewsMonthsTheme;\n}\n\nexport const DatepickerViewsMonth: FC<DatepickerViewsMonthsProps> = ({ theme: customTheme = {} }) => {\n  const {\n    theme: rootTheme,\n    minDate,\n    maxDate,\n    selectedDate,\n    viewDate,\n    language,\n    setViewDate,\n    setView,\n  } = useDatePickerContext();\n\n  const theme = mergeDeep(rootTheme.views.months, customTheme);\n\n  return (\n    <div className={theme.items.base}>\n      {[...Array(12)].map((_month, index) => {\n        const newDate = new Date();\n        // setting day to 1 to avoid overflow issues\n        newDate.setMonth(index, 1);\n        newDate.setFullYear(viewDate.getFullYear());\n        const month = getFormattedDate(language, newDate, { month: \"short\" });\n\n        const isSelected = selectedDate && isDateEqual(selectedDate, newDate);\n        const isDisabled = !isDateInRange(newDate, minDate, maxDate);\n\n        return (\n          <button\n            disabled={isDisabled}\n            key={index}\n            type=\"button\"\n            className={twMerge(\n              theme.items.item.base,\n              isSelected && theme.items.item.selected,\n              isDisabled && theme.items.item.disabled,\n            )}\n            onClick={() => {\n              if (isDisabled) return;\n\n              setViewDate(newDate);\n              setView(Views.Days);\n            }}\n          >\n            {month}\n          </button>\n        );\n      })}\n    </div>\n  );\n};\n"], "names": [], "mappings": ";;;;;;AAMY,MAAC,oBAAoB,GAAG,CAAC,EAAE,KAAK,EAAE,WAAW,GAAG,EAAE,EAAE,KAAK;AACrE,EAAE,MAAM;AACR,IAAI,KAAK,EAAE,SAAS;AACpB,IAAI,OAAO;AACX,IAAI,OAAO;AACX,IAAI,YAAY;AAChB,IAAI,QAAQ;AACZ,IAAI,QAAQ;AACZ,IAAI,WAAW;AACf,IAAI,OAAO;AACX,GAAG,GAAG,oBAAoB,EAAE,CAAC;AAC7B,EAAE,MAAM,KAAK,GAAG,SAAS,CAAC,SAAS,CAAC,KAAK,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC;AAC/D,EAAE,uBAAuB,GAAG,CAAC,KAAK,EAAE,EAAE,SAAS,EAAE,KAAK,CAAC,KAAK,CAAC,IAAI,EAAE,QAAQ,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,KAAK,KAAK;AACnH,IAAI,MAAM,OAAO,mBAAmB,IAAI,IAAI,EAAE,CAAC;AAC/C,IAAI,OAAO,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;AAC/B,IAAI,OAAO,CAAC,WAAW,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAC,CAAC;AAChD,IAAI,MAAM,KAAK,GAAG,gBAAgB,CAAC,QAAQ,EAAE,OAAO,EAAE,EAAE,KAAK,EAAE,OAAO,EAAE,CAAC,CAAC;AAC1E,IAAI,MAAM,UAAU,GAAG,YAAY,IAAI,WAAW,CAAC,YAAY,EAAE,OAAO,CAAC,CAAC;AAC1E,IAAI,MAAM,UAAU,GAAG,CAAC,aAAa,CAAC,OAAO,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;AACjE,IAAI,uBAAuB,GAAG;AAC9B,MAAM,QAAQ;AACd,MAAM;AACN,QAAQ,QAAQ,EAAE,UAAU;AAC5B,QAAQ,IAAI,EAAE,QAAQ;AACtB,QAAQ,SAAS,EAAE,OAAO;AAC1B,UAAU,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI;AAC/B,UAAU,UAAU,IAAI,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ;AACjD,UAAU,UAAU,IAAI,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ;AACjD,SAAS;AACT,QAAQ,OAAO,EAAE,MAAM;AACvB,UAAU,IAAI,UAAU,EAAE,OAAO;AACjC,UAAU,WAAW,CAAC,OAAO,CAAC,CAAC;AAC/B,UAAU,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;AAC9B,SAAS;AACT,QAAQ,QAAQ,EAAE,KAAK;AACvB,OAAO;AACP,MAAM,KAAK;AACX,KAAK,CAAC;AACN,GAAG,CAAC,EAAE,CAAC,CAAC;AACR;;;;"}