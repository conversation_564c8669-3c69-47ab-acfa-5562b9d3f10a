{"version": 3, "file": "theme.mjs", "sources": ["../../../../src/components/Dropdown/theme.ts"], "sourcesContent": ["import { createTheme } from \"../../helpers/create-theme\";\nimport type { FlowbiteDropdownTheme } from \"./Dropdown\";\n\nexport const dropdownTheme: FlowbiteDropdownTheme = createTheme({\n  arrowIcon: \"ml-2 h-4 w-4\",\n  content: \"py-1 focus:outline-none\",\n  floating: {\n    animation: \"transition-opacity\",\n    arrow: {\n      base: \"absolute z-10 h-2 w-2 rotate-45\",\n      style: {\n        dark: \"bg-gray-900 dark:bg-gray-700\",\n        light: \"bg-white\",\n        auto: \"bg-white dark:bg-gray-700\",\n      },\n      placement: \"-4px\",\n    },\n    base: \"z-10 w-fit divide-y divide-gray-100 rounded shadow focus:outline-none\",\n    content: \"py-1 text-sm text-gray-700 dark:text-gray-200\",\n    divider: \"my-1 h-px bg-gray-100 dark:bg-gray-600\",\n    header: \"block px-4 py-2 text-sm text-gray-700 dark:text-gray-200\",\n    hidden: \"invisible opacity-0\",\n    item: {\n      container: \"\",\n      base: \"flex w-full cursor-pointer items-center justify-start px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 focus:bg-gray-100 focus:outline-none dark:text-gray-200 dark:hover:bg-gray-600 dark:hover:text-white dark:focus:bg-gray-600 dark:focus:text-white\",\n      icon: \"mr-2 h-4 w-4\",\n    },\n    style: {\n      dark: \"bg-gray-900 text-white dark:bg-gray-700\",\n      light: \"border border-gray-200 bg-white text-gray-900\",\n      auto: \"border border-gray-200 bg-white text-gray-900 dark:border-none dark:bg-gray-700 dark:text-white\",\n    },\n    target: \"w-fit\",\n  },\n  inlineWrapper: \"flex items-center\",\n});\n"], "names": [], "mappings": ";;AAEY,MAAC,aAAa,GAAG,WAAW,CAAC;AACzC,EAAE,SAAS,EAAE,cAAc;AAC3B,EAAE,OAAO,EAAE,yBAAyB;AACpC,EAAE,QAAQ,EAAE;AACZ,IAAI,SAAS,EAAE,oBAAoB;AACnC,IAAI,KAAK,EAAE;AACX,MAAM,IAAI,EAAE,iCAAiC;AAC7C,MAAM,KAAK,EAAE;AACb,QAAQ,IAAI,EAAE,8BAA8B;AAC5C,QAAQ,KAAK,EAAE,UAAU;AACzB,QAAQ,IAAI,EAAE,2BAA2B;AACzC,OAAO;AACP,MAAM,SAAS,EAAE,MAAM;AACvB,KAAK;AACL,IAAI,IAAI,EAAE,uEAAuE;AACjF,IAAI,OAAO,EAAE,+CAA+C;AAC5D,IAAI,OAAO,EAAE,wCAAwC;AACrD,IAAI,MAAM,EAAE,0DAA0D;AACtE,IAAI,MAAM,EAAE,qBAAqB;AACjC,IAAI,IAAI,EAAE;AACV,MAAM,SAAS,EAAE,EAAE;AACnB,MAAM,IAAI,EAAE,2PAA2P;AACvQ,MAAM,IAAI,EAAE,cAAc;AAC1B,KAAK;AACL,IAAI,KAAK,EAAE;AACX,MAAM,IAAI,EAAE,yCAAyC;AACrD,MAAM,KAAK,EAAE,+CAA+C;AAC5D,MAAM,IAAI,EAAE,iGAAiG;AAC7G,KAAK;AACL,IAAI,MAAM,EAAE,OAAO;AACnB,GAAG;AACH,EAAE,aAAa,EAAE,mBAAmB;AACpC,CAAC;;;;"}