import { offset, autoPlacement, flip, shift, arrow } from '@floating-ui/react';

const getMiddleware = ({
  arrowRef,
  placement
}) => {
  const middleware = [];
  middleware.push(offset(8));
  middleware.push(placement === "auto" ? autoPlacement() : flip());
  middleware.push(shift({ padding: 8 }));
  if (arrowRef?.current) {
    middleware.push(arrow({ element: arrowRef.current }));
  }
  return middleware;
};
const getPlacement = ({ placement }) => {
  return placement === "auto" ? void 0 : placement;
};
const getArrowPlacement = ({ placement }) => {
  return {
    top: "bottom",
    right: "left",
    bottom: "top",
    left: "right"
  }[placement.split("-")[0]];
};

export { getArrowPlacement, getMiddleware, getPlacement };
//# sourceMappingURL=helpers.mjs.map
