{"version": 3, "file": "theme.mjs", "sources": ["../../../../src/components/Footer/theme.ts"], "sourcesContent": ["import { createTheme } from \"../../helpers/create-theme\";\nimport type { FlowbiteFooterTheme } from \"./Footer\";\n\nexport const footerTheme: FlowbiteFooterTheme = createTheme({\n  root: {\n    base: \"w-full rounded-lg bg-white shadow dark:bg-gray-800 md:flex md:items-center md:justify-between\",\n    container: \"w-full p-6\",\n    bgDark: \"bg-gray-800\",\n  },\n  groupLink: {\n    base: \"flex flex-wrap text-sm text-gray-500 dark:text-white\",\n    link: {\n      base: \"me-4 last:mr-0 md:mr-6\",\n      href: \"hover:underline\",\n    },\n    col: \"flex-col space-y-4\",\n  },\n  icon: {\n    base: \"text-gray-500 dark:hover:text-white\",\n    size: \"h-5 w-5\",\n  },\n  title: {\n    base: \"mb-6 text-sm font-semibold uppercase text-gray-500 dark:text-white\",\n  },\n  divider: {\n    base: \"my-6 w-full border-gray-200 dark:border-gray-700 sm:mx-auto lg:my-8\",\n  },\n  copyright: {\n    base: \"text-sm text-gray-500 dark:text-gray-400 sm:text-center\",\n    href: \"ml-1 hover:underline\",\n    span: \"ml-1\",\n  },\n  brand: {\n    base: \"mb-4 flex items-center sm:mb-0\",\n    img: \"mr-3 h-8\",\n    span: \"self-center whitespace-nowrap text-2xl font-semibold text-gray-800 dark:text-white\",\n  },\n});\n"], "names": [], "mappings": ";;AAEY,MAAC,WAAW,GAAG,WAAW,CAAC;AACvC,EAAE,IAAI,EAAE;AACR,IAAI,IAAI,EAAE,+FAA+F;AACzG,IAAI,SAAS,EAAE,YAAY;AAC3B,IAAI,MAAM,EAAE,aAAa;AACzB,GAAG;AACH,EAAE,SAAS,EAAE;AACb,IAAI,IAAI,EAAE,sDAAsD;AAChE,IAAI,IAAI,EAAE;AACV,MAAM,IAAI,EAAE,wBAAwB;AACpC,MAAM,IAAI,EAAE,iBAAiB;AAC7B,KAAK;AACL,IAAI,GAAG,EAAE,oBAAoB;AAC7B,GAAG;AACH,EAAE,IAAI,EAAE;AACR,IAAI,IAAI,EAAE,qCAAqC;AAC/C,IAAI,IAAI,EAAE,SAAS;AACnB,GAAG;AACH,EAAE,KAAK,EAAE;AACT,IAAI,IAAI,EAAE,oEAAoE;AAC9E,GAAG;AACH,EAAE,OAAO,EAAE;AACX,IAAI,IAAI,EAAE,qEAAqE;AAC/E,GAAG;AACH,EAAE,SAAS,EAAE;AACb,IAAI,IAAI,EAAE,yDAAyD;AACnE,IAAI,IAAI,EAAE,sBAAsB;AAChC,IAAI,IAAI,EAAE,MAAM;AAChB,GAAG;AACH,EAAE,KAAK,EAAE;AACT,IAAI,IAAI,EAAE,gCAAgC;AAC1C,IAAI,GAAG,EAAE,UAAU;AACnB,IAAI,IAAI,EAAE,oFAAoF;AAC9F,GAAG;AACH,CAAC;;;;"}