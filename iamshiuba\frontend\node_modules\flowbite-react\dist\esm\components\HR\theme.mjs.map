{"version": 3, "file": "theme.mjs", "sources": ["../../../../src/components/HR/theme.ts"], "sourcesContent": ["import { createTheme } from \"../../helpers/create-theme\";\nimport type { FlowbiteHRTheme } from \"./HR\";\n\nexport const hrTheme: FlowbiteHRTheme = createTheme({\n  root: {\n    base: \"my-8 h-px border-0 bg-gray-200 dark:bg-gray-700\",\n  },\n  trimmed: {\n    base: \"mx-auto my-4 h-1 w-48 rounded border-0 bg-gray-100 dark:bg-gray-700 md:my-10\",\n  },\n  icon: {\n    base: \"inline-flex w-full items-center justify-center\",\n    hrLine: \"my-8 h-1 w-64 rounded border-0 bg-gray-200 dark:bg-gray-700\",\n    icon: {\n      base: \"absolute left-1/2 -translate-x-1/2 bg-white px-4 dark:bg-gray-900\",\n      icon: \"h-4 w-4 text-gray-700 dark:text-gray-300\",\n    },\n  },\n  text: {\n    base: \"inline-flex w-full items-center justify-center\",\n    hrLine: \"my-8 h-px w-64 border-0 bg-gray-200 dark:bg-gray-700\",\n    text: \"absolute left-1/2 -translate-x-1/2 bg-white px-3 font-medium text-gray-900 dark:bg-gray-900 dark:text-white\",\n  },\n  square: {\n    base: \"mx-auto my-8 h-8 w-8 rounded border-0 bg-gray-200 dark:bg-gray-700 md:my-12\",\n  },\n});\n"], "names": [], "mappings": ";;AAEY,MAAC,OAAO,GAAG,WAAW,CAAC;AACnC,EAAE,IAAI,EAAE;AACR,IAAI,IAAI,EAAE,iDAAiD;AAC3D,GAAG;AACH,EAAE,OAAO,EAAE;AACX,IAAI,IAAI,EAAE,8EAA8E;AACxF,GAAG;AACH,EAAE,IAAI,EAAE;AACR,IAAI,IAAI,EAAE,gDAAgD;AAC1D,IAAI,MAAM,EAAE,6DAA6D;AACzE,IAAI,IAAI,EAAE;AACV,MAAM,IAAI,EAAE,mEAAmE;AAC/E,MAAM,IAAI,EAAE,0CAA0C;AACtD,KAAK;AACL,GAAG;AACH,EAAE,IAAI,EAAE;AACR,IAAI,IAAI,EAAE,gDAAgD;AAC1D,IAAI,MAAM,EAAE,sDAAsD;AAClE,IAAI,IAAI,EAAE,6GAA6G;AACvH,GAAG;AACH,EAAE,MAAM,EAAE;AACV,IAAI,IAAI,EAAE,6EAA6E;AACvF,GAAG;AACH,CAAC;;;;"}