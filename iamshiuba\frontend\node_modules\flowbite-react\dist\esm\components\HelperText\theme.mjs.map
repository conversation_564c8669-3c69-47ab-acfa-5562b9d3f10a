{"version": 3, "file": "theme.mjs", "sources": ["../../../../src/components/HelperText/theme.ts"], "sourcesContent": ["import { createTheme } from \"../../helpers/create-theme\";\nimport type { FlowbiteHelperTextTheme } from \"./HelperText\";\n\nexport const helperTextTheme: FlowbiteHelperTextTheme = createTheme({\n  root: {\n    base: \"mt-2 text-sm\",\n    colors: {\n      gray: \"text-gray-500 dark:text-gray-400\",\n      info: \"text-cyan-700 dark:text-cyan-800\",\n      success: \"text-green-600 dark:text-green-500\",\n      failure: \"text-red-600 dark:text-red-500\",\n      warning: \"text-yellow-500 dark:text-yellow-600\",\n    },\n  },\n});\n"], "names": [], "mappings": ";;AAEY,MAAC,eAAe,GAAG,WAAW,CAAC;AAC3C,EAAE,IAAI,EAAE;AACR,IAAI,IAAI,EAAE,cAAc;AACxB,IAAI,MAAM,EAAE;AACZ,MAAM,IAAI,EAAE,kCAAkC;AAC9C,MAAM,IAAI,EAAE,kCAAkC;AAC9C,MAAM,OAAO,EAAE,oCAAoC;AACnD,MAAM,OAAO,EAAE,gCAAgC;AAC/C,MAAM,OAAO,EAAE,sCAAsC;AACrD,KAAK;AACL,GAAG;AACH,CAAC;;;;"}