{"version": 3, "file": "Kbd.mjs", "sources": ["../../../../src/components/Kbd/Kbd.tsx"], "sourcesContent": ["import type { ComponentProps, FC } from \"react\";\nimport { twMerge } from \"tailwind-merge\";\nimport { mergeDeep } from \"../../helpers/merge-deep\";\nimport { getTheme } from \"../../theme-store\";\nimport type { DeepPartial } from \"../../types\";\n\nexport interface FlowbiteKbdTheme {\n  root: FlowbiteKbdRootTheme;\n}\n\nexport interface FlowbiteKbdRootTheme {\n  base: string;\n  icon: string;\n}\n\nexport interface KbdProps extends ComponentProps<\"span\"> {\n  icon?: FC<ComponentProps<\"svg\">>;\n  theme?: DeepPartial<FlowbiteKbdTheme>;\n}\n\nexport const Kbd: FC<KbdProps> = ({ children, className, icon: Icon, theme: customTheme = {}, ...props }) => {\n  const theme = mergeDeep(getTheme().kbd, customTheme);\n\n  return (\n    <span className={twMerge(theme.root.base, className)} data-testid=\"flowbite-kbd\" {...props}>\n      {Icon && <Icon className={theme.root.icon} data-testid=\"flowbite-kbd-icon\" />}\n      {children}\n    </span>\n  );\n};\n\nKbd.displayName = \"Kbd\";\n"], "names": [], "mappings": ";;;;;AAKY,MAAC,GAAG,GAAG,CAAC,EAAE,QAAQ,EAAE,SAAS,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,WAAW,GAAG,EAAE,EAAE,GAAG,KAAK,EAAE,KAAK;AAC/F,EAAE,MAAM,KAAK,GAAG,SAAS,CAAC,QAAQ,EAAE,CAAC,GAAG,EAAE,WAAW,CAAC,CAAC;AACvD,EAAE,uBAAuB,IAAI,CAAC,MAAM,EAAE,EAAE,SAAS,EAAE,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,EAAE,SAAS,CAAC,EAAE,aAAa,EAAE,cAAc,EAAE,GAAG,KAAK,EAAE,QAAQ,EAAE;AAC3I,IAAI,IAAI,oBAAoB,GAAG,CAAC,IAAI,EAAE,EAAE,SAAS,EAAE,KAAK,CAAC,IAAI,CAAC,IAAI,EAAE,aAAa,EAAE,mBAAmB,EAAE,CAAC;AACzG,IAAI,QAAQ;AACZ,GAAG,EAAE,CAAC,CAAC;AACP,EAAE;AACF,GAAG,CAAC,WAAW,GAAG,KAAK;;;;"}