{"version": 3, "file": "Label.mjs", "sources": ["../../../../src/components/Label/Label.tsx"], "sourcesContent": ["import type { ComponentProps, FC } from \"react\";\nimport { twMerge } from \"tailwind-merge\";\nimport { mergeDeep } from \"../../helpers/merge-deep\";\nimport { getTheme } from \"../../theme-store\";\nimport type { DeepPartial, DynamicStringEnumKeysOf } from \"../../types\";\nimport type { FlowbiteStateColors } from \"../Flowbite\";\n\nexport interface FlowbiteLabelTheme {\n  root: FlowbiteLabelRootTheme;\n}\n\nexport interface FlowbiteLabelRootTheme {\n  base: string;\n  colors: LabelColors;\n  disabled: string;\n}\n\nexport interface LabelColors extends FlowbiteStateColors {\n  [key: string]: string;\n  default: string;\n}\n\nexport interface LabelProps extends Omit<ComponentProps<\"label\">, \"color\"> {\n  color?: DynamicStringEnumKeysOf<LabelColors>;\n  disabled?: boolean;\n  theme?: DeepPartial<FlowbiteLabelTheme>;\n  value?: string;\n}\n\nexport const Label: FC<LabelProps> = ({\n  children,\n  className,\n  color = \"default\",\n  disabled = false,\n  theme: customTheme = {},\n  value,\n  ...props\n}) => {\n  const theme = mergeDeep(getTheme().label, customTheme);\n\n  return (\n    <label\n      className={twMerge(theme.root.base, theme.root.colors[color], disabled && theme.root.disabled, className)}\n      data-testid=\"flowbite-label\"\n      {...props}\n    >\n      {value ?? children ?? \"\"}\n    </label>\n  );\n};\n\nLabel.displayName = \"Label\";\n"], "names": [], "mappings": ";;;;;AAKY,MAAC,KAAK,GAAG,CAAC;AACtB,EAAE,QAAQ;AACV,EAAE,SAAS;AACX,EAAE,KAAK,GAAG,SAAS;AACnB,EAAE,QAAQ,GAAG,KAAK;AAClB,EAAE,KAAK,EAAE,WAAW,GAAG,EAAE;AACzB,EAAE,KAAK;AACP,EAAE,GAAG,KAAK;AACV,CAAC,KAAK;AACN,EAAE,MAAM,KAAK,GAAG,SAAS,CAAC,QAAQ,EAAE,CAAC,KAAK,EAAE,WAAW,CAAC,CAAC;AACzD,EAAE,uBAAuB,GAAG;AAC5B,IAAI,OAAO;AACX,IAAI;AACJ,MAAM,SAAS,EAAE,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,QAAQ,IAAI,KAAK,CAAC,IAAI,CAAC,QAAQ,EAAE,SAAS,CAAC;AAC/G,MAAM,aAAa,EAAE,gBAAgB;AACrC,MAAM,GAAG,KAAK;AACd,MAAM,QAAQ,EAAE,KAAK,IAAI,QAAQ,IAAI,EAAE;AACvC,KAAK;AACL,GAAG,CAAC;AACJ,EAAE;AACF,KAAK,CAAC,WAAW,GAAG,OAAO;;;;"}