{"version": 3, "file": "ListGroup.mjs", "sources": ["../../../../src/components/ListGroup/ListGroup.tsx"], "sourcesContent": ["import type { ComponentProps, FC } from \"react\";\nimport { twMerge } from \"tailwind-merge\";\nimport { mergeDeep } from \"../../helpers/merge-deep\";\nimport { getTheme } from \"../../theme-store\";\nimport type { DeepPartial } from \"../../types\";\nimport type { FlowbiteListGroupItemTheme } from \"./ListGroupItem\";\nimport { ListGroupItem } from \"./ListGroupItem\";\n\nexport interface FlowbiteListGroupTheme {\n  root: FlowbiteListGroupRootTheme;\n  item: FlowbiteListGroupItemTheme;\n}\n\nexport interface FlowbiteListGroupRootTheme {\n  base: string;\n}\n\nexport interface ListGroupProps extends ComponentProps<\"ul\"> {\n  theme?: DeepPartial<FlowbiteListGroupTheme>;\n}\n\nconst ListGroupComponent: FC<ListGroupProps> = ({ children, className, theme: customTheme = {}, ...props }) => {\n  const theme = mergeDeep(getTheme().listGroup, customTheme);\n\n  return (\n    <ul className={twMerge(theme.root.base, className)} {...props}>\n      {children}\n    </ul>\n  );\n};\n\nListGroupComponent.displayName = \"ListGroup\";\nListGroupItem.displayName = \"ListGroup.Item\";\n\nexport const ListGroup = Object.assign(ListGroupComponent, {\n  Item: ListGroupItem,\n});\n"], "names": [], "mappings": ";;;;;;AAMA,MAAM,kBAAkB,GAAG,CAAC,EAAE,QAAQ,EAAE,SAAS,EAAE,KAAK,EAAE,WAAW,GAAG,EAAE,EAAE,GAAG,KAAK,EAAE,KAAK;AAC3F,EAAE,MAAM,KAAK,GAAG,SAAS,CAAC,QAAQ,EAAE,CAAC,SAAS,EAAE,WAAW,CAAC,CAAC;AAC7D,EAAE,uBAAuB,GAAG,CAAC,IAAI,EAAE,EAAE,SAAS,EAAE,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,EAAE,SAAS,CAAC,EAAE,GAAG,KAAK,EAAE,QAAQ,EAAE,CAAC,CAAC;AAC3G,CAAC,CAAC;AACF,kBAAkB,CAAC,WAAW,GAAG,WAAW,CAAC;AAC7C,aAAa,CAAC,WAAW,GAAG,gBAAgB,CAAC;AACjC,MAAC,SAAS,GAAG,MAAM,CAAC,MAAM,CAAC,kBAAkB,EAAE;AAC3D,EAAE,IAAI,EAAE,aAAa;AACrB,CAAC;;;;"}