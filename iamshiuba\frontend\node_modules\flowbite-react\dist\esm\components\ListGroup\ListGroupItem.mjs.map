{"version": 3, "file": "ListGroupItem.mjs", "sources": ["../../../../src/components/ListGroup/ListGroupItem.tsx"], "sourcesContent": ["import type { ComponentProps, FC, PropsWithChildren } from \"react\";\nimport { twMerge } from \"tailwind-merge\";\nimport { mergeDeep } from \"../../helpers/merge-deep\";\nimport { getTheme } from \"../../theme-store\";\nimport type { DeepPartial } from \"../../types\";\nimport type { FlowbiteBoolean } from \"../Flowbite\";\n\nexport interface FlowbiteListGroupItemTheme {\n  base: string;\n  link: {\n    base: string;\n    active: FlowbiteBoolean;\n    disabled: FlowbiteBoolean;\n    href: FlowbiteBoolean;\n    icon: string;\n  };\n}\n\nexport interface ListGroupItemProps extends PropsWithChildren {\n  active?: boolean;\n  disabled?: boolean;\n  href?: string;\n  icon?: FC<ComponentProps<\"svg\">>;\n  onClick?: () => void;\n  theme?: DeepPartial<FlowbiteListGroupItemTheme>;\n}\n\nexport const ListGroupItem: FC<ListGroupItemProps & ComponentProps<\"a\"> & ComponentProps<\"button\">> = ({\n  active: isActive,\n  children,\n  className,\n  href,\n  icon: Icon,\n  onClick,\n  theme: customTheme = {},\n  disabled,\n  ...props\n}) => {\n  const theme = mergeDeep(getTheme().listGroup.item, customTheme);\n\n  const isLink = typeof href !== \"undefined\";\n  const Component = isLink ? \"a\" : \"button\";\n\n  return (\n    <li className={twMerge(theme.base, className)}>\n      <Component\n        href={href}\n        onClick={onClick}\n        type={isLink ? undefined : \"button\"}\n        disabled={disabled}\n        className={twMerge(\n          theme.link.active[isActive ? \"on\" : \"off\"],\n          theme.link.disabled[disabled ? \"on\" : \"off\"],\n          theme.link.base,\n          theme.link.href[isLink ? \"on\" : \"off\"],\n        )}\n        {...props}\n      >\n        {Icon && <Icon aria-hidden data-testid=\"flowbite-list-group-item-icon\" className={theme.link.icon} />}\n        {children}\n      </Component>\n    </li>\n  );\n};\n"], "names": [], "mappings": ";;;;;AAKY,MAAC,aAAa,GAAG,CAAC;AAC9B,EAAE,MAAM,EAAE,QAAQ;AAClB,EAAE,QAAQ;AACV,EAAE,SAAS;AACX,EAAE,IAAI;AACN,EAAE,IAAI,EAAE,IAAI;AACZ,EAAE,OAAO;AACT,EAAE,KAAK,EAAE,WAAW,GAAG,EAAE;AACzB,EAAE,QAAQ;AACV,EAAE,GAAG,KAAK;AACV,CAAC,KAAK;AACN,EAAE,MAAM,KAAK,GAAG,SAAS,CAAC,QAAQ,EAAE,CAAC,SAAS,CAAC,IAAI,EAAE,WAAW,CAAC,CAAC;AAClE,EAAE,MAAM,MAAM,GAAG,OAAO,IAAI,KAAK,WAAW,CAAC;AAC7C,EAAE,MAAM,SAAS,GAAG,MAAM,GAAG,GAAG,GAAG,QAAQ,CAAC;AAC5C,EAAE,uBAAuB,GAAG,CAAC,IAAI,EAAE,EAAE,SAAS,EAAE,OAAO,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC,EAAE,QAAQ,kBAAkB,IAAI;AAC9G,IAAI,SAAS;AACb,IAAI;AACJ,MAAM,IAAI;AACV,MAAM,OAAO;AACb,MAAM,IAAI,EAAE,MAAM,GAAG,KAAK,CAAC,GAAG,QAAQ;AACtC,MAAM,QAAQ;AACd,MAAM,SAAS,EAAE,OAAO;AACxB,QAAQ,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,GAAG,IAAI,GAAG,KAAK,CAAC;AAClD,QAAQ,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,QAAQ,GAAG,IAAI,GAAG,KAAK,CAAC;AACpD,QAAQ,KAAK,CAAC,IAAI,CAAC,IAAI;AACvB,QAAQ,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,IAAI,GAAG,KAAK,CAAC;AAC9C,OAAO;AACP,MAAM,GAAG,KAAK;AACd,MAAM,QAAQ,EAAE;AAChB,QAAQ,IAAI,oBAAoB,GAAG,CAAC,IAAI,EAAE,EAAE,aAAa,EAAE,IAAI,EAAE,aAAa,EAAE,+BAA+B,EAAE,SAAS,EAAE,KAAK,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;AAC9I,QAAQ,QAAQ;AAChB,OAAO;AACP,KAAK;AACL,GAAG,EAAE,CAAC,CAAC;AACP;;;;"}