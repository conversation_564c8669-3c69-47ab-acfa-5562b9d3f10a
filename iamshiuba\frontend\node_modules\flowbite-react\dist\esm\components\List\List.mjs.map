{"version": 3, "file": "List.mjs", "sources": ["../../../../src/components/List/List.tsx"], "sourcesContent": ["import type { ComponentProps, FC, PropsWithChildren } from \"react\";\nimport { twMerge } from \"tailwind-merge\";\nimport type { FlowbiteListItemTheme, FlowbiteStateColors } from \"../..\";\nimport { mergeDeep } from \"../../helpers/merge-deep\";\nimport { getTheme } from \"../../theme-store\";\nimport type { DeepPartial } from \"../../types\";\nimport { ListItem } from \"./ListItem\";\n\nexport interface FlowbiteListTheme {\n  root: FlowbiteListRootTheme;\n  item: FlowbiteListItemTheme;\n}\n\nexport interface FlowbiteListRootTheme {\n  base: string;\n  ordered: {\n    on: string;\n    off: string;\n  };\n  horizontal: string;\n  unstyled: string;\n  nested: string;\n}\n\nexport interface ListColors extends FlowbiteStateColors {\n  [key: string]: string;\n  default: string;\n}\n\nexport interface ListProps extends PropsWithChildren<ComponentProps<\"ul\"> & ComponentProps<\"ol\">> {\n  theme?: DeepPartial<FlowbiteListTheme>;\n  ordered?: boolean;\n  unstyled?: boolean;\n  nested?: boolean;\n  horizontal?: boolean;\n}\n\nconst ListComponent: FC<ListProps> = ({\n  children,\n  className,\n  unstyled,\n  nested,\n  ordered,\n  horizontal,\n  theme: customTheme = {},\n  ...props\n}) => {\n  const theme = mergeDeep(getTheme().list, customTheme);\n  const Component = ordered ? \"ol\" : \"ul\";\n\n  return (\n    <Component\n      className={twMerge(\n        theme.root.base,\n        theme.root.ordered[ordered ? \"on\" : \"off\"],\n        unstyled && theme.root.unstyled,\n        nested && theme.root.nested,\n        horizontal && theme.root.horizontal,\n        className,\n      )}\n      {...props}\n    >\n      {children}\n    </Component>\n  );\n};\n\nListComponent.displayName = \"List\";\nListItem.displayName = \"List.Item\";\n\nexport const List = Object.assign(ListComponent, { Item: ListItem });\n"], "names": [], "mappings": ";;;;;;AAMA,MAAM,aAAa,GAAG,CAAC;AACvB,EAAE,QAAQ;AACV,EAAE,SAAS;AACX,EAAE,QAAQ;AACV,EAAE,MAAM;AACR,EAAE,OAAO;AACT,EAAE,UAAU;AACZ,EAAE,KAAK,EAAE,WAAW,GAAG,EAAE;AACzB,EAAE,GAAG,KAAK;AACV,CAAC,KAAK;AACN,EAAE,MAAM,KAAK,GAAG,SAAS,CAAC,QAAQ,EAAE,CAAC,IAAI,EAAE,WAAW,CAAC,CAAC;AACxD,EAAE,MAAM,SAAS,GAAG,OAAO,GAAG,IAAI,GAAG,IAAI,CAAC;AAC1C,EAAE,uBAAuB,GAAG;AAC5B,IAAI,SAAS;AACb,IAAI;AACJ,MAAM,SAAS,EAAE,OAAO;AACxB,QAAQ,KAAK,CAAC,IAAI,CAAC,IAAI;AACvB,QAAQ,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,GAAG,IAAI,GAAG,KAAK,CAAC;AAClD,QAAQ,QAAQ,IAAI,KAAK,CAAC,IAAI,CAAC,QAAQ;AACvC,QAAQ,MAAM,IAAI,KAAK,CAAC,IAAI,CAAC,MAAM;AACnC,QAAQ,UAAU,IAAI,KAAK,CAAC,IAAI,CAAC,UAAU;AAC3C,QAAQ,SAAS;AACjB,OAAO;AACP,MAAM,GAAG,KAAK;AACd,MAAM,QAAQ;AACd,KAAK;AACL,GAAG,CAAC;AACJ,CAAC,CAAC;AACF,aAAa,CAAC,WAAW,GAAG,MAAM,CAAC;AACnC,QAAQ,CAAC,WAAW,GAAG,WAAW,CAAC;AACvB,MAAC,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC,aAAa,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;;;;"}