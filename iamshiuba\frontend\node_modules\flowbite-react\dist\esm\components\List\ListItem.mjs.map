{"version": 3, "file": "ListItem.mjs", "sources": ["../../../../src/components/List/ListItem.tsx"], "sourcesContent": ["import type { ComponentProps, FC } from \"react\";\nimport { twMerge } from \"tailwind-merge\";\nimport { mergeDeep } from \"../../helpers/merge-deep\";\nimport { getTheme } from \"../../theme-store\";\nimport type { DeepPartial } from \"../../types\";\n\nexport interface FlowbiteListItemTheme {\n  icon: string;\n  withIcon: {\n    on: string;\n    off: string;\n  };\n}\n\nexport interface ListItemProps extends ComponentProps<\"li\"> {\n  className?: string;\n  icon?: FC<ComponentProps<\"svg\">>;\n  theme?: DeepPartial<FlowbiteListItemTheme>;\n}\n\nexport const ListItem: FC<ListItemProps> = ({ children, className, icon: Icon, theme: customTheme = {}, ...props }) => {\n  const theme = mergeDeep(getTheme().list.item, customTheme);\n\n  return (\n    <li className={twMerge(theme.withIcon[Icon ? \"on\" : \"off\"], className)} {...props}>\n      {Icon && <Icon className={twMerge(theme.icon)} />}\n      {children}\n    </li>\n  );\n};\n"], "names": [], "mappings": ";;;;;AAKY,MAAC,QAAQ,GAAG,CAAC,EAAE,QAAQ,EAAE,SAAS,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,WAAW,GAAG,EAAE,EAAE,GAAG,KAAK,EAAE,KAAK;AACpG,EAAE,MAAM,KAAK,GAAG,SAAS,CAAC,QAAQ,EAAE,CAAC,IAAI,CAAC,IAAI,EAAE,WAAW,CAAC,CAAC;AAC7D,EAAE,uBAAuB,IAAI,CAAC,IAAI,EAAE,EAAE,SAAS,EAAE,OAAO,CAAC,KAAK,CAAC,QAAQ,CAAC,IAAI,GAAG,IAAI,GAAG,KAAK,CAAC,EAAE,SAAS,CAAC,EAAE,GAAG,KAAK,EAAE,QAAQ,EAAE;AAC9H,IAAI,IAAI,oBAAoB,GAAG,CAAC,IAAI,EAAE,EAAE,SAAS,EAAE,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC;AACzE,IAAI,QAAQ;AACZ,GAAG,EAAE,CAAC,CAAC;AACP;;;;"}