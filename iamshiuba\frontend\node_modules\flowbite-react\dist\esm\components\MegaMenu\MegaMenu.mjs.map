{"version": 3, "file": "MegaMenu.mjs", "sources": ["../../../../src/components/MegaMenu/MegaMenu.tsx"], "sourcesContent": ["\"use client\";\n\nimport type { FC } from \"react\";\nimport { mergeDeep } from \"../../helpers/merge-deep\";\nimport { getTheme } from \"../../theme-store\";\nimport type { FlowbiteNavbarTheme, NavbarComponentProps } from \"../Navbar\";\nimport { Navbar } from \"../Navbar\";\nimport { FlowbiteMegaMenuDropdownTheme, MegaMenuDropdown } from \"./MegaMenuDropdown\";\nimport { FlowbiteMegaMenuDropdownToggleTheme, MegaMenuDropdownToggle } from \"./MegaMenuDropdownToggle\";\n\nexport interface FlowbiteMegaMenuTheme extends FlowbiteNavbarTheme {\n  dropdown: FlowbiteMegaMenuDropdownTheme;\n  dropdownToggle: FlowbiteMegaMenuDropdownToggleTheme;\n}\n\nexport type MegaMenuProps = NavbarComponentProps;\n\nconst MegaMenuComponent: FC<MegaMenuProps> = ({ children, theme: customTheme = {}, ...props }) => {\n  const theme = mergeDeep(getTheme().megaMenu, customTheme);\n\n  return (\n    <Navbar fluid theme={theme} {...props}>\n      {children}\n    </Navbar>\n  );\n};\n\nexport const MegaMenu = Object.assign(MegaMenuComponent, {\n  Dropdown: MegaMenuDropdown,\n  DropdownToggle: MegaMenuDropdownToggle,\n});\nMegaMenuComponent.displayName = \"MegaMenu\";\n"], "names": [], "mappings": ";;;;;;;;;;AAQA,MAAM,iBAAiB,GAAG,CAAC,EAAE,QAAQ,EAAE,KAAK,EAAE,WAAW,GAAG,EAAE,EAAE,GAAG,KAAK,EAAE,KAAK;AAC/E,EAAE,MAAM,KAAK,GAAG,SAAS,CAAC,QAAQ,EAAE,CAAC,QAAQ,EAAE,WAAW,CAAC,CAAC;AAC5D,EAAE,uBAAuB,GAAG,CAAC,MAAM,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,KAAK,EAAE,QAAQ,EAAE,CAAC,CAAC;AACjF,CAAC,CAAC;AACU,MAAC,QAAQ,GAAG,MAAM,CAAC,MAAM,CAAC,iBAAiB,EAAE;AACzD,EAAE,QAAQ,EAAE,gBAAgB;AAC5B,EAAE,cAAc,EAAE,sBAAsB;AACxC,CAAC,EAAE;AACH,iBAAiB,CAAC,WAAW,GAAG,UAAU;;;;"}