{"version": 3, "file": "MegaMenuDropdown.mjs", "sources": ["../../../../src/components/MegaMenu/MegaMenuDropdown.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useEffect, useId, useRef, useState, type ComponentProps, type FC } from \"react\";\nimport { twMerge } from \"tailwind-merge\";\nimport { mergeDeep } from \"../../helpers/merge-deep\";\nimport { getTheme } from \"../../theme-store\";\nimport { Dropdown, FlowbiteDropdownTheme } from \"../Dropdown\";\n\nexport interface FlowbiteMegaMenuDropdownTheme {\n  base: string;\n  toggle: FlowbiteDropdownTheme;\n}\n\nexport interface MegaMenuDropdownProps extends ComponentProps<\"div\"> {\n  theme?: FlowbiteMegaMenuDropdownTheme;\n  toggle?: JSX.Element;\n}\n\nexport const MegaMenuDropdown: FC<MegaMenuDropdownProps> = ({\n  children,\n  className,\n  theme: customTheme = {},\n  toggle,\n  ...props\n}) => {\n  const [labelledBy, setLabelledBy] = useState<string | undefined>(undefined);\n\n  const theme = mergeDeep(getTheme().megaMenu.dropdown, customTheme);\n\n  if (toggle) {\n    return (\n      <Dropdown\n        inline\n        label={toggle}\n        placement=\"bottom\"\n        theme={theme.toggle}\n        className={twMerge(theme.base, className)}\n      >\n        {children}\n      </Dropdown>\n    );\n  }\n\n  const id = useId();\n  const ref = useRef<HTMLDivElement>(null);\n\n  useEffect(() => {\n    const findToggle = function () {\n      const megaMenu = ref.current?.closest(\"nav\");\n\n      return megaMenu?.querySelector('[aria-haspopup=\"menu\"]');\n    };\n\n    setLabelledBy(findToggle()?.id);\n  }, []);\n\n  return (\n    <div\n      aria-labelledby={labelledBy}\n      id={id}\n      ref={ref}\n      role=\"menu\"\n      className={twMerge(theme.base, className)}\n      {...props}\n    >\n      {children}\n    </div>\n  );\n};\n\nMegaMenuDropdown.displayName = \"MegaMenu.Dropdown\";\n"], "names": [], "mappings": ";;;;;;;;;AAQY,MAAC,gBAAgB,GAAG,CAAC;AACjC,EAAE,QAAQ;AACV,EAAE,SAAS;AACX,EAAE,KAAK,EAAE,WAAW,GAAG,EAAE;AACzB,EAAE,MAAM;AACR,EAAE,GAAG,KAAK;AACV,CAAC,KAAK;AACN,EAAE,MAAM,CAAC,UAAU,EAAE,aAAa,CAAC,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;AACvD,EAAE,MAAM,KAAK,GAAG,SAAS,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,QAAQ,EAAE,WAAW,CAAC,CAAC;AACrE,EAAE,IAAI,MAAM,EAAE;AACd,IAAI,uBAAuB,GAAG;AAC9B,MAAM,QAAQ;AACd,MAAM;AACN,QAAQ,MAAM,EAAE,IAAI;AACpB,QAAQ,KAAK,EAAE,MAAM;AACrB,QAAQ,SAAS,EAAE,QAAQ;AAC3B,QAAQ,KAAK,EAAE,KAAK,CAAC,MAAM;AAC3B,QAAQ,SAAS,EAAE,OAAO,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC;AACjD,QAAQ,QAAQ;AAChB,OAAO;AACP,KAAK,CAAC;AACN,GAAG;AACH,EAAE,MAAM,EAAE,GAAG,KAAK,EAAE,CAAC;AACrB,EAAE,MAAM,GAAG,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC;AAC3B,EAAE,SAAS,CAAC,MAAM;AAClB,IAAI,MAAM,UAAU,GAAG,WAAW;AAClC,MAAM,MAAM,QAAQ,GAAG,GAAG,CAAC,OAAO,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC;AACnD,MAAM,OAAO,QAAQ,EAAE,aAAa,CAAC,wBAAwB,CAAC,CAAC;AAC/D,KAAK,CAAC;AACN,IAAI,aAAa,CAAC,UAAU,EAAE,EAAE,EAAE,CAAC,CAAC;AACpC,GAAG,EAAE,EAAE,CAAC,CAAC;AACT,EAAE,uBAAuB,GAAG;AAC5B,IAAI,KAAK;AACT,IAAI;AACJ,MAAM,iBAAiB,EAAE,UAAU;AACnC,MAAM,EAAE;AACR,MAAM,GAAG;AACT,MAAM,IAAI,EAAE,MAAM;AAClB,MAAM,SAAS,EAAE,OAAO,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC;AAC/C,MAAM,GAAG,KAAK;AACd,MAAM,QAAQ;AACd,KAAK;AACL,GAAG,CAAC;AACJ,EAAE;AACF,gBAAgB,CAAC,WAAW,GAAG,mBAAmB;;;;"}