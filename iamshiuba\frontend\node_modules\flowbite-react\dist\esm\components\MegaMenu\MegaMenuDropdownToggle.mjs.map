{"version": 3, "file": "MegaMenuDropdownToggle.mjs", "sources": ["../../../../src/components/MegaMenu/MegaMenuDropdownToggle.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useEffect, useId, useRef, useState, type ComponentProps, type FC, type Mouse<PERSON>ventH<PERSON><PERSON> } from \"react\";\nimport { twMerge } from \"tailwind-merge\";\nimport { mergeDeep } from \"../../helpers/merge-deep\";\nimport { getTheme } from \"../../theme-store\";\nimport { DeepPartial } from \"../../types\";\n\nexport interface FlowbiteMegaMenuDropdownToggleTheme {\n  base: string;\n}\n\nexport interface MegaMenuDropdownToggleProps extends ComponentProps<\"button\"> {\n  theme?: DeepPartial<FlowbiteMegaMenuDropdownToggleTheme>;\n}\n\nexport const MegaMenuDropdownToggle: FC<MegaMenuDropdownToggleProps> = ({\n  children,\n  className,\n  theme: customTheme = {},\n  ...props\n}) => {\n  const id = useId();\n  const ref = useRef<HTMLButtonElement>(null);\n  const [controls, setControls] = useState<string | undefined>(undefined);\n  const [isExpanded, setExpanded] = useState<boolean | undefined>(undefined);\n\n  const theme = mergeDeep(getTheme().megaMenu.dropdownToggle, customTheme);\n\n  const findDropdown = function () {\n    const megaMenu = ref.current?.closest(\"nav\");\n\n    return megaMenu?.querySelector('[role=\"menu\"]');\n  };\n\n  const onClick: MouseEventHandler<HTMLButtonElement> = function () {\n    findDropdown()?.classList.toggle(\"hidden\");\n\n    setExpanded(!isExpanded);\n  };\n\n  useEffect(() => {\n    const dropdown = findDropdown();\n    const isDropdownHidden = dropdown?.classList.contains(\"hidden\");\n\n    setControls(dropdown?.id);\n    setExpanded(!isDropdownHidden);\n  }, []);\n\n  return (\n    <button\n      aria-controls={controls}\n      aria-expanded={isExpanded}\n      aria-haspopup=\"menu\"\n      id={id}\n      onClick={onClick}\n      ref={ref}\n      className={twMerge(theme.base, className)}\n      {...props}\n    >\n      {children}\n    </button>\n  );\n};\n\nMegaMenuDropdownToggle.displayName = \"MegaMenu.DropdownToggle\";\n"], "names": [], "mappings": ";;;;;;AAOY,MAAC,sBAAsB,GAAG,CAAC;AACvC,EAAE,QAAQ;AACV,EAAE,SAAS;AACX,EAAE,KAAK,EAAE,WAAW,GAAG,EAAE;AACzB,EAAE,GAAG,KAAK;AACV,CAAC,KAAK;AACN,EAAE,MAAM,EAAE,GAAG,KAAK,EAAE,CAAC;AACrB,EAAE,MAAM,GAAG,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC;AAC3B,EAAE,MAAM,CAAC,QAAQ,EAAE,WAAW,CAAC,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;AACnD,EAAE,MAAM,CAAC,UAAU,EAAE,WAAW,CAAC,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;AACrD,EAAE,MAAM,KAAK,GAAG,SAAS,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,cAAc,EAAE,WAAW,CAAC,CAAC;AAC3E,EAAE,MAAM,YAAY,GAAG,WAAW;AAClC,IAAI,MAAM,QAAQ,GAAG,GAAG,CAAC,OAAO,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC;AACjD,IAAI,OAAO,QAAQ,EAAE,aAAa,CAAC,eAAe,CAAC,CAAC;AACpD,GAAG,CAAC;AACJ,EAAE,MAAM,OAAO,GAAG,WAAW;AAC7B,IAAI,YAAY,EAAE,EAAE,SAAS,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;AAC/C,IAAI,WAAW,CAAC,CAAC,UAAU,CAAC,CAAC;AAC7B,GAAG,CAAC;AACJ,EAAE,SAAS,CAAC,MAAM;AAClB,IAAI,MAAM,QAAQ,GAAG,YAAY,EAAE,CAAC;AACpC,IAAI,MAAM,gBAAgB,GAAG,QAAQ,EAAE,SAAS,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;AACpE,IAAI,WAAW,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC;AAC9B,IAAI,WAAW,CAAC,CAAC,gBAAgB,CAAC,CAAC;AACnC,GAAG,EAAE,EAAE,CAAC,CAAC;AACT,EAAE,uBAAuB,GAAG;AAC5B,IAAI,QAAQ;AACZ,IAAI;AACJ,MAAM,eAAe,EAAE,QAAQ;AAC/B,MAAM,eAAe,EAAE,UAAU;AACjC,MAAM,eAAe,EAAE,MAAM;AAC7B,MAAM,EAAE;AACR,MAAM,OAAO;AACb,MAAM,GAAG;AACT,MAAM,SAAS,EAAE,OAAO,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC;AAC/C,MAAM,GAAG,KAAK;AACd,MAAM,QAAQ;AACd,KAAK;AACL,GAAG,CAAC;AACJ,EAAE;AACF,sBAAsB,CAAC,WAAW,GAAG,yBAAyB;;;;"}