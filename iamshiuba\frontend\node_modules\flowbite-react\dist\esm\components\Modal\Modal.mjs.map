{"version": 3, "file": "Modal.mjs", "sources": ["../../../../src/components/Modal/Modal.tsx"], "sourcesContent": ["\"use client\";\n\nimport {\n  FloatingFocus<PERSON>anager,\n  FloatingOverlay,\n  FloatingPortal,\n  useClick,\n  useDismiss,\n  useFloating,\n  useInteractions,\n  useMergeRefs,\n  useRole,\n} from \"@floating-ui/react\";\nimport type { MutableRefObject } from \"react\";\nimport { forwardRef, useState, type ComponentPropsWithoutRef } from \"react\";\nimport { twMerge } from \"tailwind-merge\";\nimport { mergeDeep } from \"../../helpers/merge-deep\";\nimport { getTheme } from \"../../theme-store\";\nimport type { DeepPartial, DynamicStringEnumKeysOf } from \"../../types\";\nimport type { FlowbiteBoolean, FlowbitePositions, FlowbiteSizes } from \"../Flowbite\";\nimport type { FlowbiteModalBodyTheme } from \"./ModalBody\";\nimport { ModalBody } from \"./ModalBody\";\nimport { ModalContext } from \"./ModalContext\";\nimport type { FlowbiteModalFooterTheme } from \"./ModalFooter\";\nimport { ModalFooter } from \"./ModalFooter\";\nimport type { FlowbiteModalHeaderTheme } from \"./ModalHeader\";\nimport { ModalHeader } from \"./ModalHeader\";\n\nexport interface FlowbiteModalTheme {\n  root: FlowbiteModalRootTheme;\n  content: FlowbiteModalContentTheme;\n  body: FlowbiteModalBodyTheme;\n  header: FlowbiteModalHeaderTheme;\n  footer: FlowbiteModalFooterTheme;\n}\n\nexport interface FlowbiteModalRootTheme {\n  base: string;\n  show: FlowbiteBoolean;\n  sizes: ModalSizes;\n  positions: ModalPositions;\n}\n\nexport interface FlowbiteModalContentTheme {\n  base: string;\n  inner: string;\n}\n\nexport interface ModalPositions extends FlowbitePositions {\n  [key: string]: string;\n}\n\nexport interface ModalSizes extends Omit<FlowbiteSizes, \"xs\"> {\n  [key: string]: string;\n}\n\nexport interface ModalProps extends ComponentPropsWithoutRef<\"div\"> {\n  onClose?: () => void;\n  position?: DynamicStringEnumKeysOf<ModalPositions>;\n  popup?: boolean;\n  root?: HTMLElement;\n  show?: boolean;\n  size?: DynamicStringEnumKeysOf<ModalSizes>;\n  dismissible?: boolean;\n  theme?: DeepPartial<FlowbiteModalTheme>;\n  initialFocus?: number | MutableRefObject<HTMLElement | null>;\n}\n\nconst ModalComponent = forwardRef<HTMLDivElement, ModalProps>(\n  (\n    {\n      children,\n      className,\n      dismissible = false,\n      onClose,\n      popup,\n      position = \"center\",\n      root,\n      show,\n      size = \"2xl\",\n      theme: customTheme = {},\n      initialFocus,\n      ...props\n    },\n    theirRef,\n  ) => {\n    const [headerId, setHeaderId] = useState<string | undefined>(undefined);\n    const theme = mergeDeep(getTheme().modal, customTheme);\n\n    const { context } = useFloating({\n      open: show,\n      onOpenChange: () => onClose && onClose(),\n    });\n\n    const ref = useMergeRefs([context.refs.setFloating, theirRef]);\n\n    const click = useClick(context);\n    const dismiss = useDismiss(context, { outsidePressEvent: \"mousedown\", enabled: dismissible });\n    const role = useRole(context);\n\n    const { getFloatingProps } = useInteractions([click, dismiss, role]);\n\n    if (!show) {\n      return null;\n    }\n\n    return (\n      <ModalContext.Provider value={{ theme, popup, onClose, setHeaderId }}>\n        <FloatingPortal root={root}>\n          <FloatingOverlay\n            lockScroll\n            data-testid=\"modal-overlay\"\n            className={twMerge(\n              theme.root.base,\n              theme.root.positions[position],\n              show ? theme.root.show.on : theme.root.show.off,\n              className,\n            )}\n            {...props}\n          >\n            <FloatingFocusManager context={context} initialFocus={initialFocus}>\n              <div\n                ref={ref}\n                {...getFloatingProps(props)}\n                aria-labelledby={headerId}\n                className={twMerge(theme.content.base, theme.root.sizes[size])}\n              >\n                <div className={theme.content.inner}>{children}</div>\n              </div>\n            </FloatingFocusManager>\n          </FloatingOverlay>\n        </FloatingPortal>\n      </ModalContext.Provider>\n    );\n  },\n);\n\nModalComponent.displayName = \"Modal\";\nModalHeader.displayName = \"Modal.Header\";\nModalBody.displayName = \"Modal.Body\";\nModalFooter.displayName = \"Modal.Footer\";\n\nexport const Modal = Object.assign(ModalComponent, {\n  Header: ModalHeader,\n  Body: ModalBody,\n  Footer: ModalFooter,\n});\n"], "names": [], "mappings": ";;;;;;;;;;;AAsBA,MAAM,cAAc,GAAG,UAAU;AACjC,EAAE,CAAC;AACH,IAAI,QAAQ;AACZ,IAAI,SAAS;AACb,IAAI,WAAW,GAAG,KAAK;AACvB,IAAI,OAAO;AACX,IAAI,KAAK;AACT,IAAI,QAAQ,GAAG,QAAQ;AACvB,IAAI,IAAI;AACR,IAAI,IAAI;AACR,IAAI,IAAI,GAAG,KAAK;AAChB,IAAI,KAAK,EAAE,WAAW,GAAG,EAAE;AAC3B,IAAI,YAAY;AAChB,IAAI,GAAG,KAAK;AACZ,GAAG,EAAE,QAAQ,KAAK;AAClB,IAAI,MAAM,CAAC,QAAQ,EAAE,WAAW,CAAC,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;AACrD,IAAI,MAAM,KAAK,GAAG,SAAS,CAAC,QAAQ,EAAE,CAAC,KAAK,EAAE,WAAW,CAAC,CAAC;AAC3D,IAAI,MAAM,EAAE,OAAO,EAAE,GAAG,WAAW,CAAC;AACpC,MAAM,IAAI,EAAE,IAAI;AAChB,MAAM,YAAY,EAAE,MAAM,OAAO,IAAI,OAAO,EAAE;AAC9C,KAAK,CAAC,CAAC;AACP,IAAI,MAAM,GAAG,GAAG,YAAY,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAC,CAAC;AACnE,IAAI,MAAM,KAAK,GAAG,QAAQ,CAAC,OAAO,CAAC,CAAC;AACpC,IAAI,MAAM,OAAO,GAAG,UAAU,CAAC,OAAO,EAAE,EAAE,iBAAiB,EAAE,WAAW,EAAE,OAAO,EAAE,WAAW,EAAE,CAAC,CAAC;AAClG,IAAI,MAAM,IAAI,GAAG,OAAO,CAAC,OAAO,CAAC,CAAC;AAClC,IAAI,MAAM,EAAE,gBAAgB,EAAE,GAAG,eAAe,CAAC,CAAC,KAAK,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC,CAAC;AACzE,IAAI,IAAI,CAAC,IAAI,EAAE;AACf,MAAM,OAAO,IAAI,CAAC;AAClB,KAAK;AACL,IAAI,uBAAuB,GAAG,CAAC,YAAY,CAAC,QAAQ,EAAE,EAAE,KAAK,EAAE,EAAE,KAAK,EAAE,KAAK,EAAE,OAAO,EAAE,WAAW,EAAE,EAAE,QAAQ,kBAAkB,GAAG,CAAC,cAAc,EAAE,EAAE,IAAI,EAAE,QAAQ,kBAAkB,GAAG;AAC1L,MAAM,eAAe;AACrB,MAAM;AACN,QAAQ,UAAU,EAAE,IAAI;AACxB,QAAQ,aAAa,EAAE,eAAe;AACtC,QAAQ,SAAS,EAAE,OAAO;AAC1B,UAAU,KAAK,CAAC,IAAI,CAAC,IAAI;AACzB,UAAU,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC;AACxC,UAAU,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG;AACzD,UAAU,SAAS;AACnB,SAAS;AACT,QAAQ,GAAG,KAAK;AAChB,QAAQ,QAAQ,kBAAkB,GAAG,CAAC,oBAAoB,EAAE,EAAE,OAAO,EAAE,YAAY,EAAE,QAAQ,kBAAkB,GAAG;AAClH,UAAU,KAAK;AACf,UAAU;AACV,YAAY,GAAG;AACf,YAAY,GAAG,gBAAgB,CAAC,KAAK,CAAC;AACtC,YAAY,iBAAiB,EAAE,QAAQ;AACvC,YAAY,SAAS,EAAE,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,EAAE,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;AAC1E,YAAY,QAAQ,kBAAkB,GAAG,CAAC,KAAK,EAAE,EAAE,SAAS,EAAE,KAAK,CAAC,OAAO,CAAC,KAAK,EAAE,QAAQ,EAAE,CAAC;AAC9F,WAAW;AACX,SAAS,EAAE,CAAC;AACZ,OAAO;AACP,KAAK,EAAE,CAAC,EAAE,CAAC,CAAC;AACZ,GAAG;AACH,CAAC,CAAC;AACF,cAAc,CAAC,WAAW,GAAG,OAAO,CAAC;AACrC,WAAW,CAAC,WAAW,GAAG,cAAc,CAAC;AACzC,SAAS,CAAC,WAAW,GAAG,YAAY,CAAC;AACrC,WAAW,CAAC,WAAW,GAAG,cAAc,CAAC;AAC7B,MAAC,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC,cAAc,EAAE;AACnD,EAAE,MAAM,EAAE,WAAW;AACrB,EAAE,IAAI,EAAE,SAAS;AACjB,EAAE,MAAM,EAAE,WAAW;AACrB,CAAC;;;;"}