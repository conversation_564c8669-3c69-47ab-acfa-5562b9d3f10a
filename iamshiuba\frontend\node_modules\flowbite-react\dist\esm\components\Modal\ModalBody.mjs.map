{"version": 3, "file": "ModalBody.mjs", "sources": ["../../../../src/components/Modal/ModalBody.tsx"], "sourcesContent": ["\"use client\";\n\nimport type { ComponentProps, FC } from \"react\";\nimport { twMerge } from \"tailwind-merge\";\nimport { mergeDeep } from \"../../helpers/merge-deep\";\nimport type { DeepPartial } from \"../../types\";\nimport { useModalContext } from \"./ModalContext\";\n\nexport interface FlowbiteModalBodyTheme {\n  base: string;\n  popup: string;\n}\n\nexport interface ModalBodyProps extends ComponentProps<\"div\"> {\n  theme?: DeepPartial<FlowbiteModalBodyTheme>;\n}\n\nexport const ModalBody: FC<ModalBodyProps> = ({ children, className, theme: customTheme = {}, ...props }) => {\n  const { theme: rootTheme, popup } = useModalContext();\n\n  const theme = mergeDeep(rootTheme.body, customTheme);\n\n  return (\n    <div className={twMerge(theme.base, popup && [theme.popup], className)} {...props}>\n      {children}\n    </div>\n  );\n};\n"], "names": [], "mappings": ";;;;;AAMY,MAAC,SAAS,GAAG,CAAC,EAAE,QAAQ,EAAE,SAAS,EAAE,KAAK,EAAE,WAAW,GAAG,EAAE,EAAE,GAAG,KAAK,EAAE,KAAK;AACzF,EAAE,MAAM,EAAE,KAAK,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,eAAe,EAAE,CAAC;AACxD,EAAE,MAAM,KAAK,GAAG,SAAS,CAAC,SAAS,CAAC,IAAI,EAAE,WAAW,CAAC,CAAC;AACvD,EAAE,uBAAuB,GAAG,CAAC,KAAK,EAAE,EAAE,SAAS,EAAE,OAAO,CAAC,KAAK,CAAC,IAAI,EAAE,KAAK,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE,SAAS,CAAC,EAAE,GAAG,KAAK,EAAE,QAAQ,EAAE,CAAC,CAAC;AAC/H;;;;"}