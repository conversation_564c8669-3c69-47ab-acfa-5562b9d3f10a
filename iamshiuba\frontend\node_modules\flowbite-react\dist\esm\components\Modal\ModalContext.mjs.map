{"version": 3, "file": "ModalContext.mjs", "sources": ["../../../../src/components/Modal/ModalContext.tsx"], "sourcesContent": ["\"use client\";\n\nimport { createContext, useContext } from \"react\";\nimport type { FlowbiteModalTheme } from \"./Modal\";\n\ntype ModalContext = {\n  theme: FlowbiteModalTheme;\n  popup?: boolean;\n  setHeaderId: (id: string | undefined) => void;\n  onClose?: () => void;\n};\n\nexport const ModalContext = createContext<ModalContext | undefined>(undefined);\n\nexport function useModalContext(): ModalContext {\n  const context = useContext(ModalContext);\n\n  if (!context) {\n    throw new Error(\"useModalContext should be used within the ModalContext provider!\");\n  }\n\n  return context;\n}\n"], "names": [], "mappings": ";;AAGY,MAAC,YAAY,GAAG,aAAa,CAAC,KAAK,CAAC,EAAE;AAC3C,SAAS,eAAe,GAAG;AAClC,EAAE,MAAM,OAAO,GAAG,UAAU,CAAC,YAAY,CAAC,CAAC;AAC3C,EAAE,IAAI,CAAC,OAAO,EAAE;AAChB,IAAI,MAAM,IAAI,KAAK,CAAC,kEAAkE,CAAC,CAAC;AACxF,GAAG;AACH,EAAE,OAAO,OAAO,CAAC;AACjB;;;;"}