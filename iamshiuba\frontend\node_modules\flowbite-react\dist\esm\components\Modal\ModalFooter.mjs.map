{"version": 3, "file": "ModalFooter.mjs", "sources": ["../../../../src/components/Modal/ModalFooter.tsx"], "sourcesContent": ["\"use client\";\n\nimport type { ComponentProps, FC } from \"react\";\nimport { twMerge } from \"tailwind-merge\";\nimport { mergeDeep } from \"../../helpers/merge-deep\";\nimport type { DeepPartial } from \"../../types\";\nimport { useModalContext } from \"./ModalContext\";\n\nexport interface FlowbiteModalFooterTheme {\n  base: string;\n  popup: string;\n}\n\nexport interface ModalFooterProps extends ComponentProps<\"div\"> {\n  theme?: DeepPartial<FlowbiteModalFooterTheme>;\n}\n\nexport const ModalFooter: FC<ModalFooterProps> = ({ children, className, theme: customTheme = {}, ...props }) => {\n  const { theme: rootTheme, popup } = useModalContext();\n\n  const theme = mergeDeep(rootTheme.footer, customTheme);\n\n  return (\n    <div className={twMerge(theme.base, !popup && theme.popup, className)} {...props}>\n      {children}\n    </div>\n  );\n};\n"], "names": [], "mappings": ";;;;;AAMY,MAAC,WAAW,GAAG,CAAC,EAAE,QAAQ,EAAE,SAAS,EAAE,KAAK,EAAE,WAAW,GAAG,EAAE,EAAE,GAAG,KAAK,EAAE,KAAK;AAC3F,EAAE,MAAM,EAAE,KAAK,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,eAAe,EAAE,CAAC;AACxD,EAAE,MAAM,KAAK,GAAG,SAAS,CAAC,SAAS,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC;AACzD,EAAE,uBAAuB,GAAG,CAAC,KAAK,EAAE,EAAE,SAAS,EAAE,OAAO,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,KAAK,IAAI,KAAK,CAAC,KAAK,EAAE,SAAS,CAAC,EAAE,GAAG,KAAK,EAAE,QAAQ,EAAE,CAAC,CAAC;AAC9H;;;;"}