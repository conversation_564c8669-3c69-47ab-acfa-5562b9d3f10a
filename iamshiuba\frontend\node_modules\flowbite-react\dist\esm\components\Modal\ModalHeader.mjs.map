{"version": 3, "file": "ModalHeader.mjs", "sources": ["../../../../src/components/Modal/ModalHeader.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useId, useLayoutEffect, type ComponentProps, type ElementType, type FC } from \"react\";\nimport { HiOutlineX } from \"react-icons/hi\";\nimport { twMerge } from \"tailwind-merge\";\nimport { mergeDeep } from \"../../helpers/merge-deep\";\nimport type { DeepPartial } from \"../../types\";\nimport { useModalContext } from \"./ModalContext\";\n\nexport interface FlowbiteModalHeaderTheme {\n  base: string;\n  popup: string;\n  title: string;\n  close: {\n    base: string;\n    icon: string;\n  };\n}\n\nexport interface ModalHeaderProps extends ComponentProps<\"div\"> {\n  as?: ElementType;\n  theme?: DeepPartial<FlowbiteModalHeaderTheme>;\n}\n\nexport const ModalHeader: FC<ModalHeaderProps> = ({\n  as: Component = \"h3\",\n  children,\n  className,\n  theme: customTheme = {},\n  id,\n  ...props\n}) => {\n  const innerHeaderId = useId();\n  const headerId = id || innerHeaderId;\n\n  const { theme: rootTheme, popup, onClose, setHeaderId } = useModalContext();\n\n  const theme = mergeDeep(rootTheme.header, customTheme);\n\n  useLayoutEffect(() => {\n    setHeaderId(headerId);\n\n    return () => setHeaderId(undefined);\n  }, [headerId, setHeaderId]);\n\n  return (\n    <div className={twMerge(theme.base, popup && theme.popup, className)} {...props}>\n      <Component id={headerId} className={theme.title}>\n        {children}\n      </Component>\n      <button aria-label=\"Close\" className={theme.close.base} type=\"button\" onClick={onClose}>\n        <HiOutlineX aria-hidden className={theme.close.icon} />\n      </button>\n    </div>\n  );\n};\n"], "names": [], "mappings": ";;;;;;;AAQY,MAAC,WAAW,GAAG,CAAC;AAC5B,EAAE,EAAE,EAAE,SAAS,GAAG,IAAI;AACtB,EAAE,QAAQ;AACV,EAAE,SAAS;AACX,EAAE,KAAK,EAAE,WAAW,GAAG,EAAE;AACzB,EAAE,EAAE;AACJ,EAAE,GAAG,KAAK;AACV,CAAC,KAAK;AACN,EAAE,MAAM,aAAa,GAAG,KAAK,EAAE,CAAC;AAChC,EAAE,MAAM,QAAQ,GAAG,EAAE,IAAI,aAAa,CAAC;AACvC,EAAE,MAAM,EAAE,KAAK,EAAE,SAAS,EAAE,KAAK,EAAE,OAAO,EAAE,WAAW,EAAE,GAAG,eAAe,EAAE,CAAC;AAC9E,EAAE,MAAM,KAAK,GAAG,SAAS,CAAC,SAAS,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC;AACzD,EAAE,eAAe,CAAC,MAAM;AACxB,IAAI,WAAW,CAAC,QAAQ,CAAC,CAAC;AAC1B,IAAI,OAAO,MAAM,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC;AACrC,GAAG,EAAE,CAAC,QAAQ,EAAE,WAAW,CAAC,CAAC,CAAC;AAC9B,EAAE,uBAAuB,IAAI,CAAC,KAAK,EAAE,EAAE,SAAS,EAAE,OAAO,CAAC,KAAK,CAAC,IAAI,EAAE,KAAK,IAAI,KAAK,CAAC,KAAK,EAAE,SAAS,CAAC,EAAE,GAAG,KAAK,EAAE,QAAQ,EAAE;AAC5H,oBAAoB,GAAG,CAAC,SAAS,EAAE,EAAE,EAAE,EAAE,QAAQ,EAAE,SAAS,EAAE,KAAK,CAAC,KAAK,EAAE,QAAQ,EAAE,CAAC;AACtF,oBAAoB,GAAG,CAAC,QAAQ,EAAE,EAAE,YAAY,EAAE,OAAO,EAAE,SAAS,EAAE,KAAK,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAE,OAAO,EAAE,OAAO,EAAE,QAAQ,kBAAkB,GAAG,CAAC,UAAU,EAAE,EAAE,aAAa,EAAE,IAAI,EAAE,SAAS,EAAE,KAAK,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC;AAC5N,GAAG,EAAE,CAAC,CAAC;AACP;;;;"}