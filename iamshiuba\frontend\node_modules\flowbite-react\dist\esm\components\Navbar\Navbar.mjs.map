{"version": 3, "file": "Navbar.mjs", "sources": ["../../../../src/components/Navbar/Navbar.tsx"], "sourcesContent": ["\"use client\";\n\nimport type { ComponentProps, FC } from \"react\";\nimport { useState } from \"react\";\nimport { twMerge } from \"tailwind-merge\";\nimport { mergeDeep } from \"../../helpers/merge-deep\";\nimport { getTheme } from \"../../theme-store\";\nimport type { DeepPartial } from \"../../types\";\nimport type { FlowbiteBoolean } from \"../Flowbite\";\nimport type { FlowbiteNavbarBrandTheme } from \"./NavbarBrand\";\nimport { NavbarBrand } from \"./NavbarBrand\";\nimport type { FlowbiteNavbarCollapseTheme } from \"./NavbarCollapse\";\nimport { NavbarCollapse } from \"./NavbarCollapse\";\nimport { NavbarContext } from \"./NavbarContext\";\nimport type { FlowbiteNavbarLinkTheme } from \"./NavbarLink\";\nimport { NavbarLink } from \"./NavbarLink\";\nimport type { FlowbiteNavbarToggleTheme } from \"./NavbarToggle\";\nimport { NavbarToggle } from \"./NavbarToggle\";\n\nexport interface FlowbiteNavbarTheme {\n  root: FlowbiteNavbarRootTheme;\n  brand: FlowbiteNavbarBrandTheme;\n  collapse: FlowbiteNavbarCollapseTheme;\n  link: FlowbiteNavbarLinkTheme;\n  toggle: FlowbiteNavbarToggleTheme;\n}\n\nexport interface FlowbiteNavbarRootTheme {\n  base: string;\n  rounded: FlowbiteBoolean;\n  bordered: FlowbiteBoolean;\n  inner: {\n    base: string;\n    fluid: FlowbiteBoolean;\n  };\n}\n\nexport interface NavbarComponentProps extends ComponentProps<\"nav\"> {\n  menuOpen?: boolean;\n  fluid?: boolean;\n  rounded?: boolean;\n  border?: boolean;\n  theme?: DeepPartial<FlowbiteNavbarTheme>;\n}\n\nconst NavbarComponent: FC<NavbarComponentProps> = ({\n  border,\n  children,\n  className,\n  fluid = false,\n  menuOpen,\n  rounded,\n  theme: customTheme = {},\n  ...props\n}) => {\n  const [isOpen, setIsOpen] = useState(menuOpen);\n\n  const theme = mergeDeep(getTheme().navbar, customTheme);\n\n  return (\n    <NavbarContext.Provider value={{ theme, isOpen, setIsOpen }}>\n      <nav\n        className={twMerge(\n          theme.root.base,\n          theme.root.bordered[border ? \"on\" : \"off\"],\n          theme.root.rounded[rounded ? \"on\" : \"off\"],\n          className,\n        )}\n        {...props}\n      >\n        <div className={twMerge(theme.root.inner.base, theme.root.inner.fluid[fluid ? \"on\" : \"off\"])}>{children}</div>\n      </nav>\n    </NavbarContext.Provider>\n  );\n};\n\nNavbarComponent.displayName = \"Navbar\";\nNavbarBrand.displayName = \"Navbar.Brand\";\nNavbarCollapse.displayName = \"Navbar.Collapse\";\nNavbarLink.displayName = \"Navbar.Link\";\nNavbarToggle.displayName = \"Navbar.Toggle\";\n\nexport const Navbar = Object.assign(NavbarComponent, {\n  Brand: NavbarBrand,\n  Collapse: NavbarCollapse,\n  Link: NavbarLink,\n  Toggle: NavbarToggle,\n});\n"], "names": [], "mappings": ";;;;;;;;;;;AAYA,MAAM,eAAe,GAAG,CAAC;AACzB,EAAE,MAAM;AACR,EAAE,QAAQ;AACV,EAAE,SAAS;AACX,EAAE,KAAK,GAAG,KAAK;AACf,EAAE,QAAQ;AACV,EAAE,OAAO;AACT,EAAE,KAAK,EAAE,WAAW,GAAG,EAAE;AACzB,EAAE,GAAG,KAAK;AACV,CAAC,KAAK;AACN,EAAE,MAAM,CAAC,MAAM,EAAE,SAAS,CAAC,GAAG,QAAQ,CAAC,QAAQ,CAAC,CAAC;AACjD,EAAE,MAAM,KAAK,GAAG,SAAS,CAAC,QAAQ,EAAE,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC;AAC1D,EAAE,uBAAuB,GAAG,CAAC,aAAa,CAAC,QAAQ,EAAE,EAAE,KAAK,EAAE,EAAE,KAAK,EAAE,MAAM,EAAE,SAAS,EAAE,EAAE,QAAQ,kBAAkB,GAAG;AACzH,IAAI,KAAK;AACT,IAAI;AACJ,MAAM,SAAS,EAAE,OAAO;AACxB,QAAQ,KAAK,CAAC,IAAI,CAAC,IAAI;AACvB,QAAQ,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,IAAI,GAAG,KAAK,CAAC;AAClD,QAAQ,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,GAAG,IAAI,GAAG,KAAK,CAAC;AAClD,QAAQ,SAAS;AACjB,OAAO;AACP,MAAM,GAAG,KAAK;AACd,MAAM,QAAQ,kBAAkB,GAAG,CAAC,KAAK,EAAE,EAAE,SAAS,EAAE,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,GAAG,IAAI,GAAG,KAAK,CAAC,CAAC,EAAE,QAAQ,EAAE,CAAC;AACjJ,KAAK;AACL,GAAG,EAAE,CAAC,CAAC;AACP,CAAC,CAAC;AACF,eAAe,CAAC,WAAW,GAAG,QAAQ,CAAC;AACvC,WAAW,CAAC,WAAW,GAAG,cAAc,CAAC;AACzC,cAAc,CAAC,WAAW,GAAG,iBAAiB,CAAC;AAC/C,UAAU,CAAC,WAAW,GAAG,aAAa,CAAC;AACvC,YAAY,CAAC,WAAW,GAAG,eAAe,CAAC;AAC/B,MAAC,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,eAAe,EAAE;AACrD,EAAE,KAAK,EAAE,WAAW;AACpB,EAAE,QAAQ,EAAE,cAAc;AAC1B,EAAE,IAAI,EAAE,UAAU;AAClB,EAAE,MAAM,EAAE,YAAY;AACtB,CAAC;;;;"}