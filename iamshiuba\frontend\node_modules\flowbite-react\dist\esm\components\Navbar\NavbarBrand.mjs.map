{"version": 3, "file": "NavbarBrand.mjs", "sources": ["../../../../src/components/Navbar/NavbarBrand.tsx"], "sourcesContent": ["\"use client\";\n\nimport type { ComponentProps, ElementType, FC } from \"react\";\nimport { twMerge } from \"tailwind-merge\";\nimport { mergeDeep } from \"../../helpers/merge-deep\";\nimport type { DeepPartial } from \"../../types\";\nimport { useNavbarContext } from \"./NavbarContext\";\n\nexport interface FlowbiteNavbarBrandTheme {\n  base: string;\n}\n\nexport interface NavbarBrandProps extends ComponentProps<\"a\">, Record<string, unknown> {\n  as?: ElementType;\n  href?: string;\n  theme?: DeepPartial<FlowbiteNavbarBrandTheme>;\n}\n\nexport const NavbarBrand: FC<NavbarBrandProps> = ({\n  as: Component = \"a\",\n  children,\n  className,\n  theme: customTheme = {},\n  ...props\n}) => {\n  const { theme: rootTheme } = useNavbarContext();\n\n  const theme = mergeDeep(rootTheme.brand, customTheme);\n\n  return (\n    <Component className={twMerge(theme.base, className)} {...props}>\n      {children}\n    </Component>\n  );\n};\n"], "names": [], "mappings": ";;;;;AAMY,MAAC,WAAW,GAAG,CAAC;AAC5B,EAAE,EAAE,EAAE,SAAS,GAAG,GAAG;AACrB,EAAE,QAAQ;AACV,EAAE,SAAS;AACX,EAAE,KAAK,EAAE,WAAW,GAAG,EAAE;AACzB,EAAE,GAAG,KAAK;AACV,CAAC,KAAK;AACN,EAAE,MAAM,EAAE,KAAK,EAAE,SAAS,EAAE,GAAG,gBAAgB,EAAE,CAAC;AAClD,EAAE,MAAM,KAAK,GAAG,SAAS,CAAC,SAAS,CAAC,KAAK,EAAE,WAAW,CAAC,CAAC;AACxD,EAAE,uBAAuB,GAAG,CAAC,SAAS,EAAE,EAAE,SAAS,EAAE,OAAO,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC,EAAE,GAAG,KAAK,EAAE,QAAQ,EAAE,CAAC,CAAC;AAC3G;;;;"}