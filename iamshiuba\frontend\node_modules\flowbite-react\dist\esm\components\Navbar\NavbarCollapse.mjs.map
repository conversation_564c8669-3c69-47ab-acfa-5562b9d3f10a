{"version": 3, "file": "NavbarCollapse.mjs", "sources": ["../../../../src/components/Navbar/NavbarCollapse.tsx"], "sourcesContent": ["\"use client\";\n\nimport type { ComponentProps, FC } from \"react\";\nimport { twMerge } from \"tailwind-merge\";\nimport { mergeDeep } from \"../../helpers/merge-deep\";\nimport type { DeepPartial } from \"../../types\";\nimport type { FlowbiteBoolean } from \"../Flowbite\";\nimport { useNavbarContext } from \"./NavbarContext\";\n\nexport interface FlowbiteNavbarCollapseTheme {\n  base: string;\n  list: string;\n  hidden: FlowbiteBoolean;\n}\n\nexport interface NavbarCollapseProps extends ComponentProps<\"div\"> {\n  theme?: DeepPartial<FlowbiteNavbarCollapseTheme>;\n}\n\nexport const NavbarCollapse: FC<NavbarCollapseProps> = ({ children, className, theme: customTheme = {}, ...props }) => {\n  const { theme: rootTheme, isOpen } = useNavbarContext();\n\n  const theme = mergeDeep(rootTheme.collapse, customTheme);\n\n  return (\n    <div\n      data-testid=\"flowbite-navbar-collapse\"\n      className={twMerge(theme.base, theme.hidden[!isOpen ? \"on\" : \"off\"], className)}\n      {...props}\n    >\n      <ul className={theme.list}>{children}</ul>\n    </div>\n  );\n};\n"], "names": [], "mappings": ";;;;;AAMY,MAAC,cAAc,GAAG,CAAC,EAAE,QAAQ,EAAE,SAAS,EAAE,KAAK,EAAE,WAAW,GAAG,EAAE,EAAE,GAAG,KAAK,EAAE,KAAK;AAC9F,EAAE,MAAM,EAAE,KAAK,EAAE,SAAS,EAAE,MAAM,EAAE,GAAG,gBAAgB,EAAE,CAAC;AAC1D,EAAE,MAAM,KAAK,GAAG,SAAS,CAAC,SAAS,CAAC,QAAQ,EAAE,WAAW,CAAC,CAAC;AAC3D,EAAE,uBAAuB,GAAG;AAC5B,IAAI,KAAK;AACT,IAAI;AACJ,MAAM,aAAa,EAAE,0BAA0B;AAC/C,MAAM,SAAS,EAAE,OAAO,CAAC,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC,MAAM,GAAG,IAAI,GAAG,KAAK,CAAC,EAAE,SAAS,CAAC;AACrF,MAAM,GAAG,KAAK;AACd,MAAM,QAAQ,kBAAkB,GAAG,CAAC,IAAI,EAAE,EAAE,SAAS,EAAE,KAAK,CAAC,IAAI,EAAE,QAAQ,EAAE,CAAC;AAC9E,KAAK;AACL,GAAG,CAAC;AACJ;;;;"}