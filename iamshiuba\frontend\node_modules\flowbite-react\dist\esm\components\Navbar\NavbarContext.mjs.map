{"version": 3, "file": "NavbarContext.mjs", "sources": ["../../../../src/components/Navbar/NavbarContext.tsx"], "sourcesContent": ["\"use client\";\n\nimport { createContext, useContext } from \"react\";\nimport type { FlowbiteNavbarTheme } from \"./Navbar\";\n\ntype NavbarContext = {\n  theme: FlowbiteNavbarTheme;\n  isOpen?: boolean;\n  setIsOpen: (isOpen: boolean) => void;\n};\n\nexport const NavbarContext = createContext<NavbarContext | undefined>(undefined);\n\nexport function useNavbarContext(): NavbarContext {\n  const context = useContext(NavbarContext);\n\n  if (!context) {\n    throw new Error(\"useNavBarContext should be used within the NavbarContext provider!\");\n  }\n\n  return context;\n}\n"], "names": [], "mappings": ";;AAGY,MAAC,aAAa,GAAG,aAAa,CAAC,KAAK,CAAC,EAAE;AAC5C,SAAS,gBAAgB,GAAG;AACnC,EAAE,MAAM,OAAO,GAAG,UAAU,CAAC,aAAa,CAAC,CAAC;AAC5C,EAAE,IAAI,CAAC,OAAO,EAAE;AAChB,IAAI,MAAM,IAAI,KAAK,CAAC,oEAAoE,CAAC,CAAC;AAC1F,GAAG;AACH,EAAE,OAAO,OAAO,CAAC;AACjB;;;;"}