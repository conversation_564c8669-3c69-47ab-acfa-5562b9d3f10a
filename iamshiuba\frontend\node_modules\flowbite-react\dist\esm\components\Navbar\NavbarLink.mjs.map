{"version": 3, "file": "NavbarLink.mjs", "sources": ["../../../../src/components/Navbar/NavbarLink.tsx"], "sourcesContent": ["\"use client\";\n\nimport type { ComponentProps, ElementType, FC, MouseEvent } from \"react\";\nimport { twMerge } from \"tailwind-merge\";\nimport { mergeDeep } from \"../../helpers/merge-deep\";\nimport type { DeepPartial } from \"../../types\";\nimport type { FlowbiteBoolean } from \"../Flowbite\";\nimport { useNavbarContext } from \"./NavbarContext\";\n\nexport interface FlowbiteNavbarLinkTheme {\n  base: string;\n  active: FlowbiteBoolean;\n  disabled: FlowbiteBoolean;\n}\n\nexport interface NavbarLinkProps extends ComponentProps<\"a\">, Record<string, unknown> {\n  active?: boolean;\n  as?: ElementType;\n  disabled?: boolean;\n  href?: string;\n  theme?: DeepPartial<FlowbiteNavbarLinkTheme>;\n}\n\nexport const NavbarLink: FC<NavbarLinkProps> = ({\n  active,\n  as: Component = \"a\",\n  disabled,\n  children,\n  className,\n  theme: customTheme = {},\n  onClick,\n  ...props\n}) => {\n  const { theme: rootTheme, setIsOpen } = useNavbarContext();\n\n  const theme = mergeDeep(rootTheme.link, customTheme);\n\n  const handleClick = (event: MouseEvent<HTMLAnchorElement>) => {\n    setIsOpen(false);\n    onClick?.(event);\n  };\n\n  return (\n    <li>\n      <Component\n        className={twMerge(\n          theme.base,\n          active && theme.active.on,\n          !active && !disabled && theme.active.off,\n          theme.disabled[disabled ? \"on\" : \"off\"],\n          className,\n        )}\n        onClick={handleClick}\n        {...props}\n      >\n        {children}\n      </Component>\n    </li>\n  );\n};\n"], "names": [], "mappings": ";;;;;AAMY,MAAC,UAAU,GAAG,CAAC;AAC3B,EAAE,MAAM;AACR,EAAE,EAAE,EAAE,SAAS,GAAG,GAAG;AACrB,EAAE,QAAQ;AACV,EAAE,QAAQ;AACV,EAAE,SAAS;AACX,EAAE,KAAK,EAAE,WAAW,GAAG,EAAE;AACzB,EAAE,OAAO;AACT,EAAE,GAAG,KAAK;AACV,CAAC,KAAK;AACN,EAAE,MAAM,EAAE,KAAK,EAAE,SAAS,EAAE,SAAS,EAAE,GAAG,gBAAgB,EAAE,CAAC;AAC7D,EAAE,MAAM,KAAK,GAAG,SAAS,CAAC,SAAS,CAAC,IAAI,EAAE,WAAW,CAAC,CAAC;AACvD,EAAE,MAAM,WAAW,GAAG,CAAC,KAAK,KAAK;AACjC,IAAI,SAAS,CAAC,KAAK,CAAC,CAAC;AACrB,IAAI,OAAO,GAAG,KAAK,CAAC,CAAC;AACrB,GAAG,CAAC;AACJ,EAAE,uBAAuB,GAAG,CAAC,IAAI,EAAE,EAAE,QAAQ,kBAAkB,GAAG;AAClE,IAAI,SAAS;AACb,IAAI;AACJ,MAAM,SAAS,EAAE,OAAO;AACxB,QAAQ,KAAK,CAAC,IAAI;AAClB,QAAQ,MAAM,IAAI,KAAK,CAAC,MAAM,CAAC,EAAE;AACjC,QAAQ,CAAC,MAAM,IAAI,CAAC,QAAQ,IAAI,KAAK,CAAC,MAAM,CAAC,GAAG;AAChD,QAAQ,KAAK,CAAC,QAAQ,CAAC,QAAQ,GAAG,IAAI,GAAG,KAAK,CAAC;AAC/C,QAAQ,SAAS;AACjB,OAAO;AACP,MAAM,OAAO,EAAE,WAAW;AAC1B,MAAM,GAAG,KAAK;AACd,MAAM,QAAQ;AACd,KAAK;AACL,GAAG,EAAE,CAAC,CAAC;AACP;;;;"}