{"version": 3, "file": "NavbarToggle.mjs", "sources": ["../../../../src/components/Navbar/NavbarToggle.tsx"], "sourcesContent": ["\"use client\";\n\nimport type { ComponentProps, FC } from \"react\";\nimport { FaBars } from \"react-icons/fa\";\nimport { twMerge } from \"tailwind-merge\";\nimport { mergeDeep } from \"../../helpers/merge-deep\";\nimport type { DeepPartial } from \"../../types\";\nimport { useNavbarContext } from \"./NavbarContext\";\n\nexport interface FlowbiteNavbarToggleTheme {\n  base: string;\n  icon: string;\n}\n\nexport interface NavbarToggleProps extends ComponentProps<\"button\"> {\n  barIcon?: FC<ComponentProps<\"svg\">>;\n  theme?: DeepPartial<FlowbiteNavbarToggleTheme>;\n}\n\nexport const NavbarToggle: FC<NavbarToggleProps> = ({\n  barIcon: BarIcon = FaBars,\n  className,\n  theme: customTheme = {},\n  ...props\n}) => {\n  const { theme: rootTheme, isOpen, setIsOpen } = useNavbarContext();\n\n  const theme = mergeDeep(rootTheme.toggle, customTheme);\n\n  const handleClick = () => {\n    setIsOpen(!isOpen);\n  };\n\n  return (\n    <button\n      data-testid=\"flowbite-navbar-toggle\"\n      onClick={handleClick}\n      className={twMerge(theme.base, className)}\n      {...props}\n    >\n      <span className=\"sr-only\">Open main menu</span>\n      <BarIcon aria-hidden className={theme.icon} />\n    </button>\n  );\n};\n"], "names": [], "mappings": ";;;;;;AAOY,MAAC,YAAY,GAAG,CAAC;AAC7B,EAAE,OAAO,EAAE,OAAO,GAAG,MAAM;AAC3B,EAAE,SAAS;AACX,EAAE,KAAK,EAAE,WAAW,GAAG,EAAE;AACzB,EAAE,GAAG,KAAK;AACV,CAAC,KAAK;AACN,EAAE,MAAM,EAAE,KAAK,EAAE,SAAS,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,gBAAgB,EAAE,CAAC;AACrE,EAAE,MAAM,KAAK,GAAG,SAAS,CAAC,SAAS,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC;AACzD,EAAE,MAAM,WAAW,GAAG,MAAM;AAC5B,IAAI,SAAS,CAAC,CAAC,MAAM,CAAC,CAAC;AACvB,GAAG,CAAC;AACJ,EAAE,uBAAuB,IAAI;AAC7B,IAAI,QAAQ;AACZ,IAAI;AACJ,MAAM,aAAa,EAAE,wBAAwB;AAC7C,MAAM,OAAO,EAAE,WAAW;AAC1B,MAAM,SAAS,EAAE,OAAO,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC;AAC/C,MAAM,GAAG,KAAK;AACd,MAAM,QAAQ,EAAE;AAChB,wBAAwB,GAAG,CAAC,MAAM,EAAE,EAAE,SAAS,EAAE,SAAS,EAAE,QAAQ,EAAE,gBAAgB,EAAE,CAAC;AACzF,wBAAwB,GAAG,CAAC,OAAO,EAAE,EAAE,aAAa,EAAE,IAAI,EAAE,SAAS,EAAE,KAAK,CAAC,IAAI,EAAE,CAAC;AACpF,OAAO;AACP,KAAK;AACL,GAAG,CAAC;AACJ;;;;"}