{"version": 3, "file": "Pagination.mjs", "sources": ["../../../../src/components/Pagination/Pagination.tsx"], "sourcesContent": ["import type { ComponentProps, FC, ReactNode } from \"react\";\nimport { HiChevronLeft, HiChevronRight } from \"react-icons/hi\";\nimport { twMerge } from \"tailwind-merge\";\nimport { mergeDeep } from \"../../helpers/merge-deep\";\nimport { getTheme } from \"../../theme-store\";\nimport type { DeepPartial } from \"../../types\";\nimport { range } from \"./helpers\";\nimport type { FlowbitePaginationButtonTheme, PaginationButtonProps } from \"./PaginationButton\";\nimport { PaginationButton, PaginationNavigation } from \"./PaginationButton\";\n\nexport interface FlowbitePaginationTheme {\n  base: string;\n  layout: FlowbitePaginationLayoutTheme;\n  pages: FlowbitePaginationPagesTheme;\n}\n\nexport interface FlowbitePaginationRootTheme {\n  base: string;\n}\n\nexport interface FlowbitePaginationLayoutTheme {\n  table: {\n    base: string;\n    span: string;\n  };\n}\n\nexport interface FlowbitePaginationPagesTheme {\n  base: string;\n  showIcon: string;\n  previous: FlowbitePaginationNavigationTheme;\n  next: FlowbitePaginationNavigationTheme;\n  selector: FlowbitePaginationButtonTheme;\n}\n\nexport interface FlowbitePaginationNavigationTheme {\n  base: string;\n  icon: string;\n}\n\nexport interface PaginationProps extends ComponentProps<\"nav\"> {\n  currentPage: number;\n  layout?: \"navigation\" | \"pagination\" | \"table\";\n  nextLabel?: string;\n  onPageChange: (page: number) => void;\n  previousLabel?: string;\n  renderPaginationButton?: (props: PaginationButtonProps) => ReactNode;\n  showIcons?: boolean;\n  theme?: DeepPartial<FlowbitePaginationTheme>;\n  totalPages: number;\n}\n\nconst PaginationComponent: FC<PaginationProps> = ({\n  className,\n  currentPage,\n  layout = \"pagination\",\n  nextLabel = \"Next\",\n  onPageChange,\n  previousLabel = \"Previous\",\n  renderPaginationButton = (props) => <PaginationButton {...props} />,\n  showIcons: showIcon = false,\n  theme: customTheme = {},\n  totalPages,\n  ...props\n}) => {\n  const theme = mergeDeep(getTheme().pagination, customTheme);\n\n  const lastPage = Math.min(Math.max(layout === \"pagination\" ? currentPage + 2 : currentPage + 4, 5), totalPages);\n  const firstPage = Math.max(1, lastPage - 4);\n\n  const goToNextPage = (): void => {\n    onPageChange(Math.min(currentPage + 1, totalPages));\n  };\n\n  const goToPreviousPage = (): void => {\n    onPageChange(Math.max(currentPage - 1, 1));\n  };\n\n  return (\n    <nav className={twMerge(theme.base, className)} {...props}>\n      {layout === \"table\" && (\n        <div className={theme.layout.table.base}>\n          Showing <span className={theme.layout.table.span}>{firstPage}</span> to&nbsp;\n          <span className={theme.layout.table.span}>{lastPage}</span> of&nbsp;\n          <span className={theme.layout.table.span}>{totalPages}</span> Entries\n        </div>\n      )}\n      <ul className={theme.pages.base}>\n        <li>\n          <PaginationNavigation\n            className={twMerge(theme.pages.previous.base, showIcon && theme.pages.showIcon)}\n            onClick={goToPreviousPage}\n            disabled={currentPage === 1}\n          >\n            {showIcon && <HiChevronLeft aria-hidden className={theme.pages.previous.icon} />}\n            {previousLabel}\n          </PaginationNavigation>\n        </li>\n        {layout === \"pagination\" &&\n          range(firstPage, lastPage).map((page: number) => (\n            <li aria-current={page === currentPage ? \"page\" : undefined} key={page}>\n              {renderPaginationButton({\n                className: twMerge(theme.pages.selector.base, currentPage === page && theme.pages.selector.active),\n                active: page === currentPage,\n                onClick: () => onPageChange(page),\n                children: page,\n              })}\n            </li>\n          ))}\n        <li>\n          <PaginationNavigation\n            className={twMerge(theme.pages.next.base, showIcon && theme.pages.showIcon)}\n            onClick={goToNextPage}\n            disabled={currentPage === totalPages}\n          >\n            {nextLabel}\n            {showIcon && <HiChevronRight aria-hidden className={theme.pages.next.icon} />}\n          </PaginationNavigation>\n        </li>\n      </ul>\n    </nav>\n  );\n};\n\nPaginationComponent.displayName = \"Pagination\";\n\nexport const Pagination = Object.assign(PaginationComponent, {\n  Button: PaginationButton,\n});\n"], "names": [], "mappings": ";;;;;;;;AAQA,MAAM,mBAAmB,GAAG,CAAC;AAC7B,EAAE,SAAS;AACX,EAAE,WAAW;AACb,EAAE,MAAM,GAAG,YAAY;AACvB,EAAE,SAAS,GAAG,MAAM;AACpB,EAAE,YAAY;AACd,EAAE,aAAa,GAAG,UAAU;AAC5B,EAAE,sBAAsB,GAAG,CAAC,MAAM,qBAAqB,GAAG,CAAC,gBAAgB,EAAE,EAAE,GAAG,MAAM,EAAE,CAAC;AAC3F,EAAE,SAAS,EAAE,QAAQ,GAAG,KAAK;AAC7B,EAAE,KAAK,EAAE,WAAW,GAAG,EAAE;AACzB,EAAE,UAAU;AACZ,EAAE,GAAG,KAAK;AACV,CAAC,KAAK;AACN,EAAE,MAAM,KAAK,GAAG,SAAS,CAAC,QAAQ,EAAE,CAAC,UAAU,EAAE,WAAW,CAAC,CAAC;AAC9D,EAAE,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,MAAM,KAAK,YAAY,GAAG,WAAW,GAAG,CAAC,GAAG,WAAW,GAAG,CAAC,EAAE,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC;AAClH,EAAE,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,QAAQ,GAAG,CAAC,CAAC,CAAC;AAC9C,EAAE,MAAM,YAAY,GAAG,MAAM;AAC7B,IAAI,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,WAAW,GAAG,CAAC,EAAE,UAAU,CAAC,CAAC,CAAC;AACxD,GAAG,CAAC;AACJ,EAAE,MAAM,gBAAgB,GAAG,MAAM;AACjC,IAAI,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,WAAW,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AAC/C,GAAG,CAAC;AACJ,EAAE,uBAAuB,IAAI,CAAC,KAAK,EAAE,EAAE,SAAS,EAAE,OAAO,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC,EAAE,GAAG,KAAK,EAAE,QAAQ,EAAE;AACtG,IAAI,MAAM,KAAK,OAAO,oBAAoB,IAAI,CAAC,KAAK,EAAE,EAAE,SAAS,EAAE,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,EAAE,QAAQ,EAAE;AACtG,MAAM,UAAU;AAChB,sBAAsB,GAAG,CAAC,MAAM,EAAE,EAAE,SAAS,EAAE,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,EAAE,QAAQ,EAAE,SAAS,EAAE,CAAC;AAC9F,MAAM,SAAS;AACf,sBAAsB,GAAG,CAAC,MAAM,EAAE,EAAE,SAAS,EAAE,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,EAAE,QAAQ,EAAE,QAAQ,EAAE,CAAC;AAC7F,MAAM,SAAS;AACf,sBAAsB,GAAG,CAAC,MAAM,EAAE,EAAE,SAAS,EAAE,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,EAAE,QAAQ,EAAE,UAAU,EAAE,CAAC;AAC/F,MAAM,UAAU;AAChB,KAAK,EAAE,CAAC;AACR,oBAAoB,IAAI,CAAC,IAAI,EAAE,EAAE,SAAS,EAAE,KAAK,CAAC,KAAK,CAAC,IAAI,EAAE,QAAQ,EAAE;AACxE,sBAAsB,GAAG,CAAC,IAAI,EAAE,EAAE,QAAQ,kBAAkB,IAAI;AAChE,QAAQ,oBAAoB;AAC5B,QAAQ;AACR,UAAU,SAAS,EAAE,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,QAAQ,CAAC,IAAI,EAAE,QAAQ,IAAI,KAAK,CAAC,KAAK,CAAC,QAAQ,CAAC;AACzF,UAAU,OAAO,EAAE,gBAAgB;AACnC,UAAU,QAAQ,EAAE,WAAW,KAAK,CAAC;AACrC,UAAU,QAAQ,EAAE;AACpB,YAAY,QAAQ,oBAAoB,GAAG,CAAC,aAAa,EAAE,EAAE,aAAa,EAAE,IAAI,EAAE,SAAS,EAAE,KAAK,CAAC,KAAK,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;AACzH,YAAY,aAAa;AACzB,WAAW;AACX,SAAS;AACT,OAAO,EAAE,CAAC;AACV,MAAM,MAAM,KAAK,YAAY,IAAI,KAAK,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,qBAAqB,GAAG,CAAC,IAAI,EAAE,EAAE,cAAc,EAAE,IAAI,KAAK,WAAW,GAAG,MAAM,GAAG,KAAK,CAAC,EAAE,QAAQ,EAAE,sBAAsB,CAAC;AAC/L,QAAQ,SAAS,EAAE,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,QAAQ,CAAC,IAAI,EAAE,WAAW,KAAK,IAAI,IAAI,KAAK,CAAC,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC;AAC1G,QAAQ,MAAM,EAAE,IAAI,KAAK,WAAW;AACpC,QAAQ,OAAO,EAAE,MAAM,YAAY,CAAC,IAAI,CAAC;AACzC,QAAQ,QAAQ,EAAE,IAAI;AACtB,OAAO,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC;AAClB,sBAAsB,GAAG,CAAC,IAAI,EAAE,EAAE,QAAQ,kBAAkB,IAAI;AAChE,QAAQ,oBAAoB;AAC5B,QAAQ;AACR,UAAU,SAAS,EAAE,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,EAAE,QAAQ,IAAI,KAAK,CAAC,KAAK,CAAC,QAAQ,CAAC;AACrF,UAAU,OAAO,EAAE,YAAY;AAC/B,UAAU,QAAQ,EAAE,WAAW,KAAK,UAAU;AAC9C,UAAU,QAAQ,EAAE;AACpB,YAAY,SAAS;AACrB,YAAY,QAAQ,oBAAoB,GAAG,CAAC,cAAc,EAAE,EAAE,aAAa,EAAE,IAAI,EAAE,SAAS,EAAE,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;AACtH,WAAW;AACX,SAAS;AACT,OAAO,EAAE,CAAC;AACV,KAAK,EAAE,CAAC;AACR,GAAG,EAAE,CAAC,CAAC;AACP,CAAC,CAAC;AACF,mBAAmB,CAAC,WAAW,GAAG,YAAY,CAAC;AACnC,MAAC,UAAU,GAAG,MAAM,CAAC,MAAM,CAAC,mBAAmB,EAAE;AAC7D,EAAE,MAAM,EAAE,gBAAgB;AAC1B,CAAC;;;;"}