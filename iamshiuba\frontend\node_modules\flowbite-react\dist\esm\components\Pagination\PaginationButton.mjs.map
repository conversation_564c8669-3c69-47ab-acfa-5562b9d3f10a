{"version": 3, "file": "PaginationButton.mjs", "sources": ["../../../../src/components/Pagination/PaginationButton.tsx"], "sourcesContent": ["import type { ComponentProps, FC, ReactEventHandler, ReactNode } from \"react\";\nimport { twMerge } from \"tailwind-merge\";\nimport { mergeDeep } from \"../../helpers/merge-deep\";\nimport { getTheme } from \"../../theme-store\";\nimport type { DeepPartial } from \"../../types\";\n\nexport interface FlowbitePaginationButtonTheme {\n  base: string;\n  active: string;\n  disabled: string;\n}\n\nexport interface PaginationButtonProps extends ComponentProps<\"button\"> {\n  active?: boolean;\n  children?: ReactNode;\n  className?: string;\n  onClick?: ReactEventHandler<HTMLButtonElement>;\n  theme?: DeepPartial<FlowbitePaginationButtonTheme>;\n}\n\nexport interface PaginationPrevButtonProps extends Omit<PaginationButtonProps, \"active\"> {\n  disabled?: boolean;\n}\n\nexport const PaginationButton: FC<PaginationButtonProps> = ({\n  active,\n  children,\n  className,\n  onClick,\n  theme: customTheme = {},\n  ...props\n}) => {\n  const theme = mergeDeep(getTheme().pagination, customTheme);\n\n  return (\n    <button\n      type=\"button\"\n      className={twMerge(active && theme.pages.selector.active, className)}\n      onClick={onClick}\n      {...props}\n    >\n      {children}\n    </button>\n  );\n};\n\nPaginationButton.displayName = \"Pagination.Button\";\n\nexport const PaginationNavigation: FC<PaginationPrevButtonProps> = ({\n  children,\n  className,\n  onClick,\n  theme: customTheme = {},\n  disabled = false,\n  ...props\n}) => {\n  const theme = mergeDeep(getTheme().pagination, customTheme);\n\n  return (\n    <button\n      type=\"button\"\n      className={twMerge(disabled && theme.pages.selector.disabled, className)}\n      disabled={disabled}\n      onClick={onClick}\n      {...props}\n    >\n      {children}\n    </button>\n  );\n};\n\nPaginationNavigation.displayName = \"Pagination.Navigation\";\n"], "names": [], "mappings": ";;;;;AAKY,MAAC,gBAAgB,GAAG,CAAC;AACjC,EAAE,MAAM;AACR,EAAE,QAAQ;AACV,EAAE,SAAS;AACX,EAAE,OAAO;AACT,EAAE,KAAK,EAAE,WAAW,GAAG,EAAE;AACzB,EAAE,GAAG,KAAK;AACV,CAAC,KAAK;AACN,EAAE,MAAM,KAAK,GAAG,SAAS,CAAC,QAAQ,EAAE,CAAC,UAAU,EAAE,WAAW,CAAC,CAAC;AAC9D,EAAE,uBAAuB,GAAG;AAC5B,IAAI,QAAQ;AACZ,IAAI;AACJ,MAAM,IAAI,EAAE,QAAQ;AACpB,MAAM,SAAS,EAAE,OAAO,CAAC,MAAM,IAAI,KAAK,CAAC,KAAK,CAAC,QAAQ,CAAC,MAAM,EAAE,SAAS,CAAC;AAC1E,MAAM,OAAO;AACb,MAAM,GAAG,KAAK;AACd,MAAM,QAAQ;AACd,KAAK;AACL,GAAG,CAAC;AACJ,EAAE;AACF,gBAAgB,CAAC,WAAW,GAAG,mBAAmB,CAAC;AACvC,MAAC,oBAAoB,GAAG,CAAC;AACrC,EAAE,QAAQ;AACV,EAAE,SAAS;AACX,EAAE,OAAO;AACT,EAAE,KAAK,EAAE,WAAW,GAAG,EAAE;AACzB,EAAE,QAAQ,GAAG,KAAK;AAClB,EAAE,GAAG,KAAK;AACV,CAAC,KAAK;AACN,EAAE,MAAM,KAAK,GAAG,SAAS,CAAC,QAAQ,EAAE,CAAC,UAAU,EAAE,WAAW,CAAC,CAAC;AAC9D,EAAE,uBAAuB,GAAG;AAC5B,IAAI,QAAQ;AACZ,IAAI;AACJ,MAAM,IAAI,EAAE,QAAQ;AACpB,MAAM,SAAS,EAAE,OAAO,CAAC,QAAQ,IAAI,KAAK,CAAC,KAAK,CAAC,QAAQ,CAAC,QAAQ,EAAE,SAAS,CAAC;AAC9E,MAAM,QAAQ;AACd,MAAM,OAAO;AACb,MAAM,GAAG,KAAK;AACd,MAAM,QAAQ;AACd,KAAK;AACL,GAAG,CAAC;AACJ,EAAE;AACF,oBAAoB,CAAC,WAAW,GAAG,uBAAuB;;;;"}