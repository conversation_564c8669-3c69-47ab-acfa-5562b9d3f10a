{"version": 3, "file": "Popover.mjs", "sources": ["../../../../src/components/Popover/Popover.tsx"], "sourcesContent": ["\"use client\";\n\nimport type { Placement } from \"@floating-ui/react\";\nimport { FloatingFocusManager, useMergeRefs } from \"@floating-ui/react\";\nimport type { ComponentProps, ComponentPropsWithRef, Dispatch, ReactNode, SetStateAction } from \"react\";\nimport { cloneElement, isValidElement, useMemo, useRef, useState } from \"react\";\nimport { mergeDeep } from \"../../helpers/merge-deep\";\nimport { useBaseFLoating, useFloatingInteractions } from \"../../hooks/use-floating\";\nimport { getTheme } from \"../../theme-store\";\nimport type { DeepPartial } from \"../../types\";\nimport type { FlowbiteFloatingArrowTheme } from \"../Floating\";\nimport { getArrowPlacement } from \"../Floating/helpers\";\n\nexport interface FlowbitePopoverTheme {\n  arrow: Omit<FlowbiteFloatingArrowTheme, \"style\">;\n  base: string;\n  content: string;\n}\n\nexport interface PopoverProps extends Omit<ComponentProps<\"div\">, \"content\" | \"style\"> {\n  arrow?: boolean;\n  content: ReactNode;\n  placement?: \"auto\" | Placement;\n  theme?: DeepPartial<FlowbitePopoverTheme>;\n  trigger?: \"hover\" | \"click\";\n  initialOpen?: boolean;\n  open?: boolean;\n  onOpenChange?: Dispatch<SetStateAction<boolean>>;\n}\n\nexport function Popover({\n  children,\n  content,\n  theme: customTheme = {},\n  arrow = true,\n  trigger = \"click\",\n  initialOpen,\n  open: controlledOpen,\n  onOpenChange: setControlledOpen,\n  placement: theirPlacement = \"bottom\",\n  ...props\n}: PopoverProps) {\n  const [uncontrolledOpen, setUncontrolledOpen] = useState<boolean>(Boolean(initialOpen));\n  const arrowRef = useRef<HTMLDivElement>(null);\n\n  const theme = mergeDeep(getTheme().popover, customTheme);\n\n  const open = controlledOpen ?? uncontrolledOpen;\n  const setOpen = setControlledOpen ?? setUncontrolledOpen;\n\n  const floatingProps = useBaseFLoating({\n    open,\n    placement: theirPlacement,\n    arrowRef,\n    setOpen,\n  });\n\n  const {\n    floatingStyles,\n    context,\n    placement,\n    middlewareData: { arrow: { x: arrowX, y: arrowY } = {} },\n    refs,\n  } = floatingProps;\n\n  const { getFloatingProps, getReferenceProps } = useFloatingInteractions({\n    context,\n    role: \"dialog\",\n    trigger,\n  });\n\n  const childrenRef = (children as ComponentPropsWithRef<\"button\">).ref;\n  const ref = useMergeRefs([context.refs.setReference, childrenRef]);\n\n  if (!isValidElement(children)) {\n    throw Error(\"Invalid target element\");\n  }\n\n  const target = useMemo(() => {\n    return cloneElement(\n      children,\n      getReferenceProps({\n        ref,\n        \"data-testid\": \"flowbite-popover-target\",\n        ...children?.props,\n      }),\n    );\n  }, [children, ref, getReferenceProps]);\n\n  return (\n    <>\n      {target}\n      {open && (\n        <FloatingFocusManager context={context} modal>\n          <div\n            className={theme.base}\n            ref={refs.setFloating}\n            data-testid=\"flowbite-popover\"\n            {...props}\n            style={floatingStyles}\n            {...getFloatingProps()}\n          >\n            <div className=\"relative\">\n              {arrow && (\n                <div\n                  className={theme.arrow.base}\n                  data-testid=\"flowbite-popover-arrow\"\n                  ref={arrowRef}\n                  style={{\n                    top: arrowY ?? \" \",\n                    left: arrowX ?? \" \",\n                    right: \" \",\n                    bottom: \" \",\n                    [getArrowPlacement({ placement })]: theme.arrow.placement,\n                  }}\n                >\n                  &nbsp;\n                </div>\n              )}\n              <div className={theme.content}>{content}</div>\n            </div>\n          </div>\n        </FloatingFocusManager>\n      )}\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;;;;;AASO,SAAS,OAAO,CAAC;AACxB,EAAE,QAAQ;AACV,EAAE,OAAO;AACT,EAAE,KAAK,EAAE,WAAW,GAAG,EAAE;AACzB,EAAE,KAAK,GAAG,IAAI;AACd,EAAE,OAAO,GAAG,OAAO;AACnB,EAAE,WAAW;AACb,EAAE,IAAI,EAAE,cAAc;AACtB,EAAE,YAAY,EAAE,iBAAiB;AACjC,EAAE,SAAS,EAAE,cAAc,GAAG,QAAQ;AACtC,EAAE,GAAG,KAAK;AACV,CAAC,EAAE;AACH,EAAE,MAAM,CAAC,gBAAgB,EAAE,mBAAmB,CAAC,GAAG,QAAQ,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC;AACjF,EAAE,MAAM,QAAQ,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC;AAChC,EAAE,MAAM,KAAK,GAAG,SAAS,CAAC,QAAQ,EAAE,CAAC,OAAO,EAAE,WAAW,CAAC,CAAC;AAC3D,EAAE,MAAM,IAAI,GAAG,cAAc,IAAI,gBAAgB,CAAC;AAClD,EAAE,MAAM,OAAO,GAAG,iBAAiB,IAAI,mBAAmB,CAAC;AAC3D,EAAE,MAAM,aAAa,GAAG,eAAe,CAAC;AACxC,IAAI,IAAI;AACR,IAAI,SAAS,EAAE,cAAc;AAC7B,IAAI,QAAQ;AACZ,IAAI,OAAO;AACX,GAAG,CAAC,CAAC;AACL,EAAE,MAAM;AACR,IAAI,cAAc;AAClB,IAAI,OAAO;AACX,IAAI,SAAS;AACb,IAAI,cAAc,EAAE,EAAE,KAAK,EAAE,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,GAAG,EAAE,EAAE;AAC5D,IAAI,IAAI;AACR,GAAG,GAAG,aAAa,CAAC;AACpB,EAAE,MAAM,EAAE,gBAAgB,EAAE,iBAAiB,EAAE,GAAG,uBAAuB,CAAC;AAC1E,IAAI,OAAO;AACX,IAAI,IAAI,EAAE,QAAQ;AAClB,IAAI,OAAO;AACX,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,WAAW,GAAG,QAAQ,CAAC,GAAG,CAAC;AACnC,EAAE,MAAM,GAAG,GAAG,YAAY,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,YAAY,EAAE,WAAW,CAAC,CAAC,CAAC;AACrE,EAAE,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,EAAE;AACjC,IAAI,MAAM,KAAK,CAAC,wBAAwB,CAAC,CAAC;AAC1C,GAAG;AACH,EAAE,MAAM,MAAM,GAAG,OAAO,CAAC,MAAM;AAC/B,IAAI,OAAO,YAAY;AACvB,MAAM,QAAQ;AACd,MAAM,iBAAiB,CAAC;AACxB,QAAQ,GAAG;AACX,QAAQ,aAAa,EAAE,yBAAyB;AAChD,QAAQ,GAAG,QAAQ,EAAE,KAAK;AAC1B,OAAO,CAAC;AACR,KAAK,CAAC;AACN,GAAG,EAAE,CAAC,QAAQ,EAAE,GAAG,EAAE,iBAAiB,CAAC,CAAC,CAAC;AACzC,EAAE,uBAAuB,IAAI,CAAC,QAAQ,EAAE,EAAE,QAAQ,EAAE;AACpD,IAAI,MAAM;AACV,IAAI,IAAI,oBAAoB,GAAG,CAAC,oBAAoB,EAAE,EAAE,OAAO,EAAE,KAAK,EAAE,IAAI,EAAE,QAAQ,kBAAkB,GAAG;AAC3G,MAAM,KAAK;AACX,MAAM;AACN,QAAQ,SAAS,EAAE,KAAK,CAAC,IAAI;AAC7B,QAAQ,GAAG,EAAE,IAAI,CAAC,WAAW;AAC7B,QAAQ,aAAa,EAAE,kBAAkB;AACzC,QAAQ,GAAG,KAAK;AAChB,QAAQ,KAAK,EAAE,cAAc;AAC7B,QAAQ,GAAG,gBAAgB,EAAE;AAC7B,QAAQ,QAAQ,kBAAkB,IAAI,CAAC,KAAK,EAAE,EAAE,SAAS,EAAE,UAAU,EAAE,QAAQ,EAAE;AACjF,UAAU,KAAK,oBAAoB,GAAG;AACtC,YAAY,KAAK;AACjB,YAAY;AACZ,cAAc,SAAS,EAAE,KAAK,CAAC,KAAK,CAAC,IAAI;AACzC,cAAc,aAAa,EAAE,wBAAwB;AACrD,cAAc,GAAG,EAAE,QAAQ;AAC3B,cAAc,KAAK,EAAE;AACrB,gBAAgB,GAAG,EAAE,MAAM,IAAI,GAAG;AAClC,gBAAgB,IAAI,EAAE,MAAM,IAAI,GAAG;AACnC,gBAAgB,KAAK,EAAE,GAAG;AAC1B,gBAAgB,MAAM,EAAE,GAAG;AAC3B,gBAAgB,CAAC,iBAAiB,CAAC,EAAE,SAAS,EAAE,CAAC,GAAG,KAAK,CAAC,KAAK,CAAC,SAAS;AACzE,eAAe;AACf,cAAc,QAAQ,EAAE,MAAM;AAC9B,aAAa;AACb,WAAW;AACX,0BAA0B,GAAG,CAAC,KAAK,EAAE,EAAE,SAAS,EAAE,KAAK,CAAC,OAAO,EAAE,QAAQ,EAAE,OAAO,EAAE,CAAC;AACrF,SAAS,EAAE,CAAC;AACZ,OAAO;AACP,KAAK,EAAE,CAAC;AACR,GAAG,EAAE,CAAC,CAAC;AACP;;;;"}