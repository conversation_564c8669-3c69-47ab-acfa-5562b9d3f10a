{"version": 3, "file": "theme.mjs", "sources": ["../../../../src/components/Popover/theme.ts"], "sourcesContent": ["import type { FlowbitePopoverTheme } from \"./Popover\";\n\nexport const popoverTheme: FlowbitePopoverTheme = {\n  base: \"absolute z-20 inline-block w-max max-w-[100vw] bg-white outline-none border border-gray-200 rounded-lg shadow-sm dark:border-gray-600 dark:bg-gray-800\",\n  content: \"z-10 overflow-hidden rounded-[7px]\",\n  arrow: {\n    base: \"absolute h-2 w-2 z-0 rotate-45 mix-blend-lighten bg-white border border-gray-200 dark:border-gray-600 dark:bg-gray-800 dark:mix-blend-color\",\n    placement: \"-4px\",\n  },\n};\n"], "names": [], "mappings": "AACY,MAAC,YAAY,GAAG;AAC5B,EAAE,IAAI,EAAE,wJAAwJ;AAChK,EAAE,OAAO,EAAE,oCAAoC;AAC/C,EAAE,KAAK,EAAE;AACT,IAAI,IAAI,EAAE,6IAA6I;AACvJ,IAAI,SAAS,EAAE,MAAM;AACrB,GAAG;AACH;;;;"}