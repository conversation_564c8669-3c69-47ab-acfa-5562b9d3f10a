{"version": 3, "file": "Progress.mjs", "sources": ["../../../../src/components/Progress/Progress.tsx"], "sourcesContent": ["import type { ComponentProps, FC } from \"react\";\nimport { useId } from \"react\";\nimport { twMerge } from \"tailwind-merge\";\nimport { mergeDeep } from \"../../helpers/merge-deep\";\nimport { getTheme } from \"../../theme-store\";\nimport type { DeepPartial, DynamicStringEnumKeysOf } from \"../../types\";\nimport type { FlowbiteColors, FlowbiteSizes } from \"../Flowbite\";\n\nexport interface FlowbiteProgressTheme {\n  base: string;\n  label: string;\n  bar: string;\n  color: ProgressColor;\n  size: ProgressSizes;\n}\n\nexport interface ProgressColor\n  extends Pick<\n    FlowbiteColors,\n    \"dark\" | \"blue\" | \"red\" | \"green\" | \"yellow\" | \"indigo\" | \"purple\" | \"cyan\" | \"gray\" | \"lime\" | \"pink\" | \"teal\"\n  > {\n  [key: string]: string;\n}\n\nexport interface ProgressSizes extends Pick<FlowbiteSizes, \"sm\" | \"md\" | \"lg\" | \"xl\"> {\n  [key: string]: string;\n}\n\nexport interface ProgressProps extends ComponentProps<\"div\"> {\n  labelProgress?: boolean;\n  labelText?: boolean;\n  progress: number;\n  progressLabelPosition?: \"inside\" | \"outside\";\n  size?: DynamicStringEnumKeysOf<ProgressSizes>;\n  textLabel?: string;\n  textLabelPosition?: \"inside\" | \"outside\";\n  theme?: DeepPartial<FlowbiteProgressTheme>;\n}\n\nexport const Progress: FC<ProgressProps> = ({\n  className,\n  color = \"cyan\",\n  labelProgress = false,\n  labelText = false,\n  progress,\n  progressLabelPosition = \"inside\",\n  size = \"md\",\n  textLabel = \"progressbar\",\n  textLabelPosition = \"inside\",\n  theme: customTheme = {},\n  ...props\n}) => {\n  const id = useId();\n  const theme = mergeDeep(getTheme().progress, customTheme);\n\n  return (\n    <>\n      <div id={id} aria-label={textLabel} aria-valuenow={progress} role=\"progressbar\" {...props}>\n        {((textLabel && labelText && textLabelPosition === \"outside\") ||\n          (progress > 0 && labelProgress && progressLabelPosition === \"outside\")) && (\n          <div className={theme.label} data-testid=\"flowbite-progress-outer-label-container\">\n            {textLabel && labelText && textLabelPosition === \"outside\" && (\n              <span data-testid=\"flowbite-progress-outer-text-label\">{textLabel}</span>\n            )}\n            {labelProgress && progressLabelPosition === \"outside\" && (\n              <span data-testid=\"flowbite-progress-outer-progress-label\">{progress}%</span>\n            )}\n          </div>\n        )}\n\n        <div className={twMerge(theme.base, theme.size[size], className)}>\n          <div style={{ width: `${progress}%` }} className={twMerge(theme.bar, theme.color[color], theme.size[size])}>\n            {textLabel && labelText && textLabelPosition === \"inside\" && (\n              <span data-testid=\"flowbite-progress-inner-text-label\">{textLabel}</span>\n            )}\n            {progress > 0 && labelProgress && progressLabelPosition === \"inside\" && (\n              <span data-testid=\"flowbite-progress-inner-progress-label\">{progress}%</span>\n            )}\n          </div>\n        </div>\n      </div>\n    </>\n  );\n};\n\nProgress.displayName = \"Progress\";\n"], "names": [], "mappings": ";;;;;;AAMY,MAAC,QAAQ,GAAG,CAAC;AACzB,EAAE,SAAS;AACX,EAAE,KAAK,GAAG,MAAM;AAChB,EAAE,aAAa,GAAG,KAAK;AACvB,EAAE,SAAS,GAAG,KAAK;AACnB,EAAE,QAAQ;AACV,EAAE,qBAAqB,GAAG,QAAQ;AAClC,EAAE,IAAI,GAAG,IAAI;AACb,EAAE,SAAS,GAAG,aAAa;AAC3B,EAAE,iBAAiB,GAAG,QAAQ;AAC9B,EAAE,KAAK,EAAE,WAAW,GAAG,EAAE;AACzB,EAAE,GAAG,KAAK;AACV,CAAC,KAAK;AACN,EAAE,MAAM,EAAE,GAAG,KAAK,EAAE,CAAC;AACrB,EAAE,MAAM,KAAK,GAAG,SAAS,CAAC,QAAQ,EAAE,CAAC,QAAQ,EAAE,WAAW,CAAC,CAAC;AAC5D,EAAE,uBAAuB,GAAG,CAAC,QAAQ,EAAE,EAAE,QAAQ,kBAAkB,IAAI,CAAC,KAAK,EAAE,EAAE,EAAE,EAAE,YAAY,EAAE,SAAS,EAAE,eAAe,EAAE,QAAQ,EAAE,IAAI,EAAE,aAAa,EAAE,GAAG,KAAK,EAAE,QAAQ,EAAE;AAClL,IAAI,CAAC,SAAS,IAAI,SAAS,IAAI,iBAAiB,KAAK,SAAS,IAAI,QAAQ,GAAG,CAAC,IAAI,aAAa,IAAI,qBAAqB,KAAK,SAAS,qBAAqB,IAAI,CAAC,KAAK,EAAE,EAAE,SAAS,EAAE,KAAK,CAAC,KAAK,EAAE,aAAa,EAAE,yCAAyC,EAAE,QAAQ,EAAE;AACrQ,MAAM,SAAS,IAAI,SAAS,IAAI,iBAAiB,KAAK,SAAS,oBAAoB,GAAG,CAAC,MAAM,EAAE,EAAE,aAAa,EAAE,oCAAoC,EAAE,QAAQ,EAAE,SAAS,EAAE,CAAC;AAC5K,MAAM,aAAa,IAAI,qBAAqB,KAAK,SAAS,oBAAoB,IAAI,CAAC,MAAM,EAAE,EAAE,aAAa,EAAE,wCAAwC,EAAE,QAAQ,EAAE;AAChK,QAAQ,QAAQ;AAChB,QAAQ,GAAG;AACX,OAAO,EAAE,CAAC;AACV,KAAK,EAAE,CAAC;AACR,oBAAoB,GAAG,CAAC,KAAK,EAAE,EAAE,SAAS,EAAE,OAAO,CAAC,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,SAAS,CAAC,EAAE,QAAQ,kBAAkB,IAAI,CAAC,KAAK,EAAE,EAAE,KAAK,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,QAAQ,CAAC,CAAC,CAAC,EAAE,EAAE,SAAS,EAAE,OAAO,CAAC,KAAK,CAAC,GAAG,EAAE,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,EAAE,QAAQ,EAAE;AACvP,MAAM,SAAS,IAAI,SAAS,IAAI,iBAAiB,KAAK,QAAQ,oBAAoB,GAAG,CAAC,MAAM,EAAE,EAAE,aAAa,EAAE,oCAAoC,EAAE,QAAQ,EAAE,SAAS,EAAE,CAAC;AAC3K,MAAM,QAAQ,GAAG,CAAC,IAAI,aAAa,IAAI,qBAAqB,KAAK,QAAQ,oBAAoB,IAAI,CAAC,MAAM,EAAE,EAAE,aAAa,EAAE,wCAAwC,EAAE,QAAQ,EAAE;AAC/K,QAAQ,QAAQ;AAChB,QAAQ,GAAG;AACX,OAAO,EAAE,CAAC;AACV,KAAK,EAAE,CAAC,EAAE,CAAC;AACX,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC;AACV,EAAE;AACF,QAAQ,CAAC,WAAW,GAAG,UAAU;;;;"}