{"version": 3, "file": "Radio.mjs", "sources": ["../../../../src/components/Radio/Radio.tsx"], "sourcesContent": ["import type { ComponentProps } from \"react\";\nimport { forwardRef } from \"react\";\nimport { twMerge } from \"tailwind-merge\";\nimport { mergeDeep } from \"../../helpers/merge-deep\";\nimport { getTheme } from \"../../theme-store\";\nimport type { DeepPartial } from \"../../types\";\n\nexport interface FlowbiteRadioTheme {\n  root: FlowbiteRadioRootTheme;\n}\n\nexport interface FlowbiteRadioRootTheme {\n  base: string;\n}\n\nexport interface RadioProps extends Omit<ComponentProps<\"input\">, \"ref\" | \"type\"> {\n  theme?: DeepPartial<FlowbiteRadioTheme>;\n}\n\nexport const Radio = forwardRef<HTMLInputElement, RadioProps>(\n  ({ className, theme: customTheme = {}, ...props }, ref) => {\n    const theme = mergeDeep(getTheme().radio, customTheme);\n\n    return <input ref={ref} type=\"radio\" className={twMerge(theme.root.base, className)} {...props} />;\n  },\n);\n\nRadio.displayName = \"Radio\";\n"], "names": [], "mappings": ";;;;;;AAMY,MAAC,KAAK,GAAG,UAAU;AAC/B,EAAE,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,WAAW,GAAG,EAAE,EAAE,GAAG,KAAK,EAAE,EAAE,GAAG,KAAK;AAC7D,IAAI,MAAM,KAAK,GAAG,SAAS,CAAC,QAAQ,EAAE,CAAC,KAAK,EAAE,WAAW,CAAC,CAAC;AAC3D,IAAI,uBAAuB,GAAG,CAAC,OAAO,EAAE,EAAE,GAAG,EAAE,IAAI,EAAE,OAAO,EAAE,SAAS,EAAE,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,EAAE,SAAS,CAAC,EAAE,GAAG,KAAK,EAAE,CAAC,CAAC;AAC1H,GAAG;AACH,EAAE;AACF,KAAK,CAAC,WAAW,GAAG,OAAO;;;;"}