{"version": 3, "file": "theme.mjs", "sources": ["../../../../src/components/Radio/theme.ts"], "sourcesContent": ["import { createTheme } from \"../../helpers/create-theme\";\nimport type { FlowbiteRadioTheme } from \"./Radio\";\n\nexport const radioTheme: FlowbiteRadioTheme = createTheme({\n  root: {\n    base: \"h-4 w-4 border border-gray-300 text-cyan-600 focus:ring-2 focus:ring-cyan-500 dark:border-gray-600 dark:bg-gray-700 dark:focus:bg-cyan-600 dark:focus:ring-cyan-600\",\n  },\n});\n"], "names": [], "mappings": ";;AAEY,MAAC,UAAU,GAAG,WAAW,CAAC;AACtC,EAAE,IAAI,EAAE;AACR,IAAI,IAAI,EAAE,qKAAqK;AAC/K,GAAG;AACH,CAAC;;;;"}