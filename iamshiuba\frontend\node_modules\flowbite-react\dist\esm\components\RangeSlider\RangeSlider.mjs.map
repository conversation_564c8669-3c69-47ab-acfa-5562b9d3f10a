{"version": 3, "file": "RangeSlider.mjs", "sources": ["../../../../src/components/RangeSlider/RangeSlider.tsx"], "sourcesContent": ["import type { ComponentProps } from \"react\";\nimport { forwardRef } from \"react\";\nimport { twMerge } from \"tailwind-merge\";\nimport { mergeDeep } from \"../../helpers/merge-deep\";\nimport { getTheme } from \"../../theme-store\";\nimport type { DeepPartial, DynamicStringEnumKeysOf } from \"../../types\";\nimport type { FlowbiteTextInputSizes } from \"../TextInput\";\n\nexport interface FlowbiteRangeSliderTheme {\n  root: FlowbiteRangeSliderRootTheme;\n  field: FlowbiteRangeSliderFieldTheme;\n}\n\nexport interface FlowbiteRangeSliderRootTheme {\n  base: string;\n}\n\nexport interface FlowbiteRangeSliderFieldTheme {\n  base: string;\n  input: {\n    base: string;\n    sizes: FlowbiteTextInputSizes;\n  };\n}\n\nexport interface RangeSliderProps extends Omit<ComponentProps<\"input\">, \"ref\" | \"type\"> {\n  sizing?: DynamicStringEnumKeysOf<FlowbiteTextInputSizes>;\n  theme?: DeepPartial<FlowbiteRangeSliderTheme>;\n}\n\nexport const RangeSlider = forwardRef<HTMLInputElement, RangeSliderProps>(\n  ({ className, sizing = \"md\", theme: customTheme = {}, ...props }, ref) => {\n    const theme = mergeDeep(getTheme().rangeSlider, customTheme);\n\n    return (\n      <>\n        <div data-testid=\"flowbite-range-slider\" className={twMerge(theme.root.base, className)}>\n          <div className={theme.field.base}>\n            <input\n              ref={ref}\n              type=\"range\"\n              className={twMerge(theme.field.input.base, theme.field.input.sizes[sizing])}\n              {...props}\n            />\n          </div>\n        </div>\n      </>\n    );\n  },\n);\n\nRangeSlider.displayName = \"RangeSlider\";\n"], "names": [], "mappings": ";;;;;;AAMY,MAAC,WAAW,GAAG,UAAU;AACrC,EAAE,CAAC,EAAE,SAAS,EAAE,MAAM,GAAG,IAAI,EAAE,KAAK,EAAE,WAAW,GAAG,EAAE,EAAE,GAAG,KAAK,EAAE,EAAE,GAAG,KAAK;AAC5E,IAAI,MAAM,KAAK,GAAG,SAAS,CAAC,QAAQ,EAAE,CAAC,WAAW,EAAE,WAAW,CAAC,CAAC;AACjE,IAAI,uBAAuB,GAAG,CAAC,QAAQ,EAAE,EAAE,QAAQ,kBAAkB,GAAG,CAAC,KAAK,EAAE,EAAE,aAAa,EAAE,uBAAuB,EAAE,SAAS,EAAE,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,EAAE,SAAS,CAAC,EAAE,QAAQ,kBAAkB,GAAG,CAAC,KAAK,EAAE,EAAE,SAAS,EAAE,KAAK,CAAC,KAAK,CAAC,IAAI,EAAE,QAAQ,kBAAkB,GAAG;AAC3Q,MAAM,OAAO;AACb,MAAM;AACN,QAAQ,GAAG;AACX,QAAQ,IAAI,EAAE,OAAO;AACrB,QAAQ,SAAS,EAAE,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;AACnF,QAAQ,GAAG,KAAK;AAChB,OAAO;AACP,KAAK,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;AACf,GAAG;AACH,EAAE;AACF,WAAW,CAAC,WAAW,GAAG,aAAa;;;;"}