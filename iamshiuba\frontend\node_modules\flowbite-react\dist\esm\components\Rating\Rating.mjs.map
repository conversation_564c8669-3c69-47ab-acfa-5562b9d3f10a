{"version": 3, "file": "Rating.mjs", "sources": ["../../../../src/components/Rating/Rating.tsx"], "sourcesContent": ["\"use client\";\n\nimport type { ComponentProps, FC } from \"react\";\nimport { twMerge } from \"tailwind-merge\";\nimport { mergeDeep } from \"../../helpers/merge-deep\";\nimport { getTheme } from \"../../theme-store\";\nimport type { DeepPartial, DynamicStringEnumKeysOf } from \"../../types\";\nimport { RatingAdvanced } from \"./RatingAdvanced\";\nimport { RatingContext } from \"./RatingContext\";\nimport type { FlowbiteRatingStarTheme, FlowbiteStarSizes } from \"./RatingStar\";\nimport { RatingStar } from \"./RatingStar\";\n\nexport interface FlowbiteRatingTheme {\n  root: {\n    base: string;\n  };\n  star: FlowbiteRatingStarTheme;\n}\n\nexport interface RatingProps extends ComponentProps<\"div\"> {\n  size?: DynamicStringEnumKeysOf<FlowbiteStarSizes>;\n  theme?: DeepPartial<FlowbiteRatingTheme>;\n}\n\nconst RatingComponent: FC<RatingProps> = ({ children, className, size = \"sm\", theme: customTheme = {}, ...props }) => {\n  const theme = mergeDeep(getTheme().rating, customTheme);\n\n  return (\n    <RatingContext.Provider value={{ theme, size }}>\n      <div className={twMerge(theme.root.base, className)} {...props}>\n        {children}\n      </div>\n    </RatingContext.Provider>\n  );\n};\n\nRatingComponent.displayName = \"Rating\";\nRatingStar.displayName = \"Rating.Star\";\nRatingAdvanced.displayName = \"Rating.Advanced\";\n\nexport const Rating = Object.assign(RatingComponent, {\n  Star: RatingStar,\n  Advanced: RatingAdvanced,\n});\n"], "names": [], "mappings": ";;;;;;;;AASA,MAAM,eAAe,GAAG,CAAC,EAAE,QAAQ,EAAE,SAAS,EAAE,IAAI,GAAG,IAAI,EAAE,KAAK,EAAE,WAAW,GAAG,EAAE,EAAE,GAAG,KAAK,EAAE,KAAK;AACrG,EAAE,MAAM,KAAK,GAAG,SAAS,CAAC,QAAQ,EAAE,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC;AAC1D,EAAE,uBAAuB,GAAG,CAAC,aAAa,CAAC,QAAQ,EAAE,EAAE,KAAK,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE,EAAE,QAAQ,kBAAkB,GAAG,CAAC,KAAK,EAAE,EAAE,SAAS,EAAE,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,EAAE,SAAS,CAAC,EAAE,GAAG,KAAK,EAAE,QAAQ,EAAE,CAAC,EAAE,CAAC,CAAC;AAC/L,CAAC,CAAC;AACF,eAAe,CAAC,WAAW,GAAG,QAAQ,CAAC;AACvC,UAAU,CAAC,WAAW,GAAG,aAAa,CAAC;AACvC,cAAc,CAAC,WAAW,GAAG,iBAAiB,CAAC;AACnC,MAAC,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,eAAe,EAAE;AACrD,EAAE,IAAI,EAAE,UAAU;AAClB,EAAE,QAAQ,EAAE,cAAc;AAC1B,CAAC;;;;"}