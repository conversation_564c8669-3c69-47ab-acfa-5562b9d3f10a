{"version": 3, "file": "RatingAdvanced.mjs", "sources": ["../../../../src/components/Rating/RatingAdvanced.tsx"], "sourcesContent": ["import type { ComponentProps, FC } from \"react\";\nimport { twMerge } from \"tailwind-merge\";\nimport { mergeDeep } from \"../../helpers/merge-deep\";\nimport { getTheme } from \"../../theme-store\";\nimport type { DeepPartial } from \"../../types\";\n\nexport interface FlowbiteRatingAdvancedTheme {\n  base: string;\n  label: string;\n  progress: {\n    base: string;\n    fill: string;\n    label: string;\n  };\n}\n\nexport interface RatingAdvancedProps extends ComponentProps<\"div\"> {\n  percentFilled?: number;\n  theme?: DeepPartial<FlowbiteRatingAdvancedTheme>;\n}\n\nexport const RatingAdvanced: FC<RatingAdvancedProps> = ({\n  children,\n  className,\n  percentFilled = 0,\n  theme: customTheme = {},\n  ...props\n}) => {\n  const theme = mergeDeep(getTheme().ratingAdvanced, customTheme);\n\n  return (\n    <div className={twMerge(theme.base, className)} {...props}>\n      <span className={theme.label}>{children}</span>\n      <div className={theme.progress.base}>\n        <div\n          className={theme.progress.fill}\n          data-testid=\"flowbite-rating-fill\"\n          style={{ width: `${percentFilled}%` }}\n        />\n      </div>\n      <span className={theme.progress.label}>{`${percentFilled}%`}</span>\n    </div>\n  );\n};\n"], "names": [], "mappings": ";;;;;AAKY,MAAC,cAAc,GAAG,CAAC;AAC/B,EAAE,QAAQ;AACV,EAAE,SAAS;AACX,EAAE,aAAa,GAAG,CAAC;AACnB,EAAE,KAAK,EAAE,WAAW,GAAG,EAAE;AACzB,EAAE,GAAG,KAAK;AACV,CAAC,KAAK;AACN,EAAE,MAAM,KAAK,GAAG,SAAS,CAAC,QAAQ,EAAE,CAAC,cAAc,EAAE,WAAW,CAAC,CAAC;AAClE,EAAE,uBAAuB,IAAI,CAAC,KAAK,EAAE,EAAE,SAAS,EAAE,OAAO,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC,EAAE,GAAG,KAAK,EAAE,QAAQ,EAAE;AACtG,oBAAoB,GAAG,CAAC,MAAM,EAAE,EAAE,SAAS,EAAE,KAAK,CAAC,KAAK,EAAE,QAAQ,EAAE,CAAC;AACrE,oBAAoB,GAAG,CAAC,KAAK,EAAE,EAAE,SAAS,EAAE,KAAK,CAAC,QAAQ,CAAC,IAAI,EAAE,QAAQ,kBAAkB,GAAG;AAC9F,MAAM,KAAK;AACX,MAAM;AACN,QAAQ,SAAS,EAAE,KAAK,CAAC,QAAQ,CAAC,IAAI;AACtC,QAAQ,aAAa,EAAE,sBAAsB;AAC7C,QAAQ,KAAK,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,aAAa,CAAC,CAAC,CAAC,EAAE;AAC7C,OAAO;AACP,KAAK,EAAE,CAAC;AACR,oBAAoB,GAAG,CAAC,MAAM,EAAE,EAAE,SAAS,EAAE,KAAK,CAAC,QAAQ,CAAC,KAAK,EAAE,QAAQ,EAAE,CAAC,EAAE,aAAa,CAAC,CAAC,CAAC,EAAE,CAAC;AACnG,GAAG,EAAE,CAAC,CAAC;AACP;;;;"}