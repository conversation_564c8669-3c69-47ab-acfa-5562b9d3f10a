{"version": 3, "file": "RatingContext.mjs", "sources": ["../../../../src/components/Rating/RatingContext.tsx"], "sourcesContent": ["\"use client\";\n\nimport { createContext, useContext } from \"react\";\nimport { DynamicStringEnumKeysOf } from \"../../types\";\nimport type { FlowbiteRatingTheme } from \"./Rating\";\nimport type { FlowbiteStarSizes } from \"./RatingStar\";\n\nexport type RatingContext = {\n  theme: FlowbiteRatingTheme;\n  size?: DynamicStringEnumKeysOf<FlowbiteStarSizes>;\n};\n\nexport const RatingContext = createContext<RatingContext | undefined>(undefined);\n\nexport function useRatingContext(): RatingContext {\n  const context = useContext(RatingContext);\n\n  if (!context) {\n    throw new Error(\"useRatingContext should be used within the RatingContext provider!\");\n  }\n\n  return context;\n}\n"], "names": [], "mappings": ";;AAGY,MAAC,aAAa,GAAG,aAAa,CAAC,KAAK,CAAC,EAAE;AAC5C,SAAS,gBAAgB,GAAG;AACnC,EAAE,MAAM,OAAO,GAAG,UAAU,CAAC,aAAa,CAAC,CAAC;AAC5C,EAAE,IAAI,CAAC,OAAO,EAAE;AAChB,IAAI,MAAM,IAAI,KAAK,CAAC,oEAAoE,CAAC,CAAC;AAC1F,GAAG;AACH,EAAE,OAAO,OAAO,CAAC;AACjB;;;;"}