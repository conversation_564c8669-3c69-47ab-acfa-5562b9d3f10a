{"version": 3, "file": "RatingStar.mjs", "sources": ["../../../../src/components/Rating/RatingStar.tsx"], "sourcesContent": ["\"use client\";\n\nimport type { ComponentProps, FC } from \"react\";\nimport { HiStar } from \"react-icons/hi\";\nimport { twMerge } from \"tailwind-merge\";\nimport { mergeDeep } from \"../../helpers/merge-deep\";\nimport type { DeepPartial } from \"../../types\";\nimport type { FlowbiteSizes } from \"../Flowbite\";\nimport { useRatingContext } from \"./RatingContext\";\n\nexport interface FlowbiteRatingStarTheme {\n  empty: string;\n  filled: string;\n  sizes: FlowbiteStarSizes;\n}\n\nexport interface FlowbiteStarSizes extends Pick<FlowbiteSizes, \"sm\" | \"md\" | \"lg\"> {\n  [key: string]: string;\n}\n\nexport interface RatingStarProps extends ComponentProps<\"svg\"> {\n  filled?: boolean;\n  starIcon?: FC<ComponentProps<\"svg\">>;\n  theme?: DeepPartial<FlowbiteRatingStarTheme>;\n}\n\nexport const RatingStar: FC<RatingStarProps> = ({\n  className,\n  filled = true,\n  starIcon: Icon = HiStar,\n  theme: customTheme = {},\n  ...props\n}) => {\n  const { theme: rootTheme, size = \"sm\" } = useRatingContext();\n\n  const theme = mergeDeep(rootTheme.star, customTheme);\n\n  return (\n    <Icon\n      data-testid=\"flowbite-rating-star\"\n      className={twMerge(theme.sizes[size], theme[filled ? \"filled\" : \"empty\"], className)}\n      {...props}\n    />\n  );\n};\n"], "names": [], "mappings": ";;;;;;AAOY,MAAC,UAAU,GAAG,CAAC;AAC3B,EAAE,SAAS;AACX,EAAE,MAAM,GAAG,IAAI;AACf,EAAE,QAAQ,EAAE,IAAI,GAAG,MAAM;AACzB,EAAE,KAAK,EAAE,WAAW,GAAG,EAAE;AACzB,EAAE,GAAG,KAAK;AACV,CAAC,KAAK;AACN,EAAE,MAAM,EAAE,KAAK,EAAE,SAAS,EAAE,IAAI,GAAG,IAAI,EAAE,GAAG,gBAAgB,EAAE,CAAC;AAC/D,EAAE,MAAM,KAAK,GAAG,SAAS,CAAC,SAAS,CAAC,IAAI,EAAE,WAAW,CAAC,CAAC;AACvD,EAAE,uBAAuB,GAAG;AAC5B,IAAI,IAAI;AACR,IAAI;AACJ,MAAM,aAAa,EAAE,sBAAsB;AAC3C,MAAM,SAAS,EAAE,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,KAAK,CAAC,MAAM,GAAG,QAAQ,GAAG,OAAO,CAAC,EAAE,SAAS,CAAC;AAC1F,MAAM,GAAG,KAAK;AACd,KAAK;AACL,GAAG,CAAC;AACJ;;;;"}