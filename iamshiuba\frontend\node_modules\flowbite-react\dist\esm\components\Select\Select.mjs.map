{"version": 3, "file": "Select.mjs", "sources": ["../../../../src/components/Select/Select.tsx"], "sourcesContent": ["import type { ComponentProps, FC, ReactNode } from \"react\";\nimport { forwardRef } from \"react\";\nimport { twMerge } from \"tailwind-merge\";\nimport { mergeDeep } from \"../../helpers/merge-deep\";\nimport { getTheme } from \"../../theme-store\";\nimport type { DeepPartial, DynamicStringEnumKeysOf } from \"../../types\";\nimport type { FlowbiteBoolean, FlowbiteColors, FlowbiteSizes } from \"../Flowbite\";\nimport { HelperText } from \"../HelperText\";\n\nexport interface FlowbiteSelectTheme {\n  base: string;\n  addon: string;\n  field: {\n    base: string;\n    icon: {\n      base: string;\n      svg: string;\n    };\n    select: {\n      base: string;\n      withIcon: FlowbiteBoolean;\n      withAddon: FlowbiteBoolean;\n      withShadow: FlowbiteBoolean;\n      sizes: SelectSizes;\n      colors: SelectColors;\n    };\n  };\n}\n\nexport interface SelectColors extends Pick<FlowbiteColors, \"gray\" | \"info\" | \"failure\" | \"warning\" | \"success\"> {\n  [key: string]: string;\n}\n\nexport interface SelectSizes extends Pick<FlowbiteSizes, \"sm\" | \"md\" | \"lg\"> {\n  [key: string]: string;\n}\n\nexport interface SelectProps extends Omit<ComponentProps<\"select\">, \"color\" | \"ref\"> {\n  addon?: ReactNode;\n  color?: DynamicStringEnumKeysOf<SelectColors>;\n  helperText?: ReactNode;\n  icon?: FC<ComponentProps<\"svg\">>;\n  shadow?: boolean;\n  sizing?: DynamicStringEnumKeysOf<SelectSizes>;\n  theme?: DeepPartial<FlowbiteSelectTheme>;\n}\n\nexport const Select = forwardRef<HTMLSelectElement, SelectProps>(\n  (\n    {\n      addon,\n      children,\n      className,\n      color = \"gray\",\n      helperText,\n      icon: Icon,\n      shadow,\n      sizing = \"md\",\n      theme: customTheme = {},\n      ...props\n    },\n    ref,\n  ) => {\n    const theme = mergeDeep(getTheme().select, customTheme);\n\n    return (\n      <div className={twMerge(theme.base, className)}>\n        {addon && <span className={theme.addon}>{addon}</span>}\n        <div className={theme.field.base}>\n          {Icon && (\n            <div className={theme.field.icon.base}>\n              <Icon className={theme.field.icon.svg} />\n            </div>\n          )}\n          <select\n            className={twMerge(\n              theme.field.select.base,\n              theme.field.select.colors[color],\n              theme.field.select.sizes[sizing],\n              theme.field.select.withIcon[Icon ? \"on\" : \"off\"],\n              theme.field.select.withAddon[addon ? \"on\" : \"off\"],\n              theme.field.select.withShadow[shadow ? \"on\" : \"off\"],\n            )}\n            {...props}\n            ref={ref}\n          >\n            {children}\n          </select>\n          {helperText && <HelperText color={color}>{helperText}</HelperText>}\n        </div>\n      </div>\n    );\n  },\n);\n\nSelect.displayName = \"Select\";\n"], "names": [], "mappings": ";;;;;;;AAOY,MAAC,MAAM,GAAG,UAAU;AAChC,EAAE,CAAC;AACH,IAAI,KAAK;AACT,IAAI,QAAQ;AACZ,IAAI,SAAS;AACb,IAAI,KAAK,GAAG,MAAM;AAClB,IAAI,UAAU;AACd,IAAI,IAAI,EAAE,IAAI;AACd,IAAI,MAAM;AACV,IAAI,MAAM,GAAG,IAAI;AACjB,IAAI,KAAK,EAAE,WAAW,GAAG,EAAE;AAC3B,IAAI,GAAG,KAAK;AACZ,GAAG,EAAE,GAAG,KAAK;AACb,IAAI,MAAM,KAAK,GAAG,SAAS,CAAC,QAAQ,EAAE,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC;AAC5D,IAAI,uBAAuB,IAAI,CAAC,KAAK,EAAE,EAAE,SAAS,EAAE,OAAO,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC,EAAE,QAAQ,EAAE;AAC9F,MAAM,KAAK,oBAAoB,GAAG,CAAC,MAAM,EAAE,EAAE,SAAS,EAAE,KAAK,CAAC,KAAK,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC;AACvF,sBAAsB,IAAI,CAAC,KAAK,EAAE,EAAE,SAAS,EAAE,KAAK,CAAC,KAAK,CAAC,IAAI,EAAE,QAAQ,EAAE;AAC3E,QAAQ,IAAI,oBAAoB,GAAG,CAAC,KAAK,EAAE,EAAE,SAAS,EAAE,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,EAAE,QAAQ,kBAAkB,GAAG,CAAC,IAAI,EAAE,EAAE,SAAS,EAAE,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC;AAC1J,wBAAwB,GAAG;AAC3B,UAAU,QAAQ;AAClB,UAAU;AACV,YAAY,SAAS,EAAE,OAAO;AAC9B,cAAc,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI;AACrC,cAAc,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC;AAC9C,cAAc,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC;AAC9C,cAAc,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,GAAG,IAAI,GAAG,KAAK,CAAC;AAC9D,cAAc,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC,SAAS,CAAC,KAAK,GAAG,IAAI,GAAG,KAAK,CAAC;AAChE,cAAc,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC,UAAU,CAAC,MAAM,GAAG,IAAI,GAAG,KAAK,CAAC;AAClE,aAAa;AACb,YAAY,GAAG,KAAK;AACpB,YAAY,GAAG;AACf,YAAY,QAAQ;AACpB,WAAW;AACX,SAAS;AACT,QAAQ,UAAU,oBAAoB,GAAG,CAAC,UAAU,EAAE,EAAE,KAAK,EAAE,QAAQ,EAAE,UAAU,EAAE,CAAC;AACtF,OAAO,EAAE,CAAC;AACV,KAAK,EAAE,CAAC,CAAC;AACT,GAAG;AACH,EAAE;AACF,MAAM,CAAC,WAAW,GAAG,QAAQ;;;;"}