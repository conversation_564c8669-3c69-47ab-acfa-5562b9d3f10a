{"version": 3, "file": "Sidebar.mjs", "sources": ["../../../../src/components/Sidebar/Sidebar.tsx"], "sourcesContent": ["\"use client\";\n\nimport type { ComponentProps, ElementType, FC } from \"react\";\nimport { twMerge } from \"tailwind-merge\";\nimport { mergeDeep } from \"../../helpers/merge-deep\";\nimport { getTheme } from \"../../theme-store\";\nimport type { DeepPartial } from \"../../types\";\nimport type { FlowbiteBoolean } from \"../Flowbite\";\nimport { SidebarCollapse, type FlowbiteSidebarCollapseTheme } from \"./SidebarCollapse\";\nimport { SidebarContext } from \"./SidebarContext\";\nimport { SidebarCTA, type FlowbiteSidebarCTATheme } from \"./SidebarCTA\";\nimport { SidebarItem, type FlowbiteSidebarItemTheme } from \"./SidebarItem\";\nimport { SidebarItemGroup, type FlowbiteSidebarItemGroupTheme } from \"./SidebarItemGroup\";\nimport { SidebarItems, type FlowbiteSidebarItemsTheme } from \"./SidebarItems\";\nimport { SidebarLogo, type FlowbiteSidebarLogoTheme } from \"./SidebarLogo\";\n\nexport interface FlowbiteSidebarTheme {\n  root: {\n    base: string;\n    collapsed: FlowbiteBoolean;\n    inner: string;\n  };\n  collapse: FlowbiteSidebarCollapseTheme;\n  cta: FlowbiteSidebarCTATheme;\n  item: FlowbiteSidebarItemTheme;\n  items: FlowbiteSidebarItemsTheme;\n  itemGroup: FlowbiteSidebarItemGroupTheme;\n  logo: FlowbiteSidebarLogoTheme;\n}\n\nexport interface SidebarProps extends ComponentProps<\"div\"> {\n  as?: ElementType;\n  collapseBehavior?: \"collapse\" | \"hide\";\n  collapsed?: boolean;\n  theme?: DeepPartial<FlowbiteSidebarTheme>;\n}\n\nconst SidebarComponent: FC<SidebarProps> = ({\n  children,\n  as: Component = \"nav\",\n  collapseBehavior = \"collapse\",\n  collapsed: isCollapsed = false,\n  theme: customTheme = {},\n  className,\n  ...props\n}) => {\n  const theme = mergeDeep(getTheme().sidebar, customTheme);\n\n  return (\n    <SidebarContext.Provider value={{ theme, isCollapsed }}>\n      <Component\n        aria-label=\"Sidebar\"\n        hidden={isCollapsed && collapseBehavior === \"hide\"}\n        className={twMerge(theme.root.base, theme.root.collapsed[isCollapsed ? \"on\" : \"off\"], className)}\n        {...props}\n      >\n        <div className={theme.root.inner}>{children}</div>\n      </Component>\n    </SidebarContext.Provider>\n  );\n};\n\nSidebarComponent.displayName = \"Sidebar\";\n\nexport const Sidebar = Object.assign(SidebarComponent, {\n  Collapse: SidebarCollapse,\n  CTA: SidebarCTA,\n  Item: SidebarItem,\n  Items: SidebarItems,\n  ItemGroup: SidebarItemGroup,\n  Logo: SidebarLogo,\n});\n"], "names": [], "mappings": ";;;;;;;;;;;;AAaA,MAAM,gBAAgB,GAAG,CAAC;AAC1B,EAAE,QAAQ;AACV,EAAE,EAAE,EAAE,SAAS,GAAG,KAAK;AACvB,EAAE,gBAAgB,GAAG,UAAU;AAC/B,EAAE,SAAS,EAAE,WAAW,GAAG,KAAK;AAChC,EAAE,KAAK,EAAE,WAAW,GAAG,EAAE;AACzB,EAAE,SAAS;AACX,EAAE,GAAG,KAAK;AACV,CAAC,KAAK;AACN,EAAE,MAAM,KAAK,GAAG,SAAS,CAAC,QAAQ,EAAE,CAAC,OAAO,EAAE,WAAW,CAAC,CAAC;AAC3D,EAAE,uBAAuB,GAAG,CAAC,cAAc,CAAC,QAAQ,EAAE,EAAE,KAAK,EAAE,EAAE,KAAK,EAAE,WAAW,EAAE,EAAE,QAAQ,kBAAkB,GAAG;AACpH,IAAI,SAAS;AACb,IAAI;AACJ,MAAM,YAAY,EAAE,SAAS;AAC7B,MAAM,MAAM,EAAE,WAAW,IAAI,gBAAgB,KAAK,MAAM;AACxD,MAAM,SAAS,EAAE,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,WAAW,GAAG,IAAI,GAAG,KAAK,CAAC,EAAE,SAAS,CAAC;AACtG,MAAM,GAAG,KAAK;AACd,MAAM,QAAQ,kBAAkB,GAAG,CAAC,KAAK,EAAE,EAAE,SAAS,EAAE,KAAK,CAAC,IAAI,CAAC,KAAK,EAAE,QAAQ,EAAE,CAAC;AACrF,KAAK;AACL,GAAG,EAAE,CAAC,CAAC;AACP,CAAC,CAAC;AACF,gBAAgB,CAAC,WAAW,GAAG,SAAS,CAAC;AAC7B,MAAC,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC,gBAAgB,EAAE;AACvD,EAAE,QAAQ,EAAE,eAAe;AAC3B,EAAE,GAAG,EAAE,UAAU;AACjB,EAAE,IAAI,EAAE,WAAW;AACnB,EAAE,KAAK,EAAE,YAAY;AACrB,EAAE,SAAS,EAAE,gBAAgB;AAC7B,EAAE,IAAI,EAAE,WAAW;AACnB,CAAC;;;;"}