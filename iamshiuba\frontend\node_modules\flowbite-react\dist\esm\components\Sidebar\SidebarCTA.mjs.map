{"version": 3, "file": "SidebarCTA.mjs", "sources": ["../../../../src/components/Sidebar/SidebarCTA.tsx"], "sourcesContent": ["\"use client\";\n\nimport type { ComponentProps, FC } from \"react\";\nimport { twMerge } from \"tailwind-merge\";\nimport { mergeDeep } from \"../../helpers/merge-deep\";\nimport type { DeepPartial, DynamicStringEnumKeysOf } from \"../../types\";\nimport type { FlowbiteColors } from \"../Flowbite\";\nimport { useSidebarContext } from \"./SidebarContext\";\n\nexport interface FlowbiteSidebarCTATheme {\n  base: string;\n  color: FlowbiteSidebarCTAColors;\n}\n\nexport interface SidebarCTAProps extends Omit<ComponentProps<\"div\">, \"color\"> {\n  color?: DynamicStringEnumKeysOf<FlowbiteSidebarCTAColors>;\n  theme?: DeepPartial<FlowbiteSidebarCTATheme>;\n}\n\nexport interface FlowbiteSidebarCTAColors\n  extends Pick<\n    FlowbiteColors,\n    \"blue\" | \"dark\" | \"failure\" | \"gray\" | \"green\" | \"light\" | \"purple\" | \"red\" | \"success\" | \"warning\" | \"yellow\"\n  > {\n  [key: string]: string;\n}\n\nexport const SidebarCTA: FC<SidebarCTAProps> = ({\n  children,\n  color = \"info\",\n  className,\n  theme: customTheme = {},\n  ...props\n}) => {\n  const { theme: rootTheme, isCollapsed } = useSidebarContext();\n\n  const theme = mergeDeep(rootTheme.cta, customTheme);\n\n  return (\n    <div\n      data-testid=\"sidebar-cta\"\n      hidden={isCollapsed}\n      className={twMerge(theme.base, theme.color[color], className)}\n      {...props}\n    >\n      {children}\n    </div>\n  );\n};\n\nSidebarCTA.displayName = \"Sidebar.CTA\";\n"], "names": [], "mappings": ";;;;;AAMY,MAAC,UAAU,GAAG,CAAC;AAC3B,EAAE,QAAQ;AACV,EAAE,KAAK,GAAG,MAAM;AAChB,EAAE,SAAS;AACX,EAAE,KAAK,EAAE,WAAW,GAAG,EAAE;AACzB,EAAE,GAAG,KAAK;AACV,CAAC,KAAK;AACN,EAAE,MAAM,EAAE,KAAK,EAAE,SAAS,EAAE,WAAW,EAAE,GAAG,iBAAiB,EAAE,CAAC;AAChE,EAAE,MAAM,KAAK,GAAG,SAAS,CAAC,SAAS,CAAC,GAAG,EAAE,WAAW,CAAC,CAAC;AACtD,EAAE,uBAAuB,GAAG;AAC5B,IAAI,KAAK;AACT,IAAI;AACJ,MAAM,aAAa,EAAE,aAAa;AAClC,MAAM,MAAM,EAAE,WAAW;AACzB,MAAM,SAAS,EAAE,OAAO,CAAC,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE,SAAS,CAAC;AACnE,MAAM,GAAG,KAAK;AACd,MAAM,QAAQ;AACd,KAAK;AACL,GAAG,CAAC;AACJ,EAAE;AACF,UAAU,CAAC,WAAW,GAAG,aAAa;;;;"}