{"version": 3, "file": "SidebarCollapse.mjs", "sources": ["../../../../src/components/Sidebar/SidebarCollapse.tsx"], "sourcesContent": ["\"use client\";\n\nimport type { ComponentProps, FC, PropsWithChildren, ReactElement } from \"react\";\nimport { useEffect, useId, useState } from \"react\";\nimport { HiChevronDown } from \"react-icons/hi\";\nimport { twMerge } from \"tailwind-merge\";\nimport { mergeDeep } from \"../../helpers/merge-deep\";\nimport type { DeepPartial } from \"../../types\";\nimport type { FlowbiteBoolean } from \"../Flowbite\";\nimport { Tooltip } from \"../Tooltip\";\nimport { useSidebarContext } from \"./SidebarContext\";\nimport type { SidebarItemProps } from \"./SidebarItem\";\nimport { SidebarItemContext } from \"./SidebarItemContext\";\n\nexport interface FlowbiteSidebarCollapseTheme {\n  button: string;\n  icon: {\n    base: string;\n    open: FlowbiteBoolean;\n  };\n  label: {\n    base: string;\n    icon: {\n      base: string;\n      open: FlowbiteBoolean;\n    };\n  };\n  list: string;\n}\n\nexport interface SidebarCollapseProps\n  extends Pick<SidebarItemProps, \"active\" | \"as\" | \"href\" | \"icon\" | \"label\" | \"labelColor\">,\n    ComponentProps<\"button\"> {\n  onClick?: ComponentProps<\"button\">[\"onClick\"];\n  open?: boolean;\n  chevronIcon?: FC<ComponentProps<\"svg\">>;\n  renderChevronIcon?: (theme: FlowbiteSidebarCollapseTheme, open: boolean) => ReactElement;\n  theme?: DeepPartial<FlowbiteSidebarCollapseTheme>;\n}\n\nexport const SidebarCollapse: FC<SidebarCollapseProps> = ({\n  children,\n  className,\n  icon: Icon,\n  label,\n  chevronIcon: ChevronIcon = HiChevronDown,\n  renderChevronIcon,\n  open = false,\n  theme: customTheme = {},\n  ...props\n}) => {\n  const id = useId();\n  const [isOpen, setOpen] = useState(open);\n  const { theme: rootTheme, isCollapsed } = useSidebarContext();\n\n  const theme = mergeDeep(rootTheme.collapse, customTheme);\n\n  useEffect(() => setOpen(open), [open]);\n\n  const Wrapper: FC<PropsWithChildren> = ({ children }) => (\n    <li>\n      {isCollapsed && !isOpen ? (\n        <Tooltip content={label} placement=\"right\">\n          {children}\n        </Tooltip>\n      ) : (\n        children\n      )}\n    </li>\n  );\n\n  return (\n    <Wrapper>\n      <button\n        id={`flowbite-sidebar-collapse-${id}`}\n        onClick={() => setOpen(!isOpen)}\n        title={label}\n        type=\"button\"\n        className={twMerge(theme.button, className)}\n        {...props}\n      >\n        {Icon && (\n          <Icon\n            aria-hidden\n            data-testid=\"flowbite-sidebar-collapse-icon\"\n            className={twMerge(theme.icon.base, theme.icon.open[isOpen ? \"on\" : \"off\"])}\n          />\n        )}\n        {isCollapsed ? (\n          <span className=\"sr-only\">{label}</span>\n        ) : (\n          <>\n            <span data-testid=\"flowbite-sidebar-collapse-label\" className={theme.label.base}>\n              {label}\n            </span>\n            {renderChevronIcon ? (\n              renderChevronIcon(theme, isOpen)\n            ) : (\n              <ChevronIcon\n                aria-hidden\n                className={twMerge(theme.label.icon.base, theme.label.icon.open[isOpen ? \"on\" : \"off\"])}\n              />\n            )}\n          </>\n        )}\n      </button>\n      <ul aria-labelledby={`flowbite-sidebar-collapse-${id}`} hidden={!isOpen} className={theme.list}>\n        <SidebarItemContext.Provider value={{ isInsideCollapse: true }}>{children}</SidebarItemContext.Provider>\n      </ul>\n    </Wrapper>\n  );\n};\n\nSidebarCollapse.displayName = \"Sidebar.Collapse\";\n"], "names": [], "mappings": ";;;;;;;;;AAUY,MAAC,eAAe,GAAG,CAAC;AAChC,EAAE,QAAQ;AACV,EAAE,SAAS;AACX,EAAE,IAAI,EAAE,IAAI;AACZ,EAAE,KAAK;AACP,EAAE,WAAW,EAAE,WAAW,GAAG,aAAa;AAC1C,EAAE,iBAAiB;AACnB,EAAE,IAAI,GAAG,KAAK;AACd,EAAE,KAAK,EAAE,WAAW,GAAG,EAAE;AACzB,EAAE,GAAG,KAAK;AACV,CAAC,KAAK;AACN,EAAE,MAAM,EAAE,GAAG,KAAK,EAAE,CAAC;AACrB,EAAE,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,GAAG,QAAQ,CAAC,IAAI,CAAC,CAAC;AAC3C,EAAE,MAAM,EAAE,KAAK,EAAE,SAAS,EAAE,WAAW,EAAE,GAAG,iBAAiB,EAAE,CAAC;AAChE,EAAE,MAAM,KAAK,GAAG,SAAS,CAAC,SAAS,CAAC,QAAQ,EAAE,WAAW,CAAC,CAAC;AAC3D,EAAE,SAAS,CAAC,MAAM,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC;AACzC,EAAE,MAAM,OAAO,GAAG,CAAC,EAAE,QAAQ,EAAE,SAAS,EAAE,qBAAqB,GAAG,CAAC,IAAI,EAAE,EAAE,QAAQ,EAAE,WAAW,IAAI,CAAC,MAAM,mBAAmB,GAAG,CAAC,OAAO,EAAE,EAAE,OAAO,EAAE,KAAK,EAAE,SAAS,EAAE,OAAO,EAAE,QAAQ,EAAE,SAAS,EAAE,CAAC,GAAG,SAAS,EAAE,CAAC,CAAC;AACvN,EAAE,uBAAuB,IAAI,CAAC,OAAO,EAAE,EAAE,QAAQ,EAAE;AACnD,oBAAoB,IAAI;AACxB,MAAM,QAAQ;AACd,MAAM;AACN,QAAQ,EAAE,EAAE,CAAC,0BAA0B,EAAE,EAAE,CAAC,CAAC;AAC7C,QAAQ,OAAO,EAAE,MAAM,OAAO,CAAC,CAAC,MAAM,CAAC;AACvC,QAAQ,KAAK,EAAE,KAAK;AACpB,QAAQ,IAAI,EAAE,QAAQ;AACtB,QAAQ,SAAS,EAAE,OAAO,CAAC,KAAK,CAAC,MAAM,EAAE,SAAS,CAAC;AACnD,QAAQ,GAAG,KAAK;AAChB,QAAQ,QAAQ,EAAE;AAClB,UAAU,IAAI,oBAAoB,GAAG;AACrC,YAAY,IAAI;AAChB,YAAY;AACZ,cAAc,aAAa,EAAE,IAAI;AACjC,cAAc,aAAa,EAAE,gCAAgC;AAC7D,cAAc,SAAS,EAAE,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,IAAI,GAAG,KAAK,CAAC,CAAC;AACzF,aAAa;AACb,WAAW;AACX,UAAU,WAAW,mBAAmB,GAAG,CAAC,MAAM,EAAE,EAAE,SAAS,EAAE,SAAS,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC,mBAAmB,IAAI,CAAC,QAAQ,EAAE,EAAE,QAAQ,EAAE;AAC5I,4BAA4B,GAAG,CAAC,MAAM,EAAE,EAAE,aAAa,EAAE,iCAAiC,EAAE,SAAS,EAAE,KAAK,CAAC,KAAK,CAAC,IAAI,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC;AAC3I,YAAY,iBAAiB,GAAG,iBAAiB,CAAC,KAAK,EAAE,MAAM,CAAC,mBAAmB,GAAG;AACtF,cAAc,WAAW;AACzB,cAAc;AACd,gBAAgB,aAAa,EAAE,IAAI;AACnC,gBAAgB,SAAS,EAAE,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,IAAI,GAAG,KAAK,CAAC,CAAC;AACvG,eAAe;AACf,aAAa;AACb,WAAW,EAAE,CAAC;AACd,SAAS;AACT,OAAO;AACP,KAAK;AACL,oBAAoB,GAAG,CAAC,IAAI,EAAE,EAAE,iBAAiB,EAAE,CAAC,0BAA0B,EAAE,EAAE,CAAC,CAAC,EAAE,MAAM,EAAE,CAAC,MAAM,EAAE,SAAS,EAAE,KAAK,CAAC,IAAI,EAAE,QAAQ,kBAAkB,GAAG,CAAC,kBAAkB,CAAC,QAAQ,EAAE,EAAE,KAAK,EAAE,EAAE,gBAAgB,EAAE,IAAI,EAAE,EAAE,QAAQ,EAAE,CAAC,EAAE,CAAC;AAC5O,GAAG,EAAE,CAAC,CAAC;AACP,EAAE;AACF,eAAe,CAAC,WAAW,GAAG,kBAAkB;;;;"}