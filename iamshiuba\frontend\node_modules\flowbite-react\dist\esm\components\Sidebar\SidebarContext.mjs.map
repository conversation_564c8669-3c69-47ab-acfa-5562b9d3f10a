{"version": 3, "file": "SidebarContext.mjs", "sources": ["../../../../src/components/Sidebar/SidebarContext.tsx"], "sourcesContent": ["\"use client\";\n\nimport { createContext, useContext } from \"react\";\nimport type { FlowbiteSidebarTheme } from \"./Sidebar\";\n\nexport type SidebarContext = {\n  theme: FlowbiteSidebarTheme;\n  isCollapsed: boolean;\n};\n\nexport const SidebarContext = createContext<SidebarContext | undefined>(undefined);\n\nexport function useSidebarContext(): SidebarContext {\n  const context = useContext(SidebarContext);\n\n  if (!context) {\n    throw new Error(\"useSidebarContext should be used within the SidebarContext provider!\");\n  }\n\n  return context;\n}\n"], "names": [], "mappings": ";;AAGY,MAAC,cAAc,GAAG,aAAa,CAAC,KAAK,CAAC,EAAE;AAC7C,SAAS,iBAAiB,GAAG;AACpC,EAAE,MAAM,OAAO,GAAG,UAAU,CAAC,cAAc,CAAC,CAAC;AAC7C,EAAE,IAAI,CAAC,OAAO,EAAE;AAChB,IAAI,MAAM,IAAI,KAAK,CAAC,sEAAsE,CAAC,CAAC;AAC5F,GAAG;AACH,EAAE,OAAO,OAAO,CAAC;AACjB;;;;"}