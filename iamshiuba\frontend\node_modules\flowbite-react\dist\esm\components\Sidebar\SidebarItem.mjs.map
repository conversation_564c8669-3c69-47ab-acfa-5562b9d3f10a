{"version": 3, "file": "SidebarItem.mjs", "sources": ["../../../../src/components/Sidebar/SidebarItem.tsx"], "sourcesContent": ["\"use client\";\n\nimport type { ComponentProps, ElementType, FC, PropsWithChildren, ReactNode } from \"react\";\nimport { forwardRef, useId } from \"react\";\nimport { twMerge } from \"tailwind-merge\";\nimport { mergeDeep } from \"../../helpers/merge-deep\";\nimport type { DeepPartial, DynamicStringEnumKeysOf } from \"../../types\";\nimport { Badge } from \"../Badge\";\nimport type { FlowbiteColors } from \"../Flowbite\";\nimport { Tooltip } from \"../Tooltip\";\nimport { useSidebarContext } from \"./SidebarContext\";\nimport { useSidebarItemContext } from \"./SidebarItemContext\";\n\nexport interface FlowbiteSidebarItemTheme {\n  active: string;\n  base: string;\n  collapsed: {\n    insideCollapse: string;\n    noIcon: string;\n  };\n  content: {\n    base: string;\n  };\n  icon: {\n    base: string;\n    active: string;\n  };\n  label: string;\n  listItem: string;\n}\n\nexport interface SidebarItemProps extends Omit<ComponentProps<\"div\">, \"ref\">, Record<string, unknown> {\n  active?: boolean;\n  as?: ElementType;\n  href?: string;\n  icon?: FC<ComponentProps<\"svg\">>;\n  label?: string;\n  labelColor?: DynamicStringEnumKeysOf<SidebarItemLabelColors>;\n  theme?: DeepPartial<FlowbiteSidebarItemTheme>;\n}\n\nexport interface SidebarItemLabelColors extends Pick<FlowbiteColors, \"gray\"> {\n  [key: string]: string;\n}\n\nconst ListItem: FC<\n  PropsWithChildren<{\n    id: string;\n    theme: FlowbiteSidebarItemTheme;\n    isCollapsed: boolean;\n    tooltipChildren: ReactNode | undefined;\n    className?: string;\n  }>\n> = ({ id, theme, isCollapsed, tooltipChildren, children: wrapperChildren, ...props }) => (\n  <li {...props}>\n    {isCollapsed ? (\n      <Tooltip\n        content={\n          <Children id={id} theme={theme}>\n            {tooltipChildren}\n          </Children>\n        }\n        placement=\"right\"\n      >\n        {wrapperChildren}\n      </Tooltip>\n    ) : (\n      wrapperChildren\n    )}\n  </li>\n);\n\nconst Children: FC<PropsWithChildren<{ id: string; theme: FlowbiteSidebarItemTheme }>> = ({ id, theme, children }) => {\n  return (\n    <span\n      data-testid=\"flowbite-sidebar-item-content\"\n      id={`flowbite-sidebar-item-${id}`}\n      className={twMerge(theme.content.base)}\n    >\n      {children}\n    </span>\n  );\n};\n\nexport const SidebarItem = forwardRef<Element, SidebarItemProps>(\n  (\n    {\n      active: isActive,\n      as: Component = \"a\",\n      children,\n      className,\n      icon: Icon,\n      label,\n      labelColor = \"info\",\n      theme: customTheme = {},\n      ...props\n    },\n    ref,\n  ) => {\n    const id = useId();\n    const { theme: rootTheme, isCollapsed } = useSidebarContext();\n    const { isInsideCollapse } = useSidebarItemContext();\n\n    const theme = mergeDeep(rootTheme.item, customTheme);\n\n    return (\n      <ListItem theme={theme} className={theme.listItem} id={id} isCollapsed={isCollapsed} tooltipChildren={children}>\n        <Component\n          aria-labelledby={`flowbite-sidebar-item-${id}`}\n          ref={ref}\n          className={twMerge(\n            theme.base,\n            isActive && theme.active,\n            !isCollapsed && isInsideCollapse && theme.collapsed?.insideCollapse,\n            className,\n          )}\n          {...props}\n        >\n          {Icon && (\n            <Icon\n              aria-hidden\n              data-testid=\"flowbite-sidebar-item-icon\"\n              className={twMerge(theme.icon?.base, isActive && theme.icon?.active)}\n            />\n          )}\n          {isCollapsed && !Icon && (\n            <span className={theme.collapsed?.noIcon}>{(children as string).charAt(0).toLocaleUpperCase() ?? \"?\"}</span>\n          )}\n          {!isCollapsed && (\n            <Children id={id} theme={theme}>\n              {children}\n            </Children>\n          )}\n          {!isCollapsed && label && (\n            <Badge color={labelColor} data-testid=\"flowbite-sidebar-label\" hidden={isCollapsed} className={theme.label}>\n              {label}\n            </Badge>\n          )}\n        </Component>\n      </ListItem>\n    );\n  },\n);\n\nSidebarItem.displayName = \"Sidebar.Item\";\n"], "names": [], "mappings": ";;;;;;;;;AAUA,MAAM,QAAQ,GAAG,CAAC,EAAE,EAAE,EAAE,KAAK,EAAE,WAAW,EAAE,eAAe,EAAE,QAAQ,EAAE,eAAe,EAAE,GAAG,KAAK,EAAE,qBAAqB,GAAG,CAAC,IAAI,EAAE,EAAE,GAAG,KAAK,EAAE,QAAQ,EAAE,WAAW,mBAAmB,GAAG;AACxL,EAAE,OAAO;AACT,EAAE;AACF,IAAI,OAAO,kBAAkB,GAAG,CAAC,QAAQ,EAAE,EAAE,EAAE,EAAE,KAAK,EAAE,QAAQ,EAAE,eAAe,EAAE,CAAC;AACpF,IAAI,SAAS,EAAE,OAAO;AACtB,IAAI,QAAQ,EAAE,eAAe;AAC7B,GAAG;AACH,CAAC,GAAG,eAAe,EAAE,CAAC,CAAC;AACvB,MAAM,QAAQ,GAAG,CAAC,EAAE,EAAE,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAK;AAC9C,EAAE,uBAAuB,GAAG;AAC5B,IAAI,MAAM;AACV,IAAI;AACJ,MAAM,aAAa,EAAE,+BAA+B;AACpD,MAAM,EAAE,EAAE,CAAC,sBAAsB,EAAE,EAAE,CAAC,CAAC;AACvC,MAAM,SAAS,EAAE,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC;AAC5C,MAAM,QAAQ;AACd,KAAK;AACL,GAAG,CAAC;AACJ,CAAC,CAAC;AACU,MAAC,WAAW,GAAG,UAAU;AACrC,EAAE,CAAC;AACH,IAAI,MAAM,EAAE,QAAQ;AACpB,IAAI,EAAE,EAAE,SAAS,GAAG,GAAG;AACvB,IAAI,QAAQ;AACZ,IAAI,SAAS;AACb,IAAI,IAAI,EAAE,IAAI;AACd,IAAI,KAAK;AACT,IAAI,UAAU,GAAG,MAAM;AACvB,IAAI,KAAK,EAAE,WAAW,GAAG,EAAE;AAC3B,IAAI,GAAG,KAAK;AACZ,GAAG,EAAE,GAAG,KAAK;AACb,IAAI,MAAM,EAAE,GAAG,KAAK,EAAE,CAAC;AACvB,IAAI,MAAM,EAAE,KAAK,EAAE,SAAS,EAAE,WAAW,EAAE,GAAG,iBAAiB,EAAE,CAAC;AAClE,IAAI,MAAM,EAAE,gBAAgB,EAAE,GAAG,qBAAqB,EAAE,CAAC;AACzD,IAAI,MAAM,KAAK,GAAG,SAAS,CAAC,SAAS,CAAC,IAAI,EAAE,WAAW,CAAC,CAAC;AACzD,IAAI,uBAAuB,GAAG,CAAC,QAAQ,EAAE,EAAE,KAAK,EAAE,SAAS,EAAE,KAAK,CAAC,QAAQ,EAAE,EAAE,EAAE,WAAW,EAAE,eAAe,EAAE,QAAQ,EAAE,QAAQ,kBAAkB,IAAI;AACvJ,MAAM,SAAS;AACf,MAAM;AACN,QAAQ,iBAAiB,EAAE,CAAC,sBAAsB,EAAE,EAAE,CAAC,CAAC;AACxD,QAAQ,GAAG;AACX,QAAQ,SAAS,EAAE,OAAO;AAC1B,UAAU,KAAK,CAAC,IAAI;AACpB,UAAU,QAAQ,IAAI,KAAK,CAAC,MAAM;AAClC,UAAU,CAAC,WAAW,IAAI,gBAAgB,IAAI,KAAK,CAAC,SAAS,EAAE,cAAc;AAC7E,UAAU,SAAS;AACnB,SAAS;AACT,QAAQ,GAAG,KAAK;AAChB,QAAQ,QAAQ,EAAE;AAClB,UAAU,IAAI,oBAAoB,GAAG;AACrC,YAAY,IAAI;AAChB,YAAY;AACZ,cAAc,aAAa,EAAE,IAAI;AACjC,cAAc,aAAa,EAAE,4BAA4B;AACzD,cAAc,SAAS,EAAE,OAAO,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,EAAE,QAAQ,IAAI,KAAK,CAAC,IAAI,EAAE,MAAM,CAAC;AAClF,aAAa;AACb,WAAW;AACX,UAAU,WAAW,IAAI,CAAC,IAAI,oBAAoB,GAAG,CAAC,MAAM,EAAE,EAAE,SAAS,EAAE,KAAK,CAAC,SAAS,EAAE,MAAM,EAAE,QAAQ,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,iBAAiB,EAAE,IAAI,GAAG,EAAE,CAAC;AAC9J,UAAU,CAAC,WAAW,oBAAoB,GAAG,CAAC,QAAQ,EAAE,EAAE,EAAE,EAAE,KAAK,EAAE,QAAQ,EAAE,CAAC;AAChF,UAAU,CAAC,WAAW,IAAI,KAAK,oBAAoB,GAAG,CAAC,KAAK,EAAE,EAAE,KAAK,EAAE,UAAU,EAAE,aAAa,EAAE,wBAAwB,EAAE,MAAM,EAAE,WAAW,EAAE,SAAS,EAAE,KAAK,CAAC,KAAK,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC;AAC3L,SAAS;AACT,OAAO;AACP,KAAK,EAAE,CAAC,CAAC;AACT,GAAG;AACH,EAAE;AACF,WAAW,CAAC,WAAW,GAAG,cAAc;;;;"}