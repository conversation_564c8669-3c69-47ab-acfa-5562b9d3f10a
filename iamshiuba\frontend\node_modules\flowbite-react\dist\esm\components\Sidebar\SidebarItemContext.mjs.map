{"version": 3, "file": "SidebarItemContext.mjs", "sources": ["../../../../src/components/Sidebar/SidebarItemContext.tsx"], "sourcesContent": ["\"use client\";\n\nimport { createContext, useContext } from \"react\";\n\nexport type SidebarItemContext = {\n  isInsideCollapse: boolean;\n};\n\nexport const SidebarItemContext = createContext<SidebarItemContext | undefined>(undefined);\n\nexport function useSidebarItemContext(): SidebarItemContext {\n  const context = useContext(SidebarItemContext);\n\n  if (!context) {\n    throw new Error(\"useSidebarItemContext should be used within the SidebarItemContext provider!\");\n  }\n\n  return context;\n}\n"], "names": [], "mappings": ";;AAGY,MAAC,kBAAkB,GAAG,aAAa,CAAC,KAAK,CAAC,EAAE;AACjD,SAAS,qBAAqB,GAAG;AACxC,EAAE,MAAM,OAAO,GAAG,UAAU,CAAC,kBAAkB,CAAC,CAAC;AACjD,EAAE,IAAI,CAAC,OAAO,EAAE;AAChB,IAAI,MAAM,IAAI,KAAK,CAAC,8EAA8E,CAAC,CAAC;AACpG,GAAG;AACH,EAAE,OAAO,OAAO,CAAC;AACjB;;;;"}