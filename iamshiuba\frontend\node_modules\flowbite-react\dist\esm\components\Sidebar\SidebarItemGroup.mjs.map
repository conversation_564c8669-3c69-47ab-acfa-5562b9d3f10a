{"version": 3, "file": "SidebarItemGroup.mjs", "sources": ["../../../../src/components/Sidebar/SidebarItemGroup.tsx"], "sourcesContent": ["\"use client\";\n\nimport type { ComponentProps, FC } from \"react\";\nimport { twMerge } from \"tailwind-merge\";\nimport { mergeDeep } from \"../../helpers/merge-deep\";\nimport type { DeepPartial } from \"../../types\";\nimport { useSidebarContext } from \"./SidebarContext\";\nimport { SidebarItemContext } from \"./SidebarItemContext\";\n\nexport interface FlowbiteSidebarItemGroupTheme {\n  base: string;\n}\n\nexport interface SidebarItemGroupProps extends ComponentProps<\"ul\"> {\n  theme?: DeepPartial<FlowbiteSidebarItemGroupTheme>;\n}\n\nexport const SidebarItemGroup: FC<SidebarItemGroupProps> = ({\n  children,\n  className,\n  theme: customTheme = {},\n  ...props\n}) => {\n  const { theme: rootTheme } = useSidebarContext();\n\n  const theme = mergeDeep(rootTheme.itemGroup, customTheme);\n\n  return (\n    <ul data-testid=\"flowbite-sidebar-item-group\" className={twMerge(theme.base, className)} {...props}>\n      <SidebarItemContext.Provider value={{ isInsideCollapse: false }}>{children}</SidebarItemContext.Provider>\n    </ul>\n  );\n};\n\nSidebarItemGroup.displayName = \"Sidebar.ItemGroup\";\n"], "names": [], "mappings": ";;;;;;AAOY,MAAC,gBAAgB,GAAG,CAAC;AACjC,EAAE,QAAQ;AACV,EAAE,SAAS;AACX,EAAE,KAAK,EAAE,WAAW,GAAG,EAAE;AACzB,EAAE,GAAG,KAAK;AACV,CAAC,KAAK;AACN,EAAE,MAAM,EAAE,KAAK,EAAE,SAAS,EAAE,GAAG,iBAAiB,EAAE,CAAC;AACnD,EAAE,MAAM,KAAK,GAAG,SAAS,CAAC,SAAS,CAAC,SAAS,EAAE,WAAW,CAAC,CAAC;AAC5D,EAAE,uBAAuB,GAAG,CAAC,IAAI,EAAE,EAAE,aAAa,EAAE,6BAA6B,EAAE,SAAS,EAAE,OAAO,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC,EAAE,GAAG,KAAK,EAAE,QAAQ,kBAAkB,GAAG,CAAC,kBAAkB,CAAC,QAAQ,EAAE,EAAE,KAAK,EAAE,EAAE,gBAAgB,EAAE,KAAK,EAAE,EAAE,QAAQ,EAAE,CAAC,EAAE,CAAC,CAAC;AACxP,EAAE;AACF,gBAAgB,CAAC,WAAW,GAAG,mBAAmB;;;;"}