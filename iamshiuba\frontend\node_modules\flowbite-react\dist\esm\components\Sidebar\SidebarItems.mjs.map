{"version": 3, "file": "SidebarItems.mjs", "sources": ["../../../../src/components/Sidebar/SidebarItems.tsx"], "sourcesContent": ["\"use client\";\n\nimport type { ComponentProps, FC } from \"react\";\nimport { twMerge } from \"tailwind-merge\";\nimport { mergeDeep } from \"../../helpers/merge-deep\";\nimport type { DeepPartial } from \"../../types\";\nimport { useSidebarContext } from \"./SidebarContext\";\n\nexport interface FlowbiteSidebarItemsTheme {\n  base: string;\n}\n\nexport interface SidebarItemsProps extends ComponentProps<\"div\"> {\n  theme?: DeepPartial<FlowbiteSidebarItemsTheme>;\n}\n\nexport const SidebarItems: FC<SidebarItemsProps> = ({ children, className, theme: customTheme = {}, ...props }) => {\n  const { theme: rootTheme } = useSidebarContext();\n\n  const theme = mergeDeep(rootTheme.items, customTheme);\n\n  return (\n    <div className={twMerge(theme.base, className)} data-testid=\"flowbite-sidebar-items\" {...props}>\n      {children}\n    </div>\n  );\n};\n\nSidebarItems.displayName = \"Sidebar.Items\";\n"], "names": [], "mappings": ";;;;;AAMY,MAAC,YAAY,GAAG,CAAC,EAAE,QAAQ,EAAE,SAAS,EAAE,KAAK,EAAE,WAAW,GAAG,EAAE,EAAE,GAAG,KAAK,EAAE,KAAK;AAC5F,EAAE,MAAM,EAAE,KAAK,EAAE,SAAS,EAAE,GAAG,iBAAiB,EAAE,CAAC;AACnD,EAAE,MAAM,KAAK,GAAG,SAAS,CAAC,SAAS,CAAC,KAAK,EAAE,WAAW,CAAC,CAAC;AACxD,EAAE,uBAAuB,GAAG,CAAC,KAAK,EAAE,EAAE,SAAS,EAAE,OAAO,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC,EAAE,aAAa,EAAE,wBAAwB,EAAE,GAAG,KAAK,EAAE,QAAQ,EAAE,CAAC,CAAC;AAChJ,EAAE;AACF,YAAY,CAAC,WAAW,GAAG,eAAe;;;;"}