{"version": 3, "file": "SidebarLogo.mjs", "sources": ["../../../../src/components/Sidebar/SidebarLogo.tsx"], "sourcesContent": ["\"use client\";\n\nimport type { ComponentProps, FC } from \"react\";\nimport { useId } from \"react\";\nimport { twMerge } from \"tailwind-merge\";\nimport { mergeDeep } from \"../../helpers/merge-deep\";\nimport type { DeepPartial } from \"../../types\";\nimport type { FlowbiteBoolean } from \"../Flowbite\";\nimport { useSidebarContext } from \"./SidebarContext\";\n\nexport interface FlowbiteSidebarLogoTheme {\n  base: string;\n  collapsed: FlowbiteBoolean;\n  img: string;\n}\n\nexport interface SidebarLogoProps extends ComponentProps<\"a\"> {\n  href: string;\n  img: string;\n  imgAlt?: string;\n  theme?: DeepPartial<FlowbiteSidebarLogoTheme>;\n}\n\nexport const SidebarLogo: FC<SidebarLogoProps> = ({\n  children,\n  className,\n  href,\n  img,\n  imgAlt = \"\",\n  theme: customTheme = {},\n  ...props\n}) => {\n  const id = useId();\n  const { theme: rootTheme, isCollapsed } = useSidebarContext();\n\n  const theme = mergeDeep(rootTheme.logo, customTheme);\n\n  return (\n    <a\n      aria-labelledby={`flowbite-sidebar-logo-${id}`}\n      href={href}\n      className={twMerge(theme.base, className)}\n      {...props}\n    >\n      <img alt={imgAlt} src={img} className={theme.img} />\n      <span className={theme.collapsed[isCollapsed ? \"on\" : \"off\"]} id={`flowbite-sidebar-logo-${id}`}>\n        {children}\n      </span>\n    </a>\n  );\n};\n\nSidebarLogo.displayName = \"Sidebar.Logo\";\n"], "names": [], "mappings": ";;;;;;AAOY,MAAC,WAAW,GAAG,CAAC;AAC5B,EAAE,QAAQ;AACV,EAAE,SAAS;AACX,EAAE,IAAI;AACN,EAAE,GAAG;AACL,EAAE,MAAM,GAAG,EAAE;AACb,EAAE,KAAK,EAAE,WAAW,GAAG,EAAE;AACzB,EAAE,GAAG,KAAK;AACV,CAAC,KAAK;AACN,EAAE,MAAM,EAAE,GAAG,KAAK,EAAE,CAAC;AACrB,EAAE,MAAM,EAAE,KAAK,EAAE,SAAS,EAAE,WAAW,EAAE,GAAG,iBAAiB,EAAE,CAAC;AAChE,EAAE,MAAM,KAAK,GAAG,SAAS,CAAC,SAAS,CAAC,IAAI,EAAE,WAAW,CAAC,CAAC;AACvD,EAAE,uBAAuB,IAAI;AAC7B,IAAI,GAAG;AACP,IAAI;AACJ,MAAM,iBAAiB,EAAE,CAAC,sBAAsB,EAAE,EAAE,CAAC,CAAC;AACtD,MAAM,IAAI;AACV,MAAM,SAAS,EAAE,OAAO,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC;AAC/C,MAAM,GAAG,KAAK;AACd,MAAM,QAAQ,EAAE;AAChB,wBAAwB,GAAG,CAAC,KAAK,EAAE,EAAE,GAAG,EAAE,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE,SAAS,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC;AACnF,wBAAwB,GAAG,CAAC,MAAM,EAAE,EAAE,SAAS,EAAE,KAAK,CAAC,SAAS,CAAC,WAAW,GAAG,IAAI,GAAG,KAAK,CAAC,EAAE,EAAE,EAAE,CAAC,sBAAsB,EAAE,EAAE,CAAC,CAAC,EAAE,QAAQ,EAAE,CAAC;AAC5I,OAAO;AACP,KAAK;AACL,GAAG,CAAC;AACJ,EAAE;AACF,WAAW,CAAC,WAAW,GAAG,cAAc;;;;"}