{"version": 3, "file": "Table.mjs", "sources": ["../../../../src/components/Table/Table.tsx"], "sourcesContent": ["\"use client\";\n\nimport { forwardRef, type ComponentPropsWithRef } from \"react\";\nimport { twMerge } from \"tailwind-merge\";\nimport { mergeDeep } from \"../../helpers/merge-deep\";\nimport { getTheme } from \"../../theme-store\";\nimport type { DeepPartial } from \"../../types\";\nimport { TableBody, type FlowbiteTableBodyTheme } from \"./TableBody\";\nimport { TableCell } from \"./TableCell\";\nimport { TableContext } from \"./TableContext\";\nimport { TableHead, type FlowbiteTableHeadTheme } from \"./TableHead\";\nimport { TableHeadCell } from \"./TableHeadCell\";\nimport { TableRow, type FlowbiteTableRowTheme } from \"./TableRow\";\n\nexport interface FlowbiteTableTheme {\n  root: FlowbiteTableRootTheme;\n  head: FlowbiteTableHeadTheme;\n  row: FlowbiteTableRowTheme;\n  body: FlowbiteTableBodyTheme;\n}\n\nexport interface FlowbiteTableRootTheme {\n  base: string;\n  shadow: string;\n  wrapper: string;\n}\n\nexport interface TableProps extends ComponentPropsWithRef<\"table\"> {\n  striped?: boolean;\n  hoverable?: boolean;\n  theme?: DeepPartial<FlowbiteTableTheme>;\n}\n\nconst TableComponent = forwardRef<HTMLTableElement, TableProps>(\n  ({ children, className, striped, hoverable, theme: customTheme = {}, ...props }, ref) => {\n    const theme = mergeDeep(getTheme().table, customTheme);\n\n    return (\n      <div data-testid=\"table-element\" className={twMerge(theme.root.wrapper)}>\n        <TableContext.Provider value={{ theme, striped, hoverable }}>\n          <div className={twMerge(theme.root.shadow, className)}></div>\n          <table className={twMerge(theme.root.base, className)} {...props} ref={ref}>\n            {children}\n          </table>\n        </TableContext.Provider>\n      </div>\n    );\n  },\n);\n\nTableComponent.displayName = \"Table\";\n\nexport const Table = Object.assign(TableComponent, {\n  Head: TableHead,\n  Body: TableBody,\n  Row: TableRow,\n  Cell: TableCell,\n  HeadCell: TableHeadCell,\n});\n"], "names": [], "mappings": ";;;;;;;;;;;;AAaA,MAAM,cAAc,GAAG,UAAU;AACjC,EAAE,CAAC,EAAE,QAAQ,EAAE,SAAS,EAAE,OAAO,EAAE,SAAS,EAAE,KAAK,EAAE,WAAW,GAAG,EAAE,EAAE,GAAG,KAAK,EAAE,EAAE,GAAG,KAAK;AAC3F,IAAI,MAAM,KAAK,GAAG,SAAS,CAAC,QAAQ,EAAE,CAAC,KAAK,EAAE,WAAW,CAAC,CAAC;AAC3D,IAAI,uBAAuB,GAAG,CAAC,KAAK,EAAE,EAAE,aAAa,EAAE,eAAe,EAAE,SAAS,EAAE,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,QAAQ,kBAAkB,IAAI,CAAC,YAAY,CAAC,QAAQ,EAAE,EAAE,KAAK,EAAE,EAAE,KAAK,EAAE,OAAO,EAAE,SAAS,EAAE,EAAE,QAAQ,EAAE;AACxN,sBAAsB,GAAG,CAAC,KAAK,EAAE,EAAE,SAAS,EAAE,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,SAAS,CAAC,EAAE,CAAC;AACtF,sBAAsB,GAAG,CAAC,OAAO,EAAE,EAAE,SAAS,EAAE,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,EAAE,SAAS,CAAC,EAAE,GAAG,KAAK,EAAE,GAAG,EAAE,QAAQ,EAAE,CAAC;AAC/G,KAAK,EAAE,CAAC,EAAE,CAAC,CAAC;AACZ,GAAG;AACH,CAAC,CAAC;AACF,cAAc,CAAC,WAAW,GAAG,OAAO,CAAC;AACzB,MAAC,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC,cAAc,EAAE;AACnD,EAAE,IAAI,EAAE,SAAS;AACjB,EAAE,IAAI,EAAE,SAAS;AACjB,EAAE,GAAG,EAAE,QAAQ;AACf,EAAE,IAAI,EAAE,SAAS;AACjB,EAAE,QAAQ,EAAE,aAAa;AACzB,CAAC;;;;"}