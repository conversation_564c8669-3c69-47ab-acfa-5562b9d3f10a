{"version": 3, "file": "TableBody.mjs", "sources": ["../../../../src/components/Table/TableBody.tsx"], "sourcesContent": ["\"use client\";\n\nimport { forwardRef, type ComponentPropsWithRef } from \"react\";\nimport { twMerge } from \"tailwind-merge\";\nimport { mergeDeep } from \"../../helpers/merge-deep\";\nimport type { DeepPartial } from \"../../types\";\nimport { TableBodyContext } from \"./TableBodyContext\";\nimport type { FlowbiteTableCellTheme } from \"./TableCell\";\nimport { useTableContext } from \"./TableContext\";\n\nexport interface FlowbiteTableBodyTheme {\n  base: string;\n  cell: FlowbiteTableCellTheme;\n}\n\nexport interface TableBodyProps extends ComponentPropsWithRef<\"tbody\"> {\n  theme?: DeepPartial<FlowbiteTableBodyTheme>;\n}\n\nexport const TableBody = forwardRef<HTMLTableSectionElement, TableBodyProps>(\n  ({ children, className, theme: customTheme = {}, ...props }, ref) => {\n    const { theme: rootTheme } = useTableContext();\n\n    const theme = mergeDeep(rootTheme.body, customTheme);\n\n    return (\n      <TableBodyContext.Provider value={{ theme }}>\n        <tbody className={twMerge(theme.base, className)} ref={ref} {...props}>\n          {children}\n        </tbody>\n      </TableBodyContext.Provider>\n    );\n  },\n);\n\nTableBody.displayName = \"Table.Body\";\n"], "names": [], "mappings": ";;;;;;;AAQY,MAAC,SAAS,GAAG,UAAU;AACnC,EAAE,CAAC,EAAE,QAAQ,EAAE,SAAS,EAAE,KAAK,EAAE,WAAW,GAAG,EAAE,EAAE,GAAG,KAAK,EAAE,EAAE,GAAG,KAAK;AACvE,IAAI,MAAM,EAAE,KAAK,EAAE,SAAS,EAAE,GAAG,eAAe,EAAE,CAAC;AACnD,IAAI,MAAM,KAAK,GAAG,SAAS,CAAC,SAAS,CAAC,IAAI,EAAE,WAAW,CAAC,CAAC;AACzD,IAAI,uBAAuB,GAAG,CAAC,gBAAgB,CAAC,QAAQ,EAAE,EAAE,KAAK,EAAE,EAAE,KAAK,EAAE,EAAE,QAAQ,kBAAkB,GAAG,CAAC,OAAO,EAAE,EAAE,SAAS,EAAE,OAAO,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC,EAAE,GAAG,EAAE,GAAG,KAAK,EAAE,QAAQ,EAAE,CAAC,EAAE,CAAC,CAAC;AAChM,GAAG;AACH,EAAE;AACF,SAAS,CAAC,WAAW,GAAG,YAAY;;;;"}