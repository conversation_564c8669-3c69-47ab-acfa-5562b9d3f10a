{"version": 3, "file": "TableBodyContext.mjs", "sources": ["../../../../src/components/Table/TableBodyContext.tsx"], "sourcesContent": ["\"use client\";\n\nimport { createContext, useContext } from \"react\";\nimport type { FlowbiteTableBodyTheme } from \"./TableBody\";\n\nexport type TableBodyContext = {\n  theme: FlowbiteTableBodyTheme;\n};\n\nexport const TableBodyContext = createContext<TableBodyContext | undefined>(undefined);\n\nexport function useTableBodyContext(): TableBodyContext {\n  const context = useContext(TableBodyContext);\n\n  if (!context) {\n    throw new Error(\"useTableBodyContext should be used within the TableBodyContext provider!\");\n  }\n\n  return context;\n}\n"], "names": [], "mappings": ";;AAGY,MAAC,gBAAgB,GAAG,aAAa,CAAC,KAAK,CAAC,EAAE;AAC/C,SAAS,mBAAmB,GAAG;AACtC,EAAE,MAAM,OAAO,GAAG,UAAU,CAAC,gBAAgB,CAAC,CAAC;AAC/C,EAAE,IAAI,CAAC,OAAO,EAAE;AAChB,IAAI,MAAM,IAAI,KAAK,CAAC,0EAA0E,CAAC,CAAC;AAChG,GAAG;AACH,EAAE,OAAO,OAAO,CAAC;AACjB;;;;"}