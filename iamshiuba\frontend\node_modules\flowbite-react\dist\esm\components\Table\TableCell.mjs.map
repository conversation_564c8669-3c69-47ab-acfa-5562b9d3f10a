{"version": 3, "file": "TableCell.mjs", "sources": ["../../../../src/components/Table/TableCell.tsx"], "sourcesContent": ["\"use client\";\n\nimport { forwardRef, type ComponentPropsWithRef } from \"react\";\nimport { twMerge } from \"tailwind-merge\";\nimport { mergeDeep } from \"../../helpers/merge-deep\";\nimport type { DeepPartial } from \"../../types\";\nimport { useTableBodyContext } from \"./TableBodyContext\";\n\nexport interface FlowbiteTableCellTheme {\n  base: string;\n}\n\nexport interface TableCellProps extends ComponentPropsWithRef<\"td\"> {\n  theme?: DeepPartial<FlowbiteTableCellTheme>;\n}\n\nexport const TableCell = forwardRef<HTMLTableCellElement, TableCellProps>(\n  ({ children, className, theme: customTheme = {}, ...props }, ref) => {\n    const { theme: bodyTheme } = useTableBodyContext();\n\n    const theme = mergeDeep(bodyTheme.cell, customTheme);\n\n    return (\n      <td className={twMerge(theme.base, className)} ref={ref} {...props}>\n        {children}\n      </td>\n    );\n  },\n);\n\nTableCell.displayName = \"Table.Cell\";\n"], "names": [], "mappings": ";;;;;;AAOY,MAAC,SAAS,GAAG,UAAU;AACnC,EAAE,CAAC,EAAE,QAAQ,EAAE,SAAS,EAAE,KAAK,EAAE,WAAW,GAAG,EAAE,EAAE,GAAG,KAAK,EAAE,EAAE,GAAG,KAAK;AACvE,IAAI,MAAM,EAAE,KAAK,EAAE,SAAS,EAAE,GAAG,mBAAmB,EAAE,CAAC;AACvD,IAAI,MAAM,KAAK,GAAG,SAAS,CAAC,SAAS,CAAC,IAAI,EAAE,WAAW,CAAC,CAAC;AACzD,IAAI,uBAAuB,GAAG,CAAC,IAAI,EAAE,EAAE,SAAS,EAAE,OAAO,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC,EAAE,GAAG,EAAE,GAAG,KAAK,EAAE,QAAQ,EAAE,CAAC,CAAC;AAC7G,GAAG;AACH,EAAE;AACF,SAAS,CAAC,WAAW,GAAG,YAAY;;;;"}