{"version": 3, "file": "TableContext.mjs", "sources": ["../../../../src/components/Table/TableContext.tsx"], "sourcesContent": ["\"use client\";\n\nimport { createContext, useContext } from \"react\";\nimport type { FlowbiteTableTheme } from \"./Table\";\n\nexport type TableContext = {\n  theme: FlowbiteTableTheme;\n  striped?: boolean;\n  hoverable?: boolean;\n};\n\nexport const TableContext = createContext<TableContext | undefined>(undefined);\n\nexport function useTableContext(): TableContext {\n  const context = useContext(TableContext);\n\n  if (!context) {\n    throw new Error(\"useTableContext should be used within the TableContext provider!\");\n  }\n\n  return context;\n}\n"], "names": [], "mappings": ";;AAGY,MAAC,YAAY,GAAG,aAAa,CAAC,KAAK,CAAC,EAAE;AAC3C,SAAS,eAAe,GAAG;AAClC,EAAE,MAAM,OAAO,GAAG,UAAU,CAAC,YAAY,CAAC,CAAC;AAC3C,EAAE,IAAI,CAAC,OAAO,EAAE;AAChB,IAAI,MAAM,IAAI,KAAK,CAAC,kEAAkE,CAAC,CAAC;AACxF,GAAG;AACH,EAAE,OAAO,OAAO,CAAC;AACjB;;;;"}