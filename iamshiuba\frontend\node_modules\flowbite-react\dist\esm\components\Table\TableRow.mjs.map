{"version": 3, "file": "TableRow.mjs", "sources": ["../../../../src/components/Table/TableRow.tsx"], "sourcesContent": ["\"use client\";\n\nimport { forwardRef, type ComponentPropsWithRef } from \"react\";\nimport { twMerge } from \"tailwind-merge\";\nimport { mergeDeep } from \"../../helpers/merge-deep\";\nimport type { DeepPartial } from \"../../types\";\nimport { useTableContext } from \"./TableContext\";\n\nexport interface FlowbiteTableRowTheme {\n  base: string;\n  hovered: string;\n  striped: string;\n}\n\nexport interface TableRowProps extends ComponentPropsWithRef<\"tr\"> {\n  theme?: DeepPartial<FlowbiteTableRowTheme>;\n}\n\nexport const TableRow = forwardRef<HTMLTableRowElement, TableRowProps>(\n  ({ children, className, theme: customTheme = {}, ...props }, ref) => {\n    const { theme: rootTheme, hoverable, striped } = useTableContext();\n\n    const theme = mergeDeep(rootTheme.row, customTheme);\n\n    return (\n      <tr\n        ref={ref}\n        data-testid=\"table-row-element\"\n        className={twMerge(theme.base, striped && theme.striped, hoverable && theme.hovered, className)}\n        {...props}\n      >\n        {children}\n      </tr>\n    );\n  },\n);\n\nTableRow.displayName = \"Table.Row\";\n"], "names": [], "mappings": ";;;;;;AAOY,MAAC,QAAQ,GAAG,UAAU;AAClC,EAAE,CAAC,EAAE,QAAQ,EAAE,SAAS,EAAE,KAAK,EAAE,WAAW,GAAG,EAAE,EAAE,GAAG,KAAK,EAAE,EAAE,GAAG,KAAK;AACvE,IAAI,MAAM,EAAE,KAAK,EAAE,SAAS,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,eAAe,EAAE,CAAC;AACvE,IAAI,MAAM,KAAK,GAAG,SAAS,CAAC,SAAS,CAAC,GAAG,EAAE,WAAW,CAAC,CAAC;AACxD,IAAI,uBAAuB,GAAG;AAC9B,MAAM,IAAI;AACV,MAAM;AACN,QAAQ,GAAG;AACX,QAAQ,aAAa,EAAE,mBAAmB;AAC1C,QAAQ,SAAS,EAAE,OAAO,CAAC,KAAK,CAAC,IAAI,EAAE,OAAO,IAAI,KAAK,CAAC,OAAO,EAAE,SAAS,IAAI,KAAK,CAAC,OAAO,EAAE,SAAS,CAAC;AACvG,QAAQ,GAAG,KAAK;AAChB,QAAQ,QAAQ;AAChB,OAAO;AACP,KAAK,CAAC;AACN,GAAG;AACH,EAAE;AACF,QAAQ,CAAC,WAAW,GAAG,WAAW;;;;"}