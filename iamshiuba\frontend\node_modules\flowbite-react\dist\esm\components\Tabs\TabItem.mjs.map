{"version": 3, "file": "TabItem.mjs", "sources": ["../../../../src/components/Tabs/TabItem.tsx"], "sourcesContent": ["\"use client\";\n\nimport type { ComponentProps, FC, ReactNode } from \"react\";\n\nexport interface TabItemProps extends Omit<ComponentProps<\"div\">, \"title\"> {\n  active?: boolean;\n  disabled?: boolean;\n  icon?: FC<ComponentProps<\"svg\">>;\n  title: ReactNode;\n}\n\nexport const TabItem: FC<TabItemProps> = ({ children, className }) => <div className={className}>{children}</div>;\n\nTabItem.displayName = \"Tabs.Item\";\n"], "names": [], "mappings": ";;AAGY,MAAC,OAAO,GAAG,CAAC,EAAE,QAAQ,EAAE,SAAS,EAAE,qBAAqB,GAAG,CAAC,KAAK,EAAE,EAAE,SAAS,EAAE,QAAQ,EAAE,EAAE;AACxG,OAAO,CAAC,WAAW,GAAG,WAAW;;;;"}