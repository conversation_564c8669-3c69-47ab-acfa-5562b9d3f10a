{"version": 3, "file": "theme.mjs", "sources": ["../../../../src/components/TextInput/theme.ts"], "sourcesContent": ["import { createTheme } from \"../../helpers/create-theme\";\nimport type { FlowbiteTextInputTheme } from \"./TextInput\";\n\nexport const textInputTheme: FlowbiteTextInputTheme = createTheme({\n  base: \"flex\",\n  addon:\n    \"inline-flex items-center rounded-l-md border border-r-0 border-gray-300 bg-gray-200 px-3 text-sm text-gray-900 dark:border-gray-600 dark:bg-gray-600 dark:text-gray-400\",\n  field: {\n    base: \"relative w-full\",\n    icon: {\n      base: \"pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3\",\n      svg: \"h-5 w-5 text-gray-500 dark:text-gray-400\",\n    },\n    rightIcon: {\n      base: \"pointer-events-none absolute inset-y-0 right-0 flex items-center pr-3\",\n      svg: \"h-5 w-5 text-gray-500 dark:text-gray-400\",\n    },\n    input: {\n      base: \"block w-full border disabled:cursor-not-allowed disabled:opacity-50\",\n      sizes: {\n        sm: \"p-2 sm:text-xs\",\n        md: \"p-2.5 text-sm\",\n        lg: \"p-4 sm:text-base\",\n      },\n      colors: {\n        gray: \"border-gray-300 bg-gray-50 text-gray-900 focus:border-cyan-500 focus:ring-cyan-500 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-cyan-500 dark:focus:ring-cyan-500\",\n        info: \"border-cyan-500 bg-cyan-50 text-cyan-900 placeholder-cyan-700 focus:border-cyan-500 focus:ring-cyan-500 dark:border-cyan-400 dark:bg-cyan-100 dark:focus:border-cyan-500 dark:focus:ring-cyan-500\",\n        failure:\n          \"border-red-500 bg-red-50 text-red-900 placeholder-red-700 focus:border-red-500 focus:ring-red-500 dark:border-red-400 dark:bg-red-100 dark:focus:border-red-500 dark:focus:ring-red-500\",\n        warning:\n          \"border-yellow-500 bg-yellow-50 text-yellow-900 placeholder-yellow-700 focus:border-yellow-500 focus:ring-yellow-500 dark:border-yellow-400 dark:bg-yellow-100 dark:focus:border-yellow-500 dark:focus:ring-yellow-500\",\n        success:\n          \"border-green-500 bg-green-50 text-green-900 placeholder-green-700 focus:border-green-500 focus:ring-green-500 dark:border-green-400 dark:bg-green-100 dark:focus:border-green-500 dark:focus:ring-green-500\",\n      },\n      withRightIcon: {\n        on: \"pr-10\",\n        off: \"\",\n      },\n      withIcon: {\n        on: \"pl-10\",\n        off: \"\",\n      },\n      withAddon: {\n        on: \"rounded-r-lg\",\n        off: \"rounded-lg\",\n      },\n      withShadow: {\n        on: \"shadow-sm dark:shadow-sm-light\",\n        off: \"\",\n      },\n    },\n  },\n});\n"], "names": [], "mappings": ";;AAEY,MAAC,cAAc,GAAG,WAAW,CAAC;AAC1C,EAAE,IAAI,EAAE,MAAM;AACd,EAAE,KAAK,EAAE,yKAAyK;AAClL,EAAE,KAAK,EAAE;AACT,IAAI,IAAI,EAAE,iBAAiB;AAC3B,IAAI,IAAI,EAAE;AACV,MAAM,IAAI,EAAE,sEAAsE;AAClF,MAAM,GAAG,EAAE,0CAA0C;AACrD,KAAK;AACL,IAAI,SAAS,EAAE;AACf,MAAM,IAAI,EAAE,uEAAuE;AACnF,MAAM,GAAG,EAAE,0CAA0C;AACrD,KAAK;AACL,IAAI,KAAK,EAAE;AACX,MAAM,IAAI,EAAE,qEAAqE;AACjF,MAAM,KAAK,EAAE;AACb,QAAQ,EAAE,EAAE,gBAAgB;AAC5B,QAAQ,EAAE,EAAE,eAAe;AAC3B,QAAQ,EAAE,EAAE,kBAAkB;AAC9B,OAAO;AACP,MAAM,MAAM,EAAE;AACd,QAAQ,IAAI,EAAE,wNAAwN;AACtO,QAAQ,IAAI,EAAE,mMAAmM;AACjN,QAAQ,OAAO,EAAE,yLAAyL;AAC1M,QAAQ,OAAO,EAAE,uNAAuN;AACxO,QAAQ,OAAO,EAAE,6MAA6M;AAC9N,OAAO;AACP,MAAM,aAAa,EAAE;AACrB,QAAQ,EAAE,EAAE,OAAO;AACnB,QAAQ,GAAG,EAAE,EAAE;AACf,OAAO;AACP,MAAM,QAAQ,EAAE;AAChB,QAAQ,EAAE,EAAE,OAAO;AACnB,QAAQ,GAAG,EAAE,EAAE;AACf,OAAO;AACP,MAAM,SAAS,EAAE;AACjB,QAAQ,EAAE,EAAE,cAAc;AAC1B,QAAQ,GAAG,EAAE,YAAY;AACzB,OAAO;AACP,MAAM,UAAU,EAAE;AAClB,QAAQ,EAAE,EAAE,gCAAgC;AAC5C,QAAQ,GAAG,EAAE,EAAE;AACf,OAAO;AACP,KAAK;AACL,GAAG;AACH,CAAC;;;;"}