{"version": 3, "file": "ThemeModeScript.mjs", "sources": ["../../../../src/components/ThemeModeScript/ThemeModeScript.tsx"], "sourcesContent": ["import React from \"react\";\nimport type { ThemeMode } from \"../../hooks/use-theme-mode\";\n\nexport interface ThemeModeScriptProps extends React.ComponentPropsWithoutRef<\"script\"> {\n  mode?: ThemeMode;\n}\n\nexport const ThemeModeScript = ({ mode, ...others }: ThemeModeScriptProps) => {\n  return (\n    <script\n      {...others}\n      data-flowbite-theme-mode-script\n      dangerouslySetInnerHTML={{\n        __html: getScript({ mode, defaultMode: \"light\", localStorageKey: \"flowbite-theme-mode\" }),\n      }}\n    />\n  );\n};\n\nfunction getScript({\n  mode,\n  defaultMode,\n  localStorageKey,\n}: {\n  mode?: ThemeMode;\n  defaultMode: ThemeMode;\n  localStorageKey: string;\n}) {\n  return `\n    try {\n      const mode = window.localStorage.getItem(\"${localStorageKey}\") ?? \"${mode}\" ?? \"${defaultMode}\";\n      const computedMode =\n        mode === \"auto\" ? (window.matchMedia(\"(prefers-color-scheme: dark)\").matches ? \"dark\" : \"light\") : mode;\n\n      if (computedMode === \"dark\") {\n        document.documentElement.classList.add(\"dark\");\n      } else {\n        document.documentElement.classList.remove(\"dark\");\n      }\n    } catch (e) {}\n  `;\n}\n"], "names": [], "mappings": ";;AAEY,MAAC,eAAe,GAAG,CAAC,EAAE,IAAI,EAAE,GAAG,MAAM,EAAE,KAAK;AACxD,EAAE,uBAAuB,GAAG;AAC5B,IAAI,QAAQ;AACZ,IAAI;AACJ,MAAM,GAAG,MAAM;AACf,MAAM,iCAAiC,EAAE,IAAI;AAC7C,MAAM,uBAAuB,EAAE;AAC/B,QAAQ,MAAM,EAAE,SAAS,CAAC,EAAE,IAAI,EAAE,WAAW,EAAE,OAAO,EAAE,eAAe,EAAE,qBAAqB,EAAE,CAAC;AACjG,OAAO;AACP,KAAK;AACL,GAAG,CAAC;AACJ,EAAE;AACF,SAAS,SAAS,CAAC;AACnB,EAAE,IAAI;AACN,EAAE,WAAW;AACb,EAAE,eAAe;AACjB,CAAC,EAAE;AACH,EAAE,OAAO,CAAC;AACV;AACA,gDAAgD,EAAE,eAAe,CAAC,OAAO,EAAE,IAAI,CAAC,MAAM,EAAE,WAAW,CAAC;AACpG;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,CAAC,CAAC;AACJ;;;;"}