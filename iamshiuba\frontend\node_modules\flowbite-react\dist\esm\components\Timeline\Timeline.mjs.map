{"version": 3, "file": "Timeline.mjs", "sources": ["../../../../src/components/Timeline/Timeline.tsx"], "sourcesContent": ["\"use client\";\n\nimport type { ComponentProps, FC } from \"react\";\nimport { twMerge } from \"tailwind-merge\";\nimport { mergeDeep } from \"../../helpers/merge-deep\";\nimport { getTheme } from \"../../theme-store\";\nimport type { DeepPartial } from \"../../types\";\nimport { TimelineBody } from \"./TimelineBody\";\nimport { TimelineContent } from \"./TimelineContent\";\nimport { TimelineContext } from \"./TimelineContext\";\nimport { TimelineItem, type FlowbiteTimelineItemTheme } from \"./TimelineItem\";\nimport { TimelinePoint } from \"./TimelinePoint\";\nimport { TimelineTime } from \"./TimelineTime\";\nimport { TimelineTitle } from \"./TimelineTitle\";\n\nexport interface FlowbiteTimelineTheme {\n  root: {\n    direction: {\n      horizontal: string;\n      vertical: string;\n    };\n  };\n  item: FlowbiteTimelineItemTheme;\n}\n\nexport interface TimelineProps extends ComponentProps<\"ol\"> {\n  horizontal?: boolean;\n  theme?: DeepPartial<FlowbiteTimelineTheme>;\n}\n\nconst TimelineComponent: FC<TimelineProps> = ({\n  children,\n  className,\n  horizontal,\n  theme: customTheme = {},\n  ...props\n}) => {\n  const theme = mergeDeep(getTheme().timeline, customTheme);\n\n  return (\n    <TimelineContext.Provider value={{ theme, horizontal }}>\n      <ol\n        data-testid=\"timeline-component\"\n        className={twMerge(\n          horizontal && theme.root.direction.horizontal,\n          !horizontal && theme.root.direction.vertical,\n          className,\n        )}\n        {...props}\n      >\n        {children}\n      </ol>\n    </TimelineContext.Provider>\n  );\n};\n\nTimelineComponent.displayName = \"Timeline\";\nTimelineItem.displayName = \"Timeline.Item\";\nTimelinePoint.displayName = \"Timeline.Point\";\nTimelineContent.displayName = \"Timeline.Content\";\nTimelineTime.displayName = \"Timeline.Time\";\nTimelineTitle.displayName = \"Timeline.Title\";\nTimelineBody.displayName = \"Timeline.Body\";\n\nexport const Timeline = Object.assign(TimelineComponent, {\n  Item: TimelineItem,\n  Point: TimelinePoint,\n  Content: TimelineContent,\n  Time: TimelineTime,\n  Title: TimelineTitle,\n  Body: TimelineBody,\n});\n"], "names": [], "mappings": ";;;;;;;;;;;;AAaA,MAAM,iBAAiB,GAAG,CAAC;AAC3B,EAAE,QAAQ;AACV,EAAE,SAAS;AACX,EAAE,UAAU;AACZ,EAAE,KAAK,EAAE,WAAW,GAAG,EAAE;AACzB,EAAE,GAAG,KAAK;AACV,CAAC,KAAK;AACN,EAAE,MAAM,KAAK,GAAG,SAAS,CAAC,QAAQ,EAAE,CAAC,QAAQ,EAAE,WAAW,CAAC,CAAC;AAC5D,EAAE,uBAAuB,GAAG,CAAC,eAAe,CAAC,QAAQ,EAAE,EAAE,KAAK,EAAE,EAAE,KAAK,EAAE,UAAU,EAAE,EAAE,QAAQ,kBAAkB,GAAG;AACpH,IAAI,IAAI;AACR,IAAI;AACJ,MAAM,aAAa,EAAE,oBAAoB;AACzC,MAAM,SAAS,EAAE,OAAO;AACxB,QAAQ,UAAU,IAAI,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,UAAU;AACrD,QAAQ,CAAC,UAAU,IAAI,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,QAAQ;AACpD,QAAQ,SAAS;AACjB,OAAO;AACP,MAAM,GAAG,KAAK;AACd,MAAM,QAAQ;AACd,KAAK;AACL,GAAG,EAAE,CAAC,CAAC;AACP,CAAC,CAAC;AACF,iBAAiB,CAAC,WAAW,GAAG,UAAU,CAAC;AAC3C,YAAY,CAAC,WAAW,GAAG,eAAe,CAAC;AAC3C,aAAa,CAAC,WAAW,GAAG,gBAAgB,CAAC;AAC7C,eAAe,CAAC,WAAW,GAAG,kBAAkB,CAAC;AACjD,YAAY,CAAC,WAAW,GAAG,eAAe,CAAC;AAC3C,aAAa,CAAC,WAAW,GAAG,gBAAgB,CAAC;AAC7C,YAAY,CAAC,WAAW,GAAG,eAAe,CAAC;AAC/B,MAAC,QAAQ,GAAG,MAAM,CAAC,MAAM,CAAC,iBAAiB,EAAE;AACzD,EAAE,IAAI,EAAE,YAAY;AACpB,EAAE,KAAK,EAAE,aAAa;AACtB,EAAE,OAAO,EAAE,eAAe;AAC1B,EAAE,IAAI,EAAE,YAAY;AACpB,EAAE,KAAK,EAAE,aAAa;AACtB,EAAE,IAAI,EAAE,YAAY;AACpB,CAAC;;;;"}