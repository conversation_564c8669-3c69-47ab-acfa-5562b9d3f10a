{"version": 3, "file": "TimelineItemContext.mjs", "sources": ["../../../../src/components/Timeline/TimelineItemContext.tsx"], "sourcesContent": ["\"use client\";\n\nimport { createContext, useContext } from \"react\";\nimport type { FlowbiteTimelineItemTheme } from \"./TimelineItem\";\n\nexport type TimelineItemContext = {\n  theme: FlowbiteTimelineItemTheme;\n};\n\nexport const TimelineItemContext = createContext<TimelineItemContext | undefined>(undefined);\n\nexport function useTimelineItemContext(): TimelineItemContext {\n  const context = useContext(TimelineItemContext);\n\n  if (!context) {\n    throw new Error(\"useTimelineItemContext should be used within the TimelineItemContext provider!\");\n  }\n\n  return context;\n}\n"], "names": [], "mappings": ";;AAGY,MAAC,mBAAmB,GAAG,aAAa,CAAC,KAAK,CAAC,EAAE;AAClD,SAAS,sBAAsB,GAAG;AACzC,EAAE,MAAM,OAAO,GAAG,UAAU,CAAC,mBAAmB,CAAC,CAAC;AAClD,EAAE,IAAI,CAAC,OAAO,EAAE;AAChB,IAAI,MAAM,IAAI,KAAK,CAAC,gFAAgF,CAAC,CAAC;AACtG,GAAG;AACH,EAAE,OAAO,OAAO,CAAC;AACjB;;;;"}