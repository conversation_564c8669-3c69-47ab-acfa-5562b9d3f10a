{"version": 3, "file": "TimelinePoint.mjs", "sources": ["../../../../src/components/Timeline/TimelinePoint.tsx"], "sourcesContent": ["\"use client\";\n\nimport type { ComponentProps, FC } from \"react\";\nimport { twMerge } from \"tailwind-merge\";\nimport { mergeDeep } from \"../../helpers/merge-deep\";\nimport type { DeepPartial } from \"../../types\";\nimport { useTimelineContext } from \"./TimelineContext\";\nimport { useTimelineItemContext } from \"./TimelineItemContext\";\n\nexport interface FlowbiteTimelinePointTheme {\n  horizontal: string;\n  line: string;\n  marker: {\n    base: {\n      horizontal: string;\n      vertical: string;\n    };\n    icon: {\n      base: string;\n      wrapper: string;\n    };\n  };\n  vertical: string;\n}\n\nexport interface TimelnePointProps extends ComponentProps<\"div\"> {\n  icon?: FC<ComponentProps<\"svg\">>;\n  theme?: DeepPartial<FlowbiteTimelinePointTheme>;\n}\n\nexport const TimelinePoint: FC<TimelnePointProps> = ({\n  children,\n  className,\n  icon: Icon,\n  theme: customTheme = {},\n  ...props\n}) => {\n  const { horizontal } = useTimelineContext();\n  const { theme: itemTheme } = useTimelineItemContext();\n\n  const theme = mergeDeep(itemTheme.point, customTheme);\n\n  return (\n    <div\n      data-testid=\"timeline-point\"\n      className={twMerge(horizontal && theme.horizontal, !horizontal && theme.vertical, className)}\n      {...props}\n    >\n      {children}\n      {Icon ? (\n        <span className={twMerge(theme.marker.icon.wrapper)}>\n          <Icon aria-hidden className={twMerge(theme.marker.icon.base)} />\n        </span>\n      ) : (\n        <div\n          className={twMerge(horizontal && theme.marker.base.horizontal, !horizontal && theme.marker.base.vertical)}\n        />\n      )}\n      {horizontal && <div className={twMerge(theme.line)} />}\n    </div>\n  );\n};\n"], "names": [], "mappings": ";;;;;;AAOY,MAAC,aAAa,GAAG,CAAC;AAC9B,EAAE,QAAQ;AACV,EAAE,SAAS;AACX,EAAE,IAAI,EAAE,IAAI;AACZ,EAAE,KAAK,EAAE,WAAW,GAAG,EAAE;AACzB,EAAE,GAAG,KAAK;AACV,CAAC,KAAK;AACN,EAAE,MAAM,EAAE,UAAU,EAAE,GAAG,kBAAkB,EAAE,CAAC;AAC9C,EAAE,MAAM,EAAE,KAAK,EAAE,SAAS,EAAE,GAAG,sBAAsB,EAAE,CAAC;AACxD,EAAE,MAAM,KAAK,GAAG,SAAS,CAAC,SAAS,CAAC,KAAK,EAAE,WAAW,CAAC,CAAC;AACxD,EAAE,uBAAuB,IAAI;AAC7B,IAAI,KAAK;AACT,IAAI;AACJ,MAAM,aAAa,EAAE,gBAAgB;AACrC,MAAM,SAAS,EAAE,OAAO,CAAC,UAAU,IAAI,KAAK,CAAC,UAAU,EAAE,CAAC,UAAU,IAAI,KAAK,CAAC,QAAQ,EAAE,SAAS,CAAC;AAClG,MAAM,GAAG,KAAK;AACd,MAAM,QAAQ,EAAE;AAChB,QAAQ,QAAQ;AAChB,QAAQ,IAAI,mBAAmB,GAAG,CAAC,MAAM,EAAE,EAAE,SAAS,EAAE,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,QAAQ,kBAAkB,GAAG,CAAC,IAAI,EAAE,EAAE,aAAa,EAAE,IAAI,EAAE,SAAS,EAAE,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,mBAAmB,GAAG;AAC7N,UAAU,KAAK;AACf,UAAU;AACV,YAAY,SAAS,EAAE,OAAO,CAAC,UAAU,IAAI,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC,UAAU,IAAI,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC;AACrH,WAAW;AACX,SAAS;AACT,QAAQ,UAAU,oBAAoB,GAAG,CAAC,KAAK,EAAE,EAAE,SAAS,EAAE,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC;AACpF,OAAO;AACP,KAAK;AACL,GAAG,CAAC;AACJ;;;;"}