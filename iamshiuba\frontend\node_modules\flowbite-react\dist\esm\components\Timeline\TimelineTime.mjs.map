{"version": 3, "file": "TimelineTime.mjs", "sources": ["../../../../src/components/Timeline/TimelineTime.tsx"], "sourcesContent": ["\"use client\";\n\nimport type { ComponentProps, FC } from \"react\";\nimport { twMerge } from \"tailwind-merge\";\nimport { mergeDeep } from \"../../helpers/merge-deep\";\nimport type { DeepPartial } from \"../../types\";\nimport { useTimelineContentContext } from \"./TimelineContentContext\";\n\nexport interface FlowbiteTimelineTimeTheme {\n  base: string;\n}\n\nexport interface TimelineTimeProps extends ComponentProps<\"time\"> {\n  theme?: DeepPartial<FlowbiteTimelineTimeTheme>;\n}\n\nexport const TimelineTime: FC<TimelineTimeProps> = ({ children, className, theme: customTheme = {}, ...props }) => {\n  const { theme: contentTheme } = useTimelineContentContext();\n\n  const theme = mergeDeep(contentTheme.time, customTheme);\n\n  return (\n    <time className={twMerge(theme.base, className)} {...props}>\n      {children}\n    </time>\n  );\n};\n"], "names": [], "mappings": ";;;;;AAMY,MAAC,YAAY,GAAG,CAAC,EAAE,QAAQ,EAAE,SAAS,EAAE,KAAK,EAAE,WAAW,GAAG,EAAE,EAAE,GAAG,KAAK,EAAE,KAAK;AAC5F,EAAE,MAAM,EAAE,KAAK,EAAE,YAAY,EAAE,GAAG,yBAAyB,EAAE,CAAC;AAC9D,EAAE,MAAM,KAAK,GAAG,SAAS,CAAC,YAAY,CAAC,IAAI,EAAE,WAAW,CAAC,CAAC;AAC1D,EAAE,uBAAuB,GAAG,CAAC,MAAM,EAAE,EAAE,SAAS,EAAE,OAAO,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC,EAAE,GAAG,KAAK,EAAE,QAAQ,EAAE,CAAC,CAAC;AACxG;;;;"}