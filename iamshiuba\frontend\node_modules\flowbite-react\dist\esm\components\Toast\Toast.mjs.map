{"version": 3, "file": "Toast.mjs", "sources": ["../../../../src/components/Toast/Toast.tsx"], "sourcesContent": ["\"use client\";\n\nimport type { ComponentProps, FC } from \"react\";\nimport { useState } from \"react\";\nimport { twMerge } from \"tailwind-merge\";\nimport { mergeDeep } from \"../../helpers/merge-deep\";\nimport { getTheme } from \"../../theme-store\";\nimport type { DeepPartial } from \"../../types\";\nimport type { Duration } from \"./ToastContext\";\nimport { ToastContext } from \"./ToastContext\";\nimport { ToastToggle } from \"./ToastToggle\";\n\nexport interface FlowbiteToastTheme {\n  root: {\n    base: string;\n    closed: string;\n  };\n  toggle: {\n    base: string;\n    icon: string;\n  };\n}\n\nexport interface ToastProps extends ComponentProps<\"div\"> {\n  duration?: Duration;\n  theme?: DeepPartial<FlowbiteToastTheme>;\n}\n\nconst durationClasses: Record<Duration, string> = {\n  75: \"duration-75\",\n  100: \"duration-100\",\n  150: \"duration-150\",\n  200: \"duration-200\",\n  300: \"duration-300\",\n  500: \"duration-500\",\n  700: \"duration-700\",\n  1000: \"duration-1000\",\n};\n\nconst ToastComponent: FC<ToastProps> = ({ children, className, duration = 300, theme: customTheme = {}, ...props }) => {\n  const [isClosed, setIsClosed] = useState(false);\n  const [isRemoved, setIsRemoved] = useState(false);\n\n  const theme = mergeDeep(getTheme().toast, customTheme);\n\n  if (isRemoved) {\n    return null;\n  }\n\n  return (\n    <ToastContext.Provider value={{ theme, duration, isClosed, isRemoved, setIsClosed, setIsRemoved }}>\n      <div\n        data-testid=\"flowbite-toast\"\n        role=\"alert\"\n        className={twMerge(theme.root.base, durationClasses[duration], isClosed && theme.root.closed, className)}\n        {...props}\n      >\n        {children}\n      </div>\n    </ToastContext.Provider>\n  );\n};\n\nToastComponent.displayName = \"Toast\";\nToastToggle.displayName = \"Toast.Toggle\";\n\nexport const Toast = Object.assign(ToastComponent, {\n  Toggle: ToastToggle,\n});\n"], "names": [], "mappings": ";;;;;;;;AASA,MAAM,eAAe,GAAG;AACxB,EAAE,EAAE,EAAE,aAAa;AACnB,EAAE,GAAG,EAAE,cAAc;AACrB,EAAE,GAAG,EAAE,cAAc;AACrB,EAAE,GAAG,EAAE,cAAc;AACrB,EAAE,GAAG,EAAE,cAAc;AACrB,EAAE,GAAG,EAAE,cAAc;AACrB,EAAE,GAAG,EAAE,cAAc;AACrB,EAAE,GAAG,EAAE,eAAe;AACtB,CAAC,CAAC;AACF,MAAM,cAAc,GAAG,CAAC,EAAE,QAAQ,EAAE,SAAS,EAAE,QAAQ,GAAG,GAAG,EAAE,KAAK,EAAE,WAAW,GAAG,EAAE,EAAE,GAAG,KAAK,EAAE,KAAK;AACvG,EAAE,MAAM,CAAC,QAAQ,EAAE,WAAW,CAAC,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC;AAClD,EAAE,MAAM,CAAC,SAAS,EAAE,YAAY,CAAC,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC;AACpD,EAAE,MAAM,KAAK,GAAG,SAAS,CAAC,QAAQ,EAAE,CAAC,KAAK,EAAE,WAAW,CAAC,CAAC;AACzD,EAAE,IAAI,SAAS,EAAE;AACjB,IAAI,OAAO,IAAI,CAAC;AAChB,GAAG;AACH,EAAE,uBAAuB,GAAG,CAAC,YAAY,CAAC,QAAQ,EAAE,EAAE,KAAK,EAAE,EAAE,KAAK,EAAE,QAAQ,EAAE,QAAQ,EAAE,SAAS,EAAE,WAAW,EAAE,YAAY,EAAE,EAAE,QAAQ,kBAAkB,GAAG;AAC/J,IAAI,KAAK;AACT,IAAI;AACJ,MAAM,aAAa,EAAE,gBAAgB;AACrC,MAAM,IAAI,EAAE,OAAO;AACnB,MAAM,SAAS,EAAE,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,EAAE,eAAe,CAAC,QAAQ,CAAC,EAAE,QAAQ,IAAI,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,SAAS,CAAC;AAC9G,MAAM,GAAG,KAAK;AACd,MAAM,QAAQ;AACd,KAAK;AACL,GAAG,EAAE,CAAC,CAAC;AACP,CAAC,CAAC;AACF,cAAc,CAAC,WAAW,GAAG,OAAO,CAAC;AACrC,WAAW,CAAC,WAAW,GAAG,cAAc,CAAC;AAC7B,MAAC,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC,cAAc,EAAE;AACnD,EAAE,MAAM,EAAE,WAAW;AACrB,CAAC;;;;"}