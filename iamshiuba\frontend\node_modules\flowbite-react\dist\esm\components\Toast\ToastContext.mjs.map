{"version": 3, "file": "ToastContext.mjs", "sources": ["../../../../src/components/Toast/ToastContext.tsx"], "sourcesContent": ["\"use client\";\n\nimport { createContext, useContext } from \"react\";\nimport type { FlowbiteToastTheme } from \"./Toast\";\n\nexport type Duration = 75 | 100 | 150 | 200 | 300 | 500 | 700 | 1000;\n\ntype ToastContext = {\n  theme: FlowbiteToastTheme;\n  duration?: Duration;\n  isClosed?: boolean;\n  isRemoved?: boolean;\n  setIsClosed: (isClosed: boolean) => void;\n  setIsRemoved: (isRemoved: boolean) => void;\n};\n\nexport const ToastContext = createContext<ToastContext | undefined>(undefined);\n\nexport function useToastContext(): ToastContext {\n  const context = useContext(ToastContext);\n\n  if (!context) {\n    throw new Error(\"useToastContext should be used within the ToastContext provider!\");\n  }\n\n  return context;\n}\n"], "names": [], "mappings": ";;AAGY,MAAC,YAAY,GAAG,aAAa,CAAC,KAAK,CAAC,EAAE;AAC3C,SAAS,eAAe,GAAG;AAClC,EAAE,MAAM,OAAO,GAAG,UAAU,CAAC,YAAY,CAAC,CAAC;AAC3C,EAAE,IAAI,CAAC,OAAO,EAAE;AAChB,IAAI,MAAM,IAAI,KAAK,CAAC,kEAAkE,CAAC,CAAC;AACxF,GAAG;AACH,EAAE,OAAO,OAAO,CAAC;AACjB;;;;"}