'use client';
import { jsx } from 'react/jsx-runtime';
import { HiX } from 'react-icons/hi';
import { twMerge } from 'tailwind-merge';
import { mergeDeep } from '../../helpers/merge-deep.mjs';
import { useToastContext } from './ToastContext.mjs';

const ToastToggle = ({
  className,
  onClick,
  theme: customTheme = {},
  xIcon: XIcon = HiX,
  onDismiss,
  ...props
}) => {
  const { theme: rootTheme, duration, isClosed, isRemoved, setIsClosed, setIsRemoved } = useToastContext();
  const theme = mergeDeep(rootTheme.toggle, customTheme);
  const handleClick = (e) => {
    if (onClick) onClick(e);
    if (onDismiss) {
      onDismiss();
      return;
    }
    setIsClosed(!isClosed);
    setTimeout(() => setIsRemoved(!isRemoved), duration);
  };
  return /* @__PURE__ */ jsx(
    "button",
    {
      "aria-label": "Close",
      onClick: handleClick,
      type: "button",
      className: twMerge(theme.base, className),
      ...props,
      children: /* @__PURE__ */ jsx(XIcon, { "aria-hidden": true, className: theme.icon })
    }
  );
};

export { ToastToggle };
//# sourceMappingURL=ToastToggle.mjs.map
