{"version": 3, "file": "ToggleSwitch.mjs", "sources": ["../../../../src/components/ToggleSwitch/ToggleSwitch.tsx"], "sourcesContent": ["import type { ComponentProps, KeyboardEvent } from \"react\";\nimport { forwardRef, useId } from \"react\";\nimport { twMerge } from \"tailwind-merge\";\nimport { mergeDeep } from \"../../helpers/merge-deep\";\nimport { getTheme } from \"../../theme-store\";\nimport type { DeepPartial, DynamicStringEnumKeysOf } from \"../../types\";\nimport type { FlowbiteBoolean, FlowbiteColors } from \"../Flowbite\";\nimport type { FlowbiteTextInputSizes } from \"../TextInput\";\n\nexport interface FlowbiteToggleSwitchTheme {\n  root: FlowbiteToggleSwitchRootTheme;\n  toggle: FlowbiteToggleSwitchToggleTheme;\n}\n\nexport interface FlowbiteToggleSwitchRootTheme {\n  base: string;\n  active: FlowbiteBoolean;\n  label: string;\n}\n\nexport interface FlowbiteToggleSwitchToggleTheme {\n  base: string;\n  sizes: FlowbiteTextInputSizes;\n  checked: FlowbiteBoolean & {\n    color: FlowbiteColors;\n  };\n}\n\nexport type ToggleSwitchProps = Omit<ComponentProps<\"button\">, \"onChange\" | \"ref\"> & {\n  checked: boolean;\n  color?: DynamicStringEnumKeysOf<FlowbiteColors>;\n  sizing?: DynamicStringEnumKeysOf<FlowbiteTextInputSizes>;\n  label?: string;\n  onChange: (checked: boolean) => void;\n  theme?: DeepPartial<FlowbiteToggleSwitchTheme>;\n};\n\nexport const ToggleSwitch = forwardRef<HTMLInputElement, ToggleSwitchProps>(\n  (\n    {\n      checked,\n      className,\n      color = \"blue\",\n      sizing = \"md\",\n      disabled,\n      label,\n      name,\n      onChange,\n      theme: customTheme = {},\n      ...props\n    },\n    ref,\n  ) => {\n    const id = useId();\n    const theme = mergeDeep(getTheme().toggleSwitch, customTheme);\n\n    const toggle = (): void => onChange(!checked);\n\n    const handleClick = (): void => {\n      toggle();\n    };\n\n    const handleOnKeyDown = (event: KeyboardEvent<HTMLButtonElement>): void => {\n      if (event.code == \"Enter\") {\n        event.preventDefault();\n      }\n    };\n\n    return (\n      <>\n        {name && checked ? (\n          <input ref={ref} checked={checked} hidden name={name} readOnly type=\"checkbox\" className=\"sr-only\" />\n        ) : null}\n        <button\n          aria-checked={checked}\n          aria-labelledby={`${id}-flowbite-toggleswitch-label`}\n          disabled={disabled}\n          id={`${id}-flowbite-toggleswitch`}\n          onClick={handleClick}\n          onKeyDown={handleOnKeyDown}\n          role=\"switch\"\n          tabIndex={0}\n          type=\"button\"\n          className={twMerge(theme.root.base, theme.root.active[disabled ? \"off\" : \"on\"], className)}\n          {...props}\n        >\n          <div\n            data-testid=\"flowbite-toggleswitch-toggle\"\n            className={twMerge(\n              theme.toggle.base,\n              theme.toggle.checked[checked ? \"on\" : \"off\"],\n              checked && theme.toggle.checked.color[color],\n              theme.toggle.sizes[sizing],\n            )}\n          />\n          {label?.length ? (\n            <span\n              data-testid=\"flowbite-toggleswitch-label\"\n              id={`${id}-flowbite-toggleswitch-label`}\n              className={theme.root.label}\n            >\n              {label}\n            </span>\n          ) : null}\n        </button>\n      </>\n    );\n  },\n);\n\nToggleSwitch.displayName = \"ToggleSwitch\";\n"], "names": [], "mappings": ";;;;;;AAMY,MAAC,YAAY,GAAG,UAAU;AACtC,EAAE,CAAC;AACH,IAAI,OAAO;AACX,IAAI,SAAS;AACb,IAAI,KAAK,GAAG,MAAM;AAClB,IAAI,MAAM,GAAG,IAAI;AACjB,IAAI,QAAQ;AACZ,IAAI,KAAK;AACT,IAAI,IAAI;AACR,IAAI,QAAQ;AACZ,IAAI,KAAK,EAAE,WAAW,GAAG,EAAE;AAC3B,IAAI,GAAG,KAAK;AACZ,GAAG,EAAE,GAAG,KAAK;AACb,IAAI,MAAM,EAAE,GAAG,KAAK,EAAE,CAAC;AACvB,IAAI,MAAM,KAAK,GAAG,SAAS,CAAC,QAAQ,EAAE,CAAC,YAAY,EAAE,WAAW,CAAC,CAAC;AAClE,IAAI,MAAM,MAAM,GAAG,MAAM,QAAQ,CAAC,CAAC,OAAO,CAAC,CAAC;AAC5C,IAAI,MAAM,WAAW,GAAG,MAAM;AAC9B,MAAM,MAAM,EAAE,CAAC;AACf,KAAK,CAAC;AACN,IAAI,MAAM,eAAe,GAAG,CAAC,KAAK,KAAK;AACvC,MAAM,IAAI,KAAK,CAAC,IAAI,IAAI,OAAO,EAAE;AACjC,QAAQ,KAAK,CAAC,cAAc,EAAE,CAAC;AAC/B,OAAO;AACP,KAAK,CAAC;AACN,IAAI,uBAAuB,IAAI,CAAC,QAAQ,EAAE,EAAE,QAAQ,EAAE;AACtD,MAAM,IAAI,IAAI,OAAO,mBAAmB,GAAG,CAAC,OAAO,EAAE,EAAE,GAAG,EAAE,OAAO,EAAE,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,IAAI,EAAE,UAAU,EAAE,SAAS,EAAE,SAAS,EAAE,CAAC,GAAG,IAAI;AACzJ,sBAAsB,IAAI;AAC1B,QAAQ,QAAQ;AAChB,QAAQ;AACR,UAAU,cAAc,EAAE,OAAO;AACjC,UAAU,iBAAiB,EAAE,CAAC,EAAE,EAAE,CAAC,4BAA4B,CAAC;AAChE,UAAU,QAAQ;AAClB,UAAU,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,sBAAsB,CAAC;AAC3C,UAAU,OAAO,EAAE,WAAW;AAC9B,UAAU,SAAS,EAAE,eAAe;AACpC,UAAU,IAAI,EAAE,QAAQ;AACxB,UAAU,QAAQ,EAAE,CAAC;AACrB,UAAU,IAAI,EAAE,QAAQ;AACxB,UAAU,SAAS,EAAE,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,GAAG,KAAK,GAAG,IAAI,CAAC,EAAE,SAAS,CAAC;AACpG,UAAU,GAAG,KAAK;AAClB,UAAU,QAAQ,EAAE;AACpB,4BAA4B,GAAG;AAC/B,cAAc,KAAK;AACnB,cAAc;AACd,gBAAgB,aAAa,EAAE,8BAA8B;AAC7D,gBAAgB,SAAS,EAAE,OAAO;AAClC,kBAAkB,KAAK,CAAC,MAAM,CAAC,IAAI;AACnC,kBAAkB,KAAK,CAAC,MAAM,CAAC,OAAO,CAAC,OAAO,GAAG,IAAI,GAAG,KAAK,CAAC;AAC9D,kBAAkB,OAAO,IAAI,KAAK,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC;AAC9D,kBAAkB,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC;AAC5C,iBAAiB;AACjB,eAAe;AACf,aAAa;AACb,YAAY,KAAK,EAAE,MAAM,mBAAmB,GAAG;AAC/C,cAAc,MAAM;AACpB,cAAc;AACd,gBAAgB,aAAa,EAAE,6BAA6B;AAC5D,gBAAgB,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,4BAA4B,CAAC;AACvD,gBAAgB,SAAS,EAAE,KAAK,CAAC,IAAI,CAAC,KAAK;AAC3C,gBAAgB,QAAQ,EAAE,KAAK;AAC/B,eAAe;AACf,aAAa,GAAG,IAAI;AACpB,WAAW;AACX,SAAS;AACT,OAAO;AACP,KAAK,EAAE,CAAC,CAAC;AACT,GAAG;AACH,EAAE;AACF,YAAY,CAAC,WAAW,GAAG,cAAc;;;;"}