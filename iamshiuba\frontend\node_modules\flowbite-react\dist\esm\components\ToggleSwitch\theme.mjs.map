{"version": 3, "file": "theme.mjs", "sources": ["../../../../src/components/ToggleSwitch/theme.ts"], "sourcesContent": ["import { createTheme } from \"../../helpers/create-theme\";\nimport type { FlowbiteToggleSwitchTheme } from \"./ToggleSwitch\";\n\nexport const toggleSwitchTheme: FlowbiteToggleSwitchTheme = createTheme({\n  root: {\n    base: \"group flex rounded-lg focus:outline-none\",\n    active: {\n      on: \"cursor-pointer\",\n      off: \"cursor-not-allowed opacity-50\",\n    },\n    label: \"ms-3 mt-0.5 text-start text-sm font-medium text-gray-900 dark:text-gray-300\",\n  },\n  toggle: {\n    base: \"relative rounded-full border after:absolute after:rounded-full after:bg-white after:transition-all group-focus:ring-4 group-focus:ring-cyan-500/25\",\n    checked: {\n      on: \"after:translate-x-full after:border-white rtl:after:-translate-x-full\",\n      off: \"border-gray-200 bg-gray-200 dark:border-gray-600 dark:bg-gray-700\",\n      color: {\n        blue: \"border-cyan-700 bg-cyan-700\",\n        dark: \"bg-dark-700 border-dark-900\",\n        failure: \"border-red-900 bg-red-700\",\n        gray: \"border-gray-600 bg-gray-500\",\n        green: \"border-green-700 bg-green-600\",\n        light: \"bg-light-700 border-light-900\",\n        red: \"border-red-900 bg-red-700\",\n        purple: \"border-purple-900 bg-purple-700\",\n        success: \"border-green-500 bg-green-500\",\n        yellow: \"border-yellow-400 bg-yellow-400\",\n        warning: \"border-yellow-600 bg-yellow-600\",\n        cyan: \"border-cyan-500 bg-cyan-500\",\n        lime: \"border-lime-400 bg-lime-400\",\n        indigo: \"border-indigo-400 bg-indigo-400\",\n        teal: \"bg-gradient-to-r from-teal-400 via-teal-500 to-teal-600 hover:bg-gradient-to-br focus:ring-4\",\n        info: \"border-cyan-600 bg-cyan-600\",\n        pink: \"border-pink-600 bg-pink-600\",\n      },\n    },\n    sizes: {\n      sm: \"h-5 w-9 min-w-9 after:left-px after:top-px after:h-4 after:w-4 rtl:after:right-px\",\n      md: \"h-6 w-11 min-w-11 after:left-px after:top-px after:h-5 after:w-5 rtl:after:right-px\",\n      lg: \"h-7 w-14 min-w-14 after:left-1 after:top-0.5 after:h-6 after:w-6 rtl:after:right-1\",\n    },\n  },\n});\n"], "names": [], "mappings": ";;AAEY,MAAC,iBAAiB,GAAG,WAAW,CAAC;AAC7C,EAAE,IAAI,EAAE;AACR,IAAI,IAAI,EAAE,0CAA0C;AACpD,IAAI,MAAM,EAAE;AACZ,MAAM,EAAE,EAAE,gBAAgB;AAC1B,MAAM,GAAG,EAAE,+BAA+B;AAC1C,KAAK;AACL,IAAI,KAAK,EAAE,6EAA6E;AACxF,GAAG;AACH,EAAE,MAAM,EAAE;AACV,IAAI,IAAI,EAAE,oJAAoJ;AAC9J,IAAI,OAAO,EAAE;AACb,MAAM,EAAE,EAAE,uEAAuE;AACjF,MAAM,GAAG,EAAE,mEAAmE;AAC9E,MAAM,KAAK,EAAE;AACb,QAAQ,IAAI,EAAE,6BAA6B;AAC3C,QAAQ,IAAI,EAAE,6BAA6B;AAC3C,QAAQ,OAAO,EAAE,2BAA2B;AAC5C,QAAQ,IAAI,EAAE,6BAA6B;AAC3C,QAAQ,KAAK,EAAE,+BAA+B;AAC9C,QAAQ,KAAK,EAAE,+BAA+B;AAC9C,QAAQ,GAAG,EAAE,2BAA2B;AACxC,QAAQ,MAAM,EAAE,iCAAiC;AACjD,QAAQ,OAAO,EAAE,+BAA+B;AAChD,QAAQ,MAAM,EAAE,iCAAiC;AACjD,QAAQ,OAAO,EAAE,iCAAiC;AAClD,QAAQ,IAAI,EAAE,6BAA6B;AAC3C,QAAQ,IAAI,EAAE,6BAA6B;AAC3C,QAAQ,MAAM,EAAE,iCAAiC;AACjD,QAAQ,IAAI,EAAE,8FAA8F;AAC5G,QAAQ,IAAI,EAAE,6BAA6B;AAC3C,QAAQ,IAAI,EAAE,6BAA6B;AAC3C,OAAO;AACP,KAAK;AACL,IAAI,KAAK,EAAE;AACX,MAAM,EAAE,EAAE,mFAAmF;AAC7F,MAAM,EAAE,EAAE,qFAAqF;AAC/F,MAAM,EAAE,EAAE,oFAAoF;AAC9F,KAAK;AACL,GAAG;AACH,CAAC;;;;"}