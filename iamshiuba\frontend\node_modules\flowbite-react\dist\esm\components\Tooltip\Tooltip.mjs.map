{"version": 3, "file": "Tooltip.mjs", "sources": ["../../../../src/components/Tooltip/Tooltip.tsx"], "sourcesContent": ["import type { Placement } from \"@floating-ui/core\";\nimport type { ComponentProps, FC, ReactNode } from \"react\";\nimport { mergeDeep } from \"../../helpers/merge-deep\";\nimport { getTheme } from \"../../theme-store\";\nimport type { DeepPartial } from \"../../types\";\nimport { Floating, type FlowbiteFloatingTheme } from \"../Floating\";\n\nexport type FlowbiteTooltipTheme = FlowbiteFloatingTheme;\n\nexport interface TooltipProps extends Omit<ComponentProps<\"div\">, \"content\" | \"style\"> {\n  animation?: false | `duration-${number}`;\n  arrow?: boolean;\n  content: ReactNode;\n  placement?: \"auto\" | Placement;\n  style?: \"dark\" | \"light\" | \"auto\";\n  theme?: DeepPartial<FlowbiteTooltipTheme>;\n  trigger?: \"hover\" | \"click\";\n}\n\n/**\n * @see https://floating-ui.com/docs/react-dom-interactions\n */\nexport const Tooltip: FC<TooltipProps> = ({\n  animation = \"duration-300\",\n  arrow = true,\n  children,\n  className,\n  content,\n  placement = \"top\",\n  style = \"dark\",\n  theme: customTheme = {},\n  trigger = \"hover\",\n  ...props\n}) => {\n  const theme = mergeDeep(getTheme().tooltip, customTheme);\n\n  return (\n    <Floating\n      animation={animation}\n      arrow={arrow}\n      content={content}\n      placement={placement}\n      style={style}\n      theme={theme}\n      trigger={trigger}\n      className={className}\n      {...props}\n    >\n      {children}\n    </Floating>\n  );\n};\n\nTooltip.displayName = \"Tooltip\";\n"], "names": [], "mappings": ";;;;;AAKY,MAAC,OAAO,GAAG,CAAC;AACxB,EAAE,SAAS,GAAG,cAAc;AAC5B,EAAE,KAAK,GAAG,IAAI;AACd,EAAE,QAAQ;AACV,EAAE,SAAS;AACX,EAAE,OAAO;AACT,EAAE,SAAS,GAAG,KAAK;AACnB,EAAE,KAAK,GAAG,MAAM;AAChB,EAAE,KAAK,EAAE,WAAW,GAAG,EAAE;AACzB,EAAE,OAAO,GAAG,OAAO;AACnB,EAAE,GAAG,KAAK;AACV,CAAC,KAAK;AACN,EAAE,MAAM,KAAK,GAAG,SAAS,CAAC,QAAQ,EAAE,CAAC,OAAO,EAAE,WAAW,CAAC,CAAC;AAC3D,EAAE,uBAAuB,GAAG;AAC5B,IAAI,QAAQ;AACZ,IAAI;AACJ,MAAM,SAAS;AACf,MAAM,KAAK;AACX,MAAM,OAAO;AACb,MAAM,SAAS;AACf,MAAM,KAAK;AACX,MAAM,KAAK;AACX,MAAM,OAAO;AACb,MAAM,SAAS;AACf,MAAM,GAAG,KAAK;AACd,MAAM,QAAQ;AACd,KAAK;AACL,GAAG,CAAC;AACJ,EAAE;AACF,OAAO,CAAC,WAAW,GAAG,SAAS;;;;"}