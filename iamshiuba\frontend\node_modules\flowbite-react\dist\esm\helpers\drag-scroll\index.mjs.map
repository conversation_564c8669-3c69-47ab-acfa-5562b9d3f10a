{"version": 3, "file": "index.mjs", "sources": ["../../../../src/helpers/drag-scroll/index.tsx"], "sourcesContent": ["import classnames from \"classnames\";\nimport debounce from \"debounce\";\nimport type { CSSProperties, ElementType, MutableRefObject, ReactNode, Ref, RefObject } from \"react\";\nimport React, { PureComponent } from \"react\";\n\nexport interface ScrollEvent {\n  external: boolean;\n}\n\nexport interface ScrollContainerProps {\n  vertical?: boolean;\n  horizontal?: boolean;\n  hideScrollbars?: boolean;\n  activationDistance?: number;\n  children?: ReactNode;\n  onStartScroll?: (event: ScrollEvent) => void;\n  onScroll?: (event: ScrollEvent) => void;\n  onEndScrolll?: (event: ScrollEvent) => void;\n  onClick?: (event: MouseEvent) => void;\n  className?: string;\n  draggingClassName?: string;\n  style?: CSSProperties;\n  ignoreElements?: string;\n  nativeMobileScroll?: boolean;\n  ref?: ReactNode;\n  component?: ElementType;\n  innerRef?: Ref<HTMLElement>;\n  stopPropagation?: boolean;\n  buttons?: number[];\n}\n\nconst SCROLL_END_DEBOUNCE = 300;\n\nconst LEFT_BUTTON = 0;\n\nexport interface ScrollEvent {\n  external: boolean;\n}\n\ninterface Props {\n  vertical?: boolean;\n  horizontal?: boolean;\n  hideScrollbars?: boolean;\n  activationDistance?: number;\n  children?: ReactNode;\n  onStartScroll?: (event: ScrollEvent) => void;\n  onScroll?: (event: ScrollEvent) => void;\n  onEndScroll?: (event: ScrollEvent) => void;\n  onClick?: (event: MouseEvent) => void;\n  className?: string;\n  draggingClassName?: string;\n  style?: CSSProperties;\n  ignoreElements?: string;\n  nativeMobileScroll?: boolean;\n  ref?: ReactNode;\n  innerRef?: Ref<HTMLElement>;\n  stopPropagation?: boolean;\n  buttons?: number[];\n}\n\nexport default class ScrollContainer extends PureComponent<Props> {\n  static defaultProps = {\n    nativeMobileScroll: true,\n    hideScrollbars: true,\n    activationDistance: 10,\n    vertical: true,\n    horizontal: true,\n    stopPropagation: false,\n    style: {},\n    buttons: [LEFT_BUTTON],\n  };\n  container: RefObject<HTMLElement>;\n  scrolling: boolean;\n  started: boolean;\n  pressed: boolean;\n  isMobile: boolean = false;\n  internal: boolean;\n\n  scrollLeft?: number;\n  scrollTop?: number;\n  clientX?: number;\n  clientY?: number;\n\n  constructor(props: ScrollContainerProps) {\n    super(props);\n    this.container = React.createRef();\n    this.onEndScroll = debounce(this.onEndScroll, SCROLL_END_DEBOUNCE);\n\n    // Is container scrolling now (for example by inertia)\n    this.scrolling = false;\n    // Is scrolling started\n    this.started = false;\n    // Is touch active or mouse pressed down\n    this.pressed = false;\n    // Is event internal\n    this.internal = false;\n\n    // Bind callbacks\n    this.getRef = this.getRef.bind(this);\n  }\n\n  componentDidMount() {\n    const { nativeMobileScroll } = this.props;\n    const container = this.container.current!;\n\n    window.addEventListener(\"mouseup\", this.onMouseUp);\n    window.addEventListener(\"mousemove\", this.onMouseMove);\n    window.addEventListener(\"touchmove\", this.onTouchMove, { passive: false });\n    window.addEventListener(\"touchend\", this.onTouchEnd);\n\n    // due to https://github.com/facebook/react/issues/9809#issuecomment-414072263\n    container.addEventListener(\"touchstart\", this.onTouchStart, {\n      passive: false,\n    });\n    container.addEventListener(\"mousedown\", this.onMouseDown, {\n      passive: false,\n    });\n\n    if (nativeMobileScroll) {\n      // We should check if it's the mobile device after page was loaded\n      // to prevent breaking SSR\n      this.isMobile = this.isMobileDevice();\n\n      // If it's the mobile device, we should rerender to change styles\n      if (this.isMobile) {\n        this.forceUpdate();\n      }\n    }\n  }\n\n  componentWillUnmount() {\n    window.removeEventListener(\"mouseup\", this.onMouseUp);\n    window.removeEventListener(\"mousemove\", this.onMouseMove);\n    window.removeEventListener(\"touchmove\", this.onTouchMove);\n    window.removeEventListener(\"touchend\", this.onTouchEnd);\n  }\n\n  getElement() {\n    return this.container.current;\n  }\n\n  isMobileDevice() {\n    return typeof window.orientation !== \"undefined\" || navigator.userAgent.indexOf(\"IEMobile\") !== -1;\n  }\n\n  isDraggable(target: HTMLElement) {\n    const ignoreElements = this.props.ignoreElements;\n    if (ignoreElements) {\n      const closest = target.closest(ignoreElements);\n      return closest === null || closest.contains(this.getElement());\n    } else {\n      return true;\n    }\n  }\n\n  isScrollable() {\n    const container = this.container.current;\n    return (\n      container && (container.scrollWidth > container.clientWidth || container.scrollHeight > container.clientHeight)\n    );\n  }\n\n  // Simulate 'onEndScroll' event that fires when scrolling is stopped\n  onEndScroll = () => {\n    this.scrolling = false;\n    if (!this.pressed && this.started) {\n      this.processEnd();\n    }\n  };\n\n  onScroll = () => {\n    const container = this.container.current!;\n    // Ignore the internal scrolls\n    if (container.scrollLeft !== this.scrollLeft || container.scrollTop !== this.scrollTop) {\n      this.scrolling = true;\n      this.processScroll();\n      this.onEndScroll();\n    }\n  };\n\n  onTouchStart = (e: TouchEvent) => {\n    const { nativeMobileScroll } = this.props;\n    if (this.isDraggable(e.target as HTMLElement)) {\n      this.internal = true;\n      if (nativeMobileScroll && this.scrolling) {\n        this.pressed = true;\n      } else {\n        const touch = e.touches[0];\n        this.processClick(touch.clientX, touch.clientY);\n        if (!nativeMobileScroll && this.props.stopPropagation) {\n          e.stopPropagation();\n        }\n      }\n    }\n  };\n\n  onTouchEnd = () => {\n    const { nativeMobileScroll } = this.props;\n    if (this.pressed) {\n      if (this.started && (!this.scrolling || !nativeMobileScroll)) {\n        this.processEnd();\n      } else {\n        this.pressed = false;\n      }\n      this.forceUpdate();\n    }\n  };\n\n  onTouchMove = (e: TouchEvent) => {\n    const { nativeMobileScroll } = this.props;\n    if (this.pressed && (!nativeMobileScroll || !this.isMobile)) {\n      const touch = e.touches[0];\n      if (touch) {\n        this.processMove(touch.clientX, touch.clientY);\n      }\n      e.preventDefault();\n      if (this.props.stopPropagation) {\n        e.stopPropagation();\n      }\n    }\n  };\n\n  onMouseDown = (e: MouseEvent) => {\n    if (this.isDraggable(e.target as HTMLElement) && this.isScrollable()) {\n      this.internal = true;\n      if (this.props?.buttons?.indexOf(e.button) !== -1) {\n        this.processClick(e.clientX, e.clientY);\n        e.preventDefault();\n        if (this.props.stopPropagation) {\n          e.stopPropagation();\n        }\n      }\n    }\n  };\n\n  onMouseMove = (e: MouseEvent) => {\n    if (this.pressed) {\n      this.processMove(e.clientX, e.clientY);\n      e.preventDefault();\n      if (this.props.stopPropagation) {\n        e.stopPropagation();\n      }\n    }\n  };\n\n  onMouseUp = (e: MouseEvent) => {\n    if (this.pressed) {\n      if (this.started) {\n        this.processEnd();\n      } else {\n        this.internal = false;\n        this.pressed = false;\n        this.forceUpdate();\n        if (this.props.onClick) {\n          this.props.onClick(e);\n        }\n      }\n      e.preventDefault();\n      if (this.props.stopPropagation) {\n        e.stopPropagation();\n      }\n    }\n  };\n\n  processClick(clientX: number, clientY: number) {\n    const container = this.container.current;\n    this.scrollLeft = container?.scrollLeft;\n    this.scrollTop = container?.scrollTop;\n    this.clientX = clientX;\n    this.clientY = clientY;\n    this.pressed = true;\n  }\n\n  processStart(changeCursor = true) {\n    const { onStartScroll } = this.props;\n\n    this.started = true;\n\n    // Add the class to change displayed cursor\n    if (changeCursor) {\n      document.body.classList.add(\"cursor-grab\");\n    }\n\n    if (onStartScroll) {\n      onStartScroll({\n        external: !this.internal,\n      });\n    }\n    this.forceUpdate();\n  }\n\n  // Process native scroll (scrollbar, mobile scroll)\n  processScroll() {\n    if (this.started) {\n      const { onScroll } = this.props;\n      if (onScroll) {\n        onScroll({\n          external: !this.internal,\n        });\n      }\n    } else {\n      this.processStart(false);\n    }\n  }\n\n  // Process non-native scroll\n  processMove(newClientX: number, newClientY: number) {\n    const { horizontal, vertical, activationDistance, onScroll } = this.props;\n    const container = this.container.current!;\n\n    if (!this.started) {\n      if (\n        (horizontal && Math.abs(newClientX - this.clientX!) > activationDistance!) ||\n        (vertical && Math.abs(newClientY - this.clientY!) > activationDistance!)\n      ) {\n        this.clientX = newClientX;\n        this.clientY = newClientY;\n        this.processStart();\n      }\n    } else {\n      if (horizontal) {\n        container.scrollLeft -= newClientX - this.clientX!;\n      }\n      if (vertical) {\n        container.scrollTop -= newClientY - this.clientY!;\n      }\n      if (onScroll) {\n        onScroll({ external: !this.internal });\n      }\n      this.clientX = newClientX;\n      this.clientY = newClientY;\n      this.scrollLeft = container.scrollLeft;\n      this.scrollTop = container.scrollTop;\n    }\n  }\n\n  processEnd() {\n    const { onEndScroll } = this.props;\n    const container = this.container.current;\n\n    if (container && onEndScroll) {\n      onEndScroll({\n        external: !this.internal,\n      });\n    }\n\n    this.pressed = false;\n    this.started = false;\n    this.scrolling = false;\n    this.internal = false;\n\n    document.body.classList.remove(\"cursor-grab\");\n    this.forceUpdate();\n  }\n\n  getRef(el: HTMLDivElement) {\n    [this.container, this.props.innerRef].forEach((ref) => {\n      if (ref) {\n        if (typeof ref === \"function\") {\n          ref(el);\n        } else {\n          (ref as MutableRefObject<HTMLElement>).current = el;\n        }\n      }\n    });\n  }\n\n  render() {\n    const { children, draggingClassName, className, style, hideScrollbars } = this.props;\n\n    return (\n      <div\n        className={classnames(className, this.pressed && draggingClassName, {\n          \"!scroll-auto [&>*]:pointer-events-none [&>*]:cursor-grab\": this.pressed,\n          \"overflow-auto\": this.isMobile,\n          \"overflow-hidden !overflow-x-hidden [overflow:-moz-scrollbars-none] [scrollbar-width:none]\": hideScrollbars,\n          \"[&::-webkit-scrollbar]:[-webkit-appearance:none !important] [&::-webkit-scrollbar]:!hidden [&::-webkit-scrollbar]:!h-0 [&::-webkit-scrollbar]:!w-0 [&::-webkit-scrollbar]:!bg-transparent\":\n            hideScrollbars,\n        })}\n        style={style}\n        ref={this.getRef}\n        onScroll={this.onScroll}\n      >\n        {children}\n      </div>\n    );\n  }\n}\n"], "names": [], "mappings": ";;;;;AACA,IAAI,SAAS,GAAG,MAAM,CAAC,cAAc,CAAC;AACtC,IAAI,eAAe,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,KAAK,KAAK,GAAG,IAAI,GAAG,GAAG,SAAS,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE,UAAU,EAAE,IAAI,EAAE,YAAY,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;AAChK,IAAI,aAAa,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,KAAK,KAAK,eAAe,CAAC,GAAG,EAAE,OAAO,GAAG,KAAK,QAAQ,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,EAAE,KAAK,CAAC,CAAC;AAK/G,MAAM,mBAAmB,GAAG,GAAG,CAAC;AAChC,MAAM,WAAW,GAAG,CAAC,CAAC;AACP,MAAM,eAAe,SAAS,aAAa,CAAC;AAC3D,EAAE,WAAW,CAAC,KAAK,EAAE;AACrB,IAAI,KAAK,CAAC,KAAK,CAAC,CAAC;AACjB,IAAI,aAAa,CAAC,IAAI,EAAE,WAAW,CAAC,CAAC;AACrC,IAAI,aAAa,CAAC,IAAI,EAAE,WAAW,CAAC,CAAC;AACrC,IAAI,aAAa,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;AACnC,IAAI,aAAa,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;AACnC,IAAI,aAAa,CAAC,IAAI,EAAE,UAAU,EAAE,KAAK,CAAC,CAAC;AAC3C,IAAI,aAAa,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC;AACpC,IAAI,aAAa,CAAC,IAAI,EAAE,YAAY,CAAC,CAAC;AACtC,IAAI,aAAa,CAAC,IAAI,EAAE,WAAW,CAAC,CAAC;AACrC,IAAI,aAAa,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;AACnC,IAAI,aAAa,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;AACnC;AACA,IAAI,aAAa,CAAC,IAAI,EAAE,aAAa,EAAE,MAAM;AAC7C,MAAM,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;AAC7B,MAAM,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,EAAE;AACzC,QAAQ,IAAI,CAAC,UAAU,EAAE,CAAC;AAC1B,OAAO;AACP,KAAK,CAAC,CAAC;AACP,IAAI,aAAa,CAAC,IAAI,EAAE,UAAU,EAAE,MAAM;AAC1C,MAAM,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC;AAC/C,MAAM,IAAI,SAAS,CAAC,UAAU,KAAK,IAAI,CAAC,UAAU,IAAI,SAAS,CAAC,SAAS,KAAK,IAAI,CAAC,SAAS,EAAE;AAC9F,QAAQ,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;AAC9B,QAAQ,IAAI,CAAC,aAAa,EAAE,CAAC;AAC7B,QAAQ,IAAI,CAAC,WAAW,EAAE,CAAC;AAC3B,OAAO;AACP,KAAK,CAAC,CAAC;AACP,IAAI,aAAa,CAAC,IAAI,EAAE,cAAc,EAAE,CAAC,CAAC,KAAK;AAC/C,MAAM,MAAM,EAAE,kBAAkB,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC;AAChD,MAAM,IAAI,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,MAAM,CAAC,EAAE;AACtC,QAAQ,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;AAC7B,QAAQ,IAAI,kBAAkB,IAAI,IAAI,CAAC,SAAS,EAAE;AAClD,UAAU,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;AAC9B,SAAS,MAAM;AACf,UAAU,MAAM,KAAK,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;AACrC,UAAU,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,OAAO,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC;AAC1D,UAAU,IAAI,CAAC,kBAAkB,IAAI,IAAI,CAAC,KAAK,CAAC,eAAe,EAAE;AACjE,YAAY,CAAC,CAAC,eAAe,EAAE,CAAC;AAChC,WAAW;AACX,SAAS;AACT,OAAO;AACP,KAAK,CAAC,CAAC;AACP,IAAI,aAAa,CAAC,IAAI,EAAE,YAAY,EAAE,MAAM;AAC5C,MAAM,MAAM,EAAE,kBAAkB,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC;AAChD,MAAM,IAAI,IAAI,CAAC,OAAO,EAAE;AACxB,QAAQ,IAAI,IAAI,CAAC,OAAO,KAAK,CAAC,IAAI,CAAC,SAAS,IAAI,CAAC,kBAAkB,CAAC,EAAE;AACtE,UAAU,IAAI,CAAC,UAAU,EAAE,CAAC;AAC5B,SAAS,MAAM;AACf,UAAU,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;AAC/B,SAAS;AACT,QAAQ,IAAI,CAAC,WAAW,EAAE,CAAC;AAC3B,OAAO;AACP,KAAK,CAAC,CAAC;AACP,IAAI,aAAa,CAAC,IAAI,EAAE,aAAa,EAAE,CAAC,CAAC,KAAK;AAC9C,MAAM,MAAM,EAAE,kBAAkB,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC;AAChD,MAAM,IAAI,IAAI,CAAC,OAAO,KAAK,CAAC,kBAAkB,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE;AACnE,QAAQ,MAAM,KAAK,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;AACnC,QAAQ,IAAI,KAAK,EAAE;AACnB,UAAU,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,OAAO,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC;AACzD,SAAS;AACT,QAAQ,CAAC,CAAC,cAAc,EAAE,CAAC;AAC3B,QAAQ,IAAI,IAAI,CAAC,KAAK,CAAC,eAAe,EAAE;AACxC,UAAU,CAAC,CAAC,eAAe,EAAE,CAAC;AAC9B,SAAS;AACT,OAAO;AACP,KAAK,CAAC,CAAC;AACP,IAAI,aAAa,CAAC,IAAI,EAAE,aAAa,EAAE,CAAC,CAAC,KAAK;AAC9C,MAAM,IAAI,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC,YAAY,EAAE,EAAE;AAC7D,QAAQ,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;AAC7B,QAAQ,IAAI,IAAI,CAAC,KAAK,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE;AAC3D,UAAU,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC,OAAO,CAAC,CAAC;AAClD,UAAU,CAAC,CAAC,cAAc,EAAE,CAAC;AAC7B,UAAU,IAAI,IAAI,CAAC,KAAK,CAAC,eAAe,EAAE;AAC1C,YAAY,CAAC,CAAC,eAAe,EAAE,CAAC;AAChC,WAAW;AACX,SAAS;AACT,OAAO;AACP,KAAK,CAAC,CAAC;AACP,IAAI,aAAa,CAAC,IAAI,EAAE,aAAa,EAAE,CAAC,CAAC,KAAK;AAC9C,MAAM,IAAI,IAAI,CAAC,OAAO,EAAE;AACxB,QAAQ,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC,OAAO,CAAC,CAAC;AAC/C,QAAQ,CAAC,CAAC,cAAc,EAAE,CAAC;AAC3B,QAAQ,IAAI,IAAI,CAAC,KAAK,CAAC,eAAe,EAAE;AACxC,UAAU,CAAC,CAAC,eAAe,EAAE,CAAC;AAC9B,SAAS;AACT,OAAO;AACP,KAAK,CAAC,CAAC;AACP,IAAI,aAAa,CAAC,IAAI,EAAE,WAAW,EAAE,CAAC,CAAC,KAAK;AAC5C,MAAM,IAAI,IAAI,CAAC,OAAO,EAAE;AACxB,QAAQ,IAAI,IAAI,CAAC,OAAO,EAAE;AAC1B,UAAU,IAAI,CAAC,UAAU,EAAE,CAAC;AAC5B,SAAS,MAAM;AACf,UAAU,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;AAChC,UAAU,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;AAC/B,UAAU,IAAI,CAAC,WAAW,EAAE,CAAC;AAC7B,UAAU,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE;AAClC,YAAY,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;AAClC,WAAW;AACX,SAAS;AACT,QAAQ,CAAC,CAAC,cAAc,EAAE,CAAC;AAC3B,QAAQ,IAAI,IAAI,CAAC,KAAK,CAAC,eAAe,EAAE;AACxC,UAAU,CAAC,CAAC,eAAe,EAAE,CAAC;AAC9B,SAAS;AACT,OAAO;AACP,KAAK,CAAC,CAAC;AACP,IAAI,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC;AACvC,IAAI,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,IAAI,CAAC,WAAW,EAAE,mBAAmB,CAAC,CAAC;AACvE,IAAI,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;AAC3B,IAAI,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;AACzB,IAAI,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;AACzB,IAAI,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;AAC1B,IAAI,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AACzC,GAAG;AACH,EAAE,iBAAiB,GAAG;AACtB,IAAI,MAAM,EAAE,kBAAkB,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC;AAC9C,IAAI,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC;AAC7C,IAAI,MAAM,CAAC,gBAAgB,CAAC,SAAS,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;AACvD,IAAI,MAAM,CAAC,gBAAgB,CAAC,WAAW,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;AAC3D,IAAI,MAAM,CAAC,gBAAgB,CAAC,WAAW,EAAE,IAAI,CAAC,WAAW,EAAE,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC,CAAC;AAC/E,IAAI,MAAM,CAAC,gBAAgB,CAAC,UAAU,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;AACzD,IAAI,SAAS,CAAC,gBAAgB,CAAC,YAAY,EAAE,IAAI,CAAC,YAAY,EAAE;AAChE,MAAM,OAAO,EAAE,KAAK;AACpB,KAAK,CAAC,CAAC;AACP,IAAI,SAAS,CAAC,gBAAgB,CAAC,WAAW,EAAE,IAAI,CAAC,WAAW,EAAE;AAC9D,MAAM,OAAO,EAAE,KAAK;AACpB,KAAK,CAAC,CAAC;AACP,IAAI,IAAI,kBAAkB,EAAE;AAC5B,MAAM,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;AAC5C,MAAM,IAAI,IAAI,CAAC,QAAQ,EAAE;AACzB,QAAQ,IAAI,CAAC,WAAW,EAAE,CAAC;AAC3B,OAAO;AACP,KAAK;AACL,GAAG;AACH,EAAE,oBAAoB,GAAG;AACzB,IAAI,MAAM,CAAC,mBAAmB,CAAC,SAAS,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;AAC1D,IAAI,MAAM,CAAC,mBAAmB,CAAC,WAAW,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;AAC9D,IAAI,MAAM,CAAC,mBAAmB,CAAC,WAAW,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;AAC9D,IAAI,MAAM,CAAC,mBAAmB,CAAC,UAAU,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;AAC5D,GAAG;AACH,EAAE,UAAU,GAAG;AACf,IAAI,OAAO,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC;AAClC,GAAG;AACH,EAAE,cAAc,GAAG;AACnB,IAAI,OAAO,OAAO,MAAM,CAAC,WAAW,KAAK,WAAW,IAAI,SAAS,CAAC,SAAS,CAAC,OAAO,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC;AACvG,GAAG;AACH,EAAE,WAAW,CAAC,MAAM,EAAE;AACtB,IAAI,MAAM,cAAc,GAAG,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC;AACrD,IAAI,IAAI,cAAc,EAAE;AACxB,MAAM,MAAM,OAAO,GAAG,MAAM,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC;AACrD,MAAM,OAAO,OAAO,KAAK,IAAI,IAAI,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC,CAAC;AACrE,KAAK,MAAM;AACX,MAAM,OAAO,IAAI,CAAC;AAClB,KAAK;AACL,GAAG;AACH,EAAE,YAAY,GAAG;AACjB,IAAI,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC;AAC7C,IAAI,OAAO,SAAS,KAAK,SAAS,CAAC,WAAW,GAAG,SAAS,CAAC,WAAW,IAAI,SAAS,CAAC,YAAY,GAAG,SAAS,CAAC,YAAY,CAAC,CAAC;AAC3H,GAAG;AACH,EAAE,YAAY,CAAC,OAAO,EAAE,OAAO,EAAE;AACjC,IAAI,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC;AAC7C,IAAI,IAAI,CAAC,UAAU,GAAG,SAAS,EAAE,UAAU,CAAC;AAC5C,IAAI,IAAI,CAAC,SAAS,GAAG,SAAS,EAAE,SAAS,CAAC;AAC1C,IAAI,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;AAC3B,IAAI,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;AAC3B,IAAI,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;AACxB,GAAG;AACH,EAAE,YAAY,CAAC,YAAY,GAAG,IAAI,EAAE;AACpC,IAAI,MAAM,EAAE,aAAa,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC;AACzC,IAAI,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;AACxB,IAAI,IAAI,YAAY,EAAE;AACtB,MAAM,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;AACjD,KAAK;AACL,IAAI,IAAI,aAAa,EAAE;AACvB,MAAM,aAAa,CAAC;AACpB,QAAQ,QAAQ,EAAE,CAAC,IAAI,CAAC,QAAQ;AAChC,OAAO,CAAC,CAAC;AACT,KAAK;AACL,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;AACvB,GAAG;AACH;AACA,EAAE,aAAa,GAAG;AAClB,IAAI,IAAI,IAAI,CAAC,OAAO,EAAE;AACtB,MAAM,MAAM,EAAE,QAAQ,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC;AACtC,MAAM,IAAI,QAAQ,EAAE;AACpB,QAAQ,QAAQ,CAAC;AACjB,UAAU,QAAQ,EAAE,CAAC,IAAI,CAAC,QAAQ;AAClC,SAAS,CAAC,CAAC;AACX,OAAO;AACP,KAAK,MAAM;AACX,MAAM,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;AAC/B,KAAK;AACL,GAAG;AACH;AACA,EAAE,WAAW,CAAC,UAAU,EAAE,UAAU,EAAE;AACtC,IAAI,MAAM,EAAE,UAAU,EAAE,QAAQ,EAAE,kBAAkB,EAAE,QAAQ,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC;AAC9E,IAAI,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC;AAC7C,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;AACvB,MAAM,IAAI,UAAU,IAAI,IAAI,CAAC,GAAG,CAAC,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,kBAAkB,IAAI,QAAQ,IAAI,IAAI,CAAC,GAAG,CAAC,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,kBAAkB,EAAE;AAC1J,QAAQ,IAAI,CAAC,OAAO,GAAG,UAAU,CAAC;AAClC,QAAQ,IAAI,CAAC,OAAO,GAAG,UAAU,CAAC;AAClC,QAAQ,IAAI,CAAC,YAAY,EAAE,CAAC;AAC5B,OAAO;AACP,KAAK,MAAM;AACX,MAAM,IAAI,UAAU,EAAE;AACtB,QAAQ,SAAS,CAAC,UAAU,IAAI,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC;AAC1D,OAAO;AACP,MAAM,IAAI,QAAQ,EAAE;AACpB,QAAQ,SAAS,CAAC,SAAS,IAAI,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC;AACzD,OAAO;AACP,MAAM,IAAI,QAAQ,EAAE;AACpB,QAAQ,QAAQ,CAAC,EAAE,QAAQ,EAAE,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC;AAC/C,OAAO;AACP,MAAM,IAAI,CAAC,OAAO,GAAG,UAAU,CAAC;AAChC,MAAM,IAAI,CAAC,OAAO,GAAG,UAAU,CAAC;AAChC,MAAM,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC,UAAU,CAAC;AAC7C,MAAM,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC,SAAS,CAAC;AAC3C,KAAK;AACL,GAAG;AACH,EAAE,UAAU,GAAG;AACf,IAAI,MAAM,EAAE,WAAW,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC;AACvC,IAAI,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC;AAC7C,IAAI,IAAI,SAAS,IAAI,WAAW,EAAE;AAClC,MAAM,WAAW,CAAC;AAClB,QAAQ,QAAQ,EAAE,CAAC,IAAI,CAAC,QAAQ;AAChC,OAAO,CAAC,CAAC;AACT,KAAK;AACL,IAAI,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;AACzB,IAAI,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;AACzB,IAAI,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;AAC3B,IAAI,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;AAC1B,IAAI,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC;AAClD,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;AACvB,GAAG;AACH,EAAE,MAAM,CAAC,EAAE,EAAE;AACb,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,CAAC,GAAG,KAAK;AAC3D,MAAM,IAAI,GAAG,EAAE;AACf,QAAQ,IAAI,OAAO,GAAG,KAAK,UAAU,EAAE;AACvC,UAAU,GAAG,CAAC,EAAE,CAAC,CAAC;AAClB,SAAS,MAAM;AACf,UAAU,GAAG,CAAC,OAAO,GAAG,EAAE,CAAC;AAC3B,SAAS;AACT,OAAO;AACP,KAAK,CAAC,CAAC;AACP,GAAG;AACH,EAAE,MAAM,GAAG;AACX,IAAI,MAAM,EAAE,QAAQ,EAAE,iBAAiB,EAAE,SAAS,EAAE,KAAK,EAAE,cAAc,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC;AACzF,IAAI,uBAAuB,GAAG;AAC9B,MAAM,KAAK;AACX,MAAM;AACN,QAAQ,SAAS,EAAE,UAAU,CAAC,SAAS,EAAE,IAAI,CAAC,OAAO,IAAI,iBAAiB,EAAE;AAC5E,UAAU,0DAA0D,EAAE,IAAI,CAAC,OAAO;AAClF,UAAU,eAAe,EAAE,IAAI,CAAC,QAAQ;AACxC,UAAU,2FAA2F,EAAE,cAAc;AACrH,UAAU,2LAA2L,EAAE,cAAc;AACrN,SAAS,CAAC;AACV,QAAQ,KAAK;AACb,QAAQ,GAAG,EAAE,IAAI,CAAC,MAAM;AACxB,QAAQ,QAAQ,EAAE,IAAI,CAAC,QAAQ;AAC/B,QAAQ,QAAQ;AAChB,OAAO;AACP,KAAK,CAAC;AACN,GAAG;AACH,CAAC;AACD,aAAa,CAAC,eAAe,EAAE,cAAc,EAAE;AAC/C,EAAE,kBAAkB,EAAE,IAAI;AAC1B,EAAE,cAAc,EAAE,IAAI;AACtB,EAAE,kBAAkB,EAAE,EAAE;AACxB,EAAE,QAAQ,EAAE,IAAI;AAChB,EAAE,UAAU,EAAE,IAAI;AAClB,EAAE,eAAe,EAAE,KAAK;AACxB,EAAE,KAAK,EAAE,EAAE;AACX,EAAE,OAAO,EAAE,CAAC,WAAW,CAAC;AACxB,CAAC,CAAC;;;;"}