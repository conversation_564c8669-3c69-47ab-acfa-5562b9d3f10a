{"version": 3, "file": "is-object.mjs", "sources": ["../../../src/helpers/is-object.ts"], "sourcesContent": ["/**\n * Check if provided parameter is plain object\n * @param item\n * @returns boolean\n */\nexport function isObject(item: unknown): item is Record<string, unknown> {\n  return item !== null && typeof item === \"object\" && item.constructor === Object;\n}\n"], "names": [], "mappings": "AACO,SAAS,QAAQ,CAAC,IAAI,EAAE;AAC/B,EAAE,OAAO,IAAI,KAAK,IAAI,IAAI,OAAO,IAAI,KAAK,QAAQ,IAAI,IAAI,CAAC,WAAW,KAAK,MAAM,CAAC;AAClF;;;;"}