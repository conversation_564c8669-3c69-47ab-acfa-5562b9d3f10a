{"version": 3, "file": "use-is-mounted.mjs", "sources": ["../../../src/hooks/use-is-mounted.ts"], "sourcesContent": ["\"use client\";\n\nimport { useEffect, useState } from \"react\";\n\nexport function useIsMounted() {\n  const [mounted, setMounted] = useState(false);\n\n  useEffect(() => setMounted(true), []);\n\n  return mounted;\n}\n"], "names": [], "mappings": ";;AAGO,SAAS,YAAY,GAAG;AAC/B,EAAE,MAAM,CAAC,OAAO,EAAE,UAAU,CAAC,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC;AAChD,EAAE,SAAS,CAAC,MAAM,UAAU,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC,CAAC;AACxC,EAAE,OAAO,OAAO,CAAC;AACjB;;;;"}