import { cloneDeep } from '../helpers/clone-deep.mjs';
import { mergeDeep } from '../helpers/merge-deep.mjs';
import { theme } from '../theme.mjs';

const store = {
  theme: cloneDeep(theme)
};
function setThemeMode(mode) {
  store.mode = mode;
}
function getThemeMode() {
  return store.mode;
}
function setTheme(theme$1) {
  if (theme$1) store.theme = mergeDeep(theme, theme$1);
}
function getTheme() {
  return cloneDeep(store.theme);
}

export { getTheme, getThemeMode, setTheme, setThemeMode };
//# sourceMappingURL=index.mjs.map
