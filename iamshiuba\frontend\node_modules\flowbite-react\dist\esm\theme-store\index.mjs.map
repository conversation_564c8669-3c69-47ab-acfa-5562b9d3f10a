{"version": 3, "file": "index.mjs", "sources": ["../../../src/theme-store/index.tsx"], "sourcesContent": ["import type { CustomFlowbiteTheme, FlowbiteTheme } from \"../components/Flowbite\";\nimport { cloneDeep } from \"../helpers/clone-deep\";\nimport { mergeDeep } from \"../helpers/merge-deep\";\nimport type { ThemeMode } from \"../hooks/use-theme-mode\";\nimport { theme as defaultTheme } from \"../theme\";\n\ninterface ThemeStore {\n  mode?: ThemeMode;\n  theme: FlowbiteTheme;\n}\n\nconst store: ThemeStore = {\n  theme: cloneDeep(defaultTheme),\n};\n\nexport function setThemeMode(mode?: ThemeMode) {\n  store.mode = mode;\n}\n\nexport function getThemeMode(): ThemeMode | undefined {\n  return store.mode;\n}\n\nexport function setTheme(theme?: CustomFlowbiteTheme) {\n  if (theme) store.theme = mergeDeep(defaultTheme, theme);\n}\n\nexport function getTheme(): FlowbiteTheme {\n  return cloneDeep(store.theme);\n}\n"], "names": ["defaultTheme", "theme"], "mappings": ";;;;AAIA,MAAM,KAAK,GAAG;AACd,EAAE,KAAK,EAAE,SAAS,CAACA,KAAY,CAAC;AAChC,CAAC,CAAC;AACK,SAAS,YAAY,CAAC,IAAI,EAAE;AACnC,EAAE,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC;AACpB,CAAC;AACM,SAAS,YAAY,GAAG;AAC/B,EAAE,OAAO,KAAK,CAAC,IAAI,CAAC;AACpB,CAAC;AACM,SAAS,QAAQ,CAACC,OAAK,EAAE;AAChC,EAAE,IAAIA,OAAK,EAAE,KAAK,CAAC,KAAK,GAAG,SAAS,CAACD,KAAY,EAAEC,OAAK,CAAC,CAAC;AAC1D,CAAC;AACM,SAAS,QAAQ,GAAG;AAC3B,EAAE,OAAO,SAAS,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AAChC;;;;"}