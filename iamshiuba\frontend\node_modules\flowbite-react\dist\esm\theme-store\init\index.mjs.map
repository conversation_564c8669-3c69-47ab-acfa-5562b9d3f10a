{"version": 3, "file": "index.mjs", "sources": ["../../../../src/theme-store/init/index.tsx"], "sourcesContent": ["import type { CustomFlowbiteTheme } from \"../../components/Flowbite\";\nimport type { ThemeMode } from \"../../hooks/use-theme-mode\";\nimport { ThemeClientInit } from \"./client\";\nimport { ThemeModeInit } from \"./mode\";\nimport { ThemeServerInit } from \"./server\";\n\ninterface Props {\n  mode?: ThemeMode;\n  theme?: CustomFlowbiteTheme;\n}\n\nexport function ThemeInit({ mode, theme }: Props) {\n  return (\n    <>\n      <ThemeModeInit mode={mode} />\n      <ThemeServerInit theme={theme} />\n      <ThemeClientInit theme={theme} />\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAKO,SAAS,SAAS,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,EAAE;AAC3C,EAAE,uBAAuB,IAAI,CAAC,QAAQ,EAAE,EAAE,QAAQ,EAAE;AACpD,oBAAoB,GAAG,CAAC,aAAa,EAAE,EAAE,IAAI,EAAE,CAAC;AAChD,oBAAoB,GAAG,CAAC,eAAe,EAAE,EAAE,KAAK,EAAE,CAAC;AACnD,oBAAoB,GAAG,CAAC,eAAe,EAAE,EAAE,KAAK,EAAE,CAAC;AACnD,GAAG,EAAE,CAAC,CAAC;AACP;;;;"}