import { accordionTheme } from './components/Accordion/theme.mjs';
import { alertTheme } from './components/Alert/theme.mjs';
import { avatarTheme } from './components/Avatar/theme.mjs';
import { badgeTheme } from './components/Badge/theme.mjs';
import { blockquoteTheme } from './components/Blockquote/theme.mjs';
import { breadcrumbTheme } from './components/Breadcrumb/theme.mjs';
import { buttonTheme, buttonGroupTheme } from './components/Button/theme.mjs';
import { cardTheme } from './components/Card/theme.mjs';
import { carouselTheme } from './components/Carousel/theme.mjs';
import { checkboxTheme } from './components/Checkbox/theme.mjs';
import { clipboardTheme } from './components/Clipboard/theme.mjs';
import { darkThemeToggleTheme } from './components/DarkThemeToggle/theme.mjs';
import { datePickerTheme } from './components/Datepicker/theme.mjs';
import { drawerTheme } from './components/Drawer/theme.mjs';
import { dropdownTheme } from './components/Dropdown/theme.mjs';
import { fileInputTheme } from './components/FileInput/theme.mjs';
import { floatingLabelTheme } from './components/FloatingLabel/theme.mjs';
import { footerTheme } from './components/Footer/theme.mjs';
import { helperTextTheme } from './components/HelperText/theme.mjs';
import { hrTheme } from './components/HR/theme.mjs';
import { kbdTheme } from './components/Kbd/theme.mjs';
import { labelTheme } from './components/Label/theme.mjs';
import { listTheme } from './components/List/theme.mjs';
import { listGroupTheme } from './components/ListGroup/theme.mjs';
import { megaMenuTheme } from './components/MegaMenu/theme.mjs';
import { modalTheme } from './components/Modal/theme.mjs';
import { navbarTheme } from './components/Navbar/theme.mjs';
import { paginationTheme } from './components/Pagination/theme.mjs';
import { popoverTheme } from './components/Popover/theme.mjs';
import { progressTheme } from './components/Progress/theme.mjs';
import { radioTheme } from './components/Radio/theme.mjs';
import { rangeSliderTheme } from './components/RangeSlider/theme.mjs';
import { ratingTheme, ratingAdvancedTheme } from './components/Rating/theme.mjs';
import { selectTheme } from './components/Select/theme.mjs';
import { sidebarTheme } from './components/Sidebar/theme.mjs';
import { spinnerTheme } from './components/Spinner/theme.mjs';
import { tableTheme } from './components/Table/theme.mjs';
import { tabTheme } from './components/Tabs/theme.mjs';
import { textareaTheme } from './components/Textarea/theme.mjs';
import { textInputTheme } from './components/TextInput/theme.mjs';
import { timelineTheme } from './components/Timeline/theme.mjs';
import { toastTheme } from './components/Toast/theme.mjs';
import { toggleSwitchTheme } from './components/ToggleSwitch/theme.mjs';
import { tooltipTheme } from './components/Tooltip/theme.mjs';

const theme = {
  accordion: accordionTheme,
  alert: alertTheme,
  avatar: avatarTheme,
  badge: badgeTheme,
  blockquote: blockquoteTheme,
  breadcrumb: breadcrumbTheme,
  button: buttonTheme,
  buttonGroup: buttonGroupTheme,
  card: cardTheme,
  carousel: carouselTheme,
  checkbox: checkboxTheme,
  clipboard: clipboardTheme,
  datepicker: datePickerTheme,
  darkThemeToggle: darkThemeToggleTheme,
  drawer: drawerTheme,
  dropdown: dropdownTheme,
  fileInput: fileInputTheme,
  floatingLabel: floatingLabelTheme,
  footer: footerTheme,
  helperText: helperTextTheme,
  hr: hrTheme,
  kbd: kbdTheme,
  label: labelTheme,
  listGroup: listGroupTheme,
  list: listTheme,
  megaMenu: megaMenuTheme,
  modal: modalTheme,
  navbar: navbarTheme,
  pagination: paginationTheme,
  popover: popoverTheme,
  progress: progressTheme,
  radio: radioTheme,
  rangeSlider: rangeSliderTheme,
  rating: ratingTheme,
  ratingAdvanced: ratingAdvancedTheme,
  select: selectTheme,
  textInput: textInputTheme,
  textarea: textareaTheme,
  toggleSwitch: toggleSwitchTheme,
  sidebar: sidebarTheme,
  spinner: spinnerTheme,
  table: tableTheme,
  tabs: tabTheme,
  timeline: timelineTheme,
  toast: toastTheme,
  tooltip: tooltipTheme
};

export { theme };
//# sourceMappingURL=theme.mjs.map
