{"version": 3, "file": "theme.mjs", "sources": ["../../src/theme.ts"], "sourcesContent": ["import type { FlowbiteTheme } from \".\";\nimport { accordionTheme } from \"./components/Accordion/theme\";\nimport { alertTheme } from \"./components/Alert/theme\";\nimport { avatarTheme } from \"./components/Avatar/theme\";\nimport { badgeTheme } from \"./components/Badge/theme\";\nimport { blockquoteTheme } from \"./components/Blockquote/theme\";\nimport { breadcrumbTheme } from \"./components/Breadcrumb/theme\";\nimport { buttonGroupTheme, buttonTheme } from \"./components/Button/theme\";\nimport { cardTheme } from \"./components/Card/theme\";\nimport { carouselTheme } from \"./components/Carousel/theme\";\nimport { checkboxTheme } from \"./components/Checkbox/theme\";\nimport { clipboardTheme } from \"./components/Clipboard/theme\";\nimport { darkThemeToggleTheme } from \"./components/DarkThemeToggle/theme\";\nimport { datePickerTheme } from \"./components/Datepicker/theme\";\nimport { drawerTheme } from \"./components/Drawer/theme\";\nimport { dropdownTheme } from \"./components/Dropdown/theme\";\nimport { fileInputTheme } from \"./components/FileInput/theme\";\nimport { floatingLabelTheme } from \"./components/FloatingLabel/theme\";\nimport { footerTheme } from \"./components/Footer/theme\";\nimport { helperTextTheme } from \"./components/HelperText/theme\";\nimport { hrTheme } from \"./components/HR/theme\";\nimport { kbdTheme } from \"./components/Kbd/theme\";\nimport { labelTheme } from \"./components/Label/theme\";\nimport { listTheme } from \"./components/List/theme\";\nimport { listGroupTheme } from \"./components/ListGroup/theme\";\nimport { megaMenuTheme } from \"./components/MegaMenu/theme\";\nimport { modalTheme } from \"./components/Modal/theme\";\nimport { navbarTheme } from \"./components/Navbar/theme\";\nimport { paginationTheme } from \"./components/Pagination/theme\";\nimport { popoverTheme } from \"./components/Popover/theme\";\nimport { progressTheme } from \"./components/Progress/theme\";\nimport { radioTheme } from \"./components/Radio/theme\";\nimport { rangeSliderTheme } from \"./components/RangeSlider/theme\";\nimport { ratingAdvancedTheme, ratingTheme } from \"./components/Rating/theme\";\nimport { selectTheme } from \"./components/Select/theme\";\nimport { sidebarTheme } from \"./components/Sidebar/theme\";\nimport { spinnerTheme } from \"./components/Spinner/theme\";\nimport { tableTheme } from \"./components/Table/theme\";\nimport { tabTheme } from \"./components/Tabs/theme\";\nimport { textareaTheme } from \"./components/Textarea/theme\";\nimport { textInputTheme } from \"./components/TextInput/theme\";\nimport { timelineTheme } from \"./components/Timeline/theme\";\nimport { toastTheme } from \"./components/Toast/theme\";\nimport { toggleSwitchTheme } from \"./components/ToggleSwitch/theme\";\nimport { tooltipTheme } from \"./components/Tooltip/theme\";\n\nexport const theme: FlowbiteTheme = {\n  accordion: accordionTheme,\n  alert: alertTheme,\n  avatar: avatarTheme,\n  badge: badgeTheme,\n  blockquote: blockquoteTheme,\n  breadcrumb: breadcrumbTheme,\n  button: buttonTheme,\n  buttonGroup: buttonGroupTheme,\n  card: cardTheme,\n  carousel: carouselTheme,\n  checkbox: checkboxTheme,\n  clipboard: clipboardTheme,\n  datepicker: datePickerTheme,\n  darkThemeToggle: darkThemeToggleTheme,\n  drawer: drawerTheme,\n  dropdown: dropdownTheme,\n  fileInput: fileInputTheme,\n  floatingLabel: floatingLabelTheme,\n  footer: footerTheme,\n  helperText: helperTextTheme,\n  hr: hrTheme,\n  kbd: kbdTheme,\n  label: labelTheme,\n  listGroup: listGroupTheme,\n  list: listTheme,\n  megaMenu: megaMenuTheme,\n  modal: modalTheme,\n  navbar: navbarTheme,\n  pagination: paginationTheme,\n  popover: popoverTheme,\n  progress: progressTheme,\n  radio: radioTheme,\n  rangeSlider: rangeSliderTheme,\n  rating: ratingTheme,\n  ratingAdvanced: ratingAdvancedTheme,\n  select: selectTheme,\n  textInput: textInputTheme,\n  textarea: textareaTheme,\n  toggleSwitch: toggleSwitchTheme,\n  sidebar: sidebarTheme,\n  spinner: spinnerTheme,\n  table: tableTheme,\n  tabs: tabTheme,\n  timeline: timelineTheme,\n  toast: toastTheme,\n  tooltip: tooltipTheme,\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6CY,MAAC,KAAK,GAAG;AACrB,EAAE,SAAS,EAAE,cAAc;AAC3B,EAAE,KAAK,EAAE,UAAU;AACnB,EAAE,MAAM,EAAE,WAAW;AACrB,EAAE,KAAK,EAAE,UAAU;AACnB,EAAE,UAAU,EAAE,eAAe;AAC7B,EAAE,UAAU,EAAE,eAAe;AAC7B,EAAE,MAAM,EAAE,WAAW;AACrB,EAAE,WAAW,EAAE,gBAAgB;AAC/B,EAAE,IAAI,EAAE,SAAS;AACjB,EAAE,QAAQ,EAAE,aAAa;AACzB,EAAE,QAAQ,EAAE,aAAa;AACzB,EAAE,SAAS,EAAE,cAAc;AAC3B,EAAE,UAAU,EAAE,eAAe;AAC7B,EAAE,eAAe,EAAE,oBAAoB;AACvC,EAAE,MAAM,EAAE,WAAW;AACrB,EAAE,QAAQ,EAAE,aAAa;AACzB,EAAE,SAAS,EAAE,cAAc;AAC3B,EAAE,aAAa,EAAE,kBAAkB;AACnC,EAAE,MAAM,EAAE,WAAW;AACrB,EAAE,UAAU,EAAE,eAAe;AAC7B,EAAE,EAAE,EAAE,OAAO;AACb,EAAE,GAAG,EAAE,QAAQ;AACf,EAAE,KAAK,EAAE,UAAU;AACnB,EAAE,SAAS,EAAE,cAAc;AAC3B,EAAE,IAAI,EAAE,SAAS;AACjB,EAAE,QAAQ,EAAE,aAAa;AACzB,EAAE,KAAK,EAAE,UAAU;AACnB,EAAE,MAAM,EAAE,WAAW;AACrB,EAAE,UAAU,EAAE,eAAe;AAC7B,EAAE,OAAO,EAAE,YAAY;AACvB,EAAE,QAAQ,EAAE,aAAa;AACzB,EAAE,KAAK,EAAE,UAAU;AACnB,EAAE,WAAW,EAAE,gBAAgB;AAC/B,EAAE,MAAM,EAAE,WAAW;AACrB,EAAE,cAAc,EAAE,mBAAmB;AACrC,EAAE,MAAM,EAAE,WAAW;AACrB,EAAE,SAAS,EAAE,cAAc;AAC3B,EAAE,QAAQ,EAAE,aAAa;AACzB,EAAE,YAAY,EAAE,iBAAiB;AACjC,EAAE,OAAO,EAAE,YAAY;AACvB,EAAE,OAAO,EAAE,YAAY;AACvB,EAAE,KAAK,EAAE,UAAU;AACnB,EAAE,IAAI,EAAE,QAAQ;AAChB,EAAE,QAAQ,EAAE,aAAa;AACzB,EAAE,KAAK,EAAE,UAAU;AACnB,EAAE,OAAO,EAAE,YAAY;AACvB;;;;"}