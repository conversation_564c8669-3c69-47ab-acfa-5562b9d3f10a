import type { ComponentProps, FC } from "react";
import type { DeepPartial } from "../../types";
import { type ButtonProps } from "../Button";
export interface FlowbiteButtonGroupTheme {
    base: string;
    position: PositionInButtonGroup;
}
export interface PositionInButtonGroup {
    none: string;
    start: string;
    middle: string;
    end: string;
}
export interface ButtonGroupProps extends ComponentProps<"div">, Pick<ButtonProps, "outline" | "pill"> {
    theme?: DeepPartial<FlowbiteButtonGroupTheme>;
}
export declare const ButtonGroup: FC<ButtonGroupProps>;
