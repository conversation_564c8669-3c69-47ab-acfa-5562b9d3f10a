import type { FlowbiteDatepickerTheme } from "./Datepicker";
import type { Views, WeekStart } from "./helpers";
type DatepickerContextProps = {
    theme: FlowbiteDatepickerTheme;
    language: string;
    weekStart: WeekStart;
    minDate?: Date;
    maxDate?: Date;
    isOpen?: boolean;
    setIsOpen: (isOpen: boolean) => void;
    view: Views;
    setView: (value: Views) => void;
    selectedDate: Date | null;
    setSelectedDate: (date: Date | null) => void;
    changeSelectedDate: (date: Date, useAutohide: boolean) => void;
    viewDate: Date;
    setViewDate: (date: Date) => void;
};
export declare const DatepickerContext: import("react").Context<DatepickerContextProps | undefined>;
export declare function useDatePickerContext(): DatepickerContextProps;
export {};
