import type { ComponentProps, FC, ReactNode } from "react";
import type { DeepPartial, DynamicStringEnumKeysOf } from "../../types";
import type { FlowbiteBoolean, FlowbiteColors, FlowbiteSizes } from "../Flowbite";
export interface FlowbiteTextInputTheme {
    base: string;
    addon: string;
    field: {
        base: string;
        icon: {
            base: string;
            svg: string;
        };
        rightIcon: {
            base: string;
            svg: string;
        };
        input: {
            base: string;
            sizes: FlowbiteTextInputSizes;
            colors: FlowbiteTextInputColors;
            withIcon: FlowbiteBoolean;
            withRightIcon: FlowbiteBoolean;
            withAddon: FlowbiteBoolean;
            withShadow: FlowbiteBoolean;
        };
    };
}
export interface FlowbiteTextInputColors extends Pick<FlowbiteColors, "gray" | "info" | "failure" | "warning" | "success"> {
    [key: string]: string;
}
export interface FlowbiteTextInputSizes extends Pick<FlowbiteSizes, "sm" | "md" | "lg"> {
    [key: string]: string;
}
export interface TextInputProps extends Omit<ComponentProps<"input">, "ref" | "color"> {
    addon?: ReactNode;
    color?: DynamicStringEnumKeysOf<FlowbiteTextInputColors>;
    helperText?: ReactNode;
    icon?: FC<ComponentProps<"svg">>;
    rightIcon?: FC<ComponentProps<"svg">>;
    shadow?: boolean;
    sizing?: DynamicStringEnumKeysOf<FlowbiteTextInputSizes>;
    theme?: DeepPartial<FlowbiteTextInputTheme>;
}
export declare const TextInput: import("react").ForwardRefExoticComponent<TextInputProps & import("react").RefAttributes<HTMLInputElement>>;
