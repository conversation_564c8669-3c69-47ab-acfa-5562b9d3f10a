import { Link } from 'react-router-dom'
import { useTheme } from '../context/ThemeContext'
import { useTranslation } from '../hooks/useTranslation'

const Footer = () => {
  const currentYear = new Date().getFullYear()
  const { theme, setTheme } = useTheme()
  const { t, currentLanguage, setLanguage } = useTranslation()

  const socialLinks = [
    {
      href: 'https://twitter.com/iamshiuba',
      icon: 'fa-brands fa-x-twitter',
      label: 'Twitter',
      color: 'text-sky-600 dark:text-sky-400 hover:text-sky-500'
    },
    {
      href: 'https://soundcloud.com/iamshiuba',
      icon: 'fa-brands fa-soundcloud',
      label: 'SoundCloud',
      color: 'text-orange-600 dark:text-orange-400 hover:text-orange-500'
    },
    {
      href: 'https://youtube.com/@iamshiuba',
      icon: 'fa-brands fa-youtube',
      label: 'YouTube',
      color: 'text-red-600 hover:text-red-700'
    }
  ]

  const themeButtons = [
    { value: 'light', icon: 'fas fa-sun', label: 'Light' },
    { value: 'dark', icon: 'fas fa-moon', label: 'Dark' },
    { value: 'black', icon: 'fas fa-lightbulb', label: 'Black' },
    { value: 'red', icon: 'fas fa-heart', label: 'Red' }
  ]

  const languages = [
    { code: 'en-US', name: 'English', flag: 'fi-us' },
    { code: 'pt-BR', name: 'Português', flag: 'fi-br' },
    { code: 'jp-JP', name: '日本語', flag: 'fi-jp' },
    { code: 'ru-RU', name: 'Русский', flag: 'fi-ru' },
    { code: 'hi-IN', name: 'हिन्दी', flag: 'fi-in' },
    { code: 'zh-CN', name: '中文', flag: 'fi-cn' }
  ]

  return (
    <footer className="bg-[var(--background-secondary)] text-[var(--text-primary)] p-3">
      {/* Desktop Footer */}
      <div className="hidden md:block">
        {/* Top Section - Social Links and Theme */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 m-1 md:m-5">
          {/* Social Links */}
          <div className="space-y-4">
            <h4 className="mb-3 mt-3 text-2xl font-bold cursor-default hover:text-[var(--accent-color)] transition-colors">
              {t('followMe')}
            </h4>
            <div className="flex gap-5 text-4xl md:text-3xl">
              {socialLinks.map((social) => (
                <a
                  key={social.label}
                  href={social.href}
                  target="_blank"
                  rel="noopener"
                  aria-label={social.label}
                  className={`${social.icon} ${social.color} transition-colors`}
                />
              ))}
            </div>
          </div>

          {/* Theme Container */}
          <div className="space-y-4">
            <h4 className="mb-3 mt-3 text-2xl font-bold cursor-default hover:text-[var(--accent-color)] transition-colors">
              {t('theme')}
            </h4>
            <div className="flex justify-start w-20">
              {themeButtons.map((themeBtn, index) => (
                <button
                  key={themeBtn.value}
                  onClick={() => setTheme(themeBtn.value)}
                  title={themeBtn.label}
                  aria-label={themeBtn.label}
                  className={`theme-button text-4xl md:text-3xl transition-all ${
                    index < themeButtons.length - 1 ? 'mr-2.5' : ''
                  } ${theme === themeBtn.value ? 'active' : ''}`}
                >
                  <i className={themeBtn.icon}></i>
                </button>
              ))}
            </div>
          </div>
        </div>

        {/* Bottom Section - Quick Links and Language */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 m-1 md:m-5">
          {/* Quick Links */}
          <div className="space-y-4">
            <h4 className="mb-3 mt-3 text-2xl font-bold cursor-default hover:text-[var(--accent-color)] transition-colors">
              {t('quickLinks')}
            </h4>
            <ul className="space-y-2">
              <li>
                <Link
                  to="/about"
                  className="f-link hover:text-red-600 hover:font-bold cursor-pointer transition-all"
                >
                  {t('About')}
                </Link>
              </li>
              <li>
                <Link
                  to="/terms"
                  className="f-link hover:text-red-600 hover:font-bold cursor-pointer transition-all"
                >
                  {t('tos')}
                </Link>
              </li>
              <li>
                <Link
                  to="/privacy"
                  className="f-link hover:text-red-600 hover:font-bold cursor-pointer transition-all"
                >
                  {t('privacy')}
                </Link>
              </li>
              <li>
                <Link
                  to="/updates"
                  className="f-link hover:text-red-600 hover:font-bold cursor-pointer transition-all"
                >
                  {t('updates')}
                </Link>
              </li>
            </ul>
          </div>

          {/* Language Container */}
          <div className="space-y-4 pb-2">
            <h4 className="mb-3 mt-3 text-2xl font-bold cursor-default hover:text-[var(--accent-color)] transition-colors">
              {t('Translations')}
            </h4>
            <ul className="space-y-2" aria-labelledby="language-label" role="group">
              {languages.map((lang) => (
                <li key={lang.code} className="langItem grid items-center justify-start">
                  <button
                    onClick={() => setLanguage(lang.code)}
                    title={lang.name}
                    className={`fi ${lang.flag} text-3xl md:text-2xl transition-all ease-in-out duration-400 cursor-pointer ${
                      currentLanguage === lang.code
                        ? 'text-4xl md:text-3xl saturate-100 active'
                        : 'saturate-0 hover:saturate-100'
                    }`}
                    aria-label={lang.name}
                  >
                    <p className="text-sm md:text-base lg:text-lg fixed left-5/4 bottom-1 w-20">
                      {lang.code}
                    </p>
                  </button>
                </li>
              ))}
            </ul>
          </div>
        </div>

        {/* Copyright */}
        <div className="text-center">
          <hr className="my-2.5 opacity-25" />
          <p className="text-[var(--text-secondary)] mb-2.5 font-light">
            {t('prdBy')} &copy; 2024 - {currentYear} {t('footer')}
          </p>
        </div>
      </div>
    </footer>
  )
}

export default Footer
