import { Link, useLocation } from 'react-router-dom'
import { useTranslation } from '../hooks/useTranslation.jsx'

const Navbar = () => {
  const location = useLocation()
  const { t } = useTranslation()

  const isActive = (path) => location.pathname === path

  return (
    <nav id="navContainer">
      <div id="navBrand">
        <Link to="/">
          <img
            id="logo"
            src="/img/is_web.svg"
            alt="logo"
            href="/"
          />
        </Link>
      </div>
      <div id="desktopMenu">
        <ul>
          <li>
            <Link
              to="/"
              className={`n-link ${isActive('/') ? 'active' : ''}`}
              title="Homepage"
              data-translate="Homepage"
            >
              {t('Homepage')}
            </Link>
          </li>
          <li>
            <Link
              className={`n-link ${isActive('/streaming') ? 'active' : ''}`}
              to="/streaming"
              title="Streaming"
              data-translate="Streaming"
            >
              {t('Streaming')}
            </Link>
          </li>
          <li>
            <Link
              className={`n-link ${isActive('/about') ? 'active' : ''}`}
              to="/about"
              title="About"
              data-translate="About"
            >
              {t('About')}
            </Link>
          </li>
        </ul>
      </div>
    </nav>
  )
}

export default Navbar
