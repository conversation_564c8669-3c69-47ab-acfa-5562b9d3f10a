import { createContext, useContext, useState, useEffect } from 'react'

const ThemeContext = createContext()

export const useTheme = () => {
  const context = useContext(ThemeContext)
  if (!context) {
    throw new Error('useTheme must be used within a ThemeProvider')
  }
  return context
}

export const ThemeProvider = ({ children }) => {
  const [theme, setTheme] = useState('light')
  const availableThemes = ['light', 'dark', 'black', 'red']

  useEffect(() => {
    // Check for saved theme preference or default to system preference
    const savedTheme = localStorage.getItem('theme')
    const systemTheme = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light'

    const initialTheme = availableThemes.includes(savedTheme) ? savedTheme : systemTheme
    setTheme(initialTheme)
    applyTheme(initialTheme)
  }, [])

  const applyTheme = (newTheme) => {
    const root = window.document.documentElement

    // Remove all theme classes
    root.classList.remove('dark', 'theme-black', 'theme-red')

    // Apply new theme
    if (newTheme === 'dark') {
      root.classList.add('dark')
    } else if (newTheme === 'black') {
      root.classList.add('dark', 'theme-black')
    } else if (newTheme === 'red') {
      root.classList.add('theme-red')
    }

    // Set data attribute for CSS custom properties
    root.setAttribute('data-theme', newTheme)
  }

  const toggleTheme = () => {
    const currentIndex = availableThemes.indexOf(theme)
    const nextIndex = (currentIndex + 1) % availableThemes.length
    const newTheme = availableThemes[nextIndex]
    setTheme(newTheme)
    localStorage.setItem('theme', newTheme)
    applyTheme(newTheme)
  }

  const setThemeMode = (newTheme) => {
    if (!availableThemes.includes(newTheme)) {
      console.warn(`Theme "${newTheme}" is not available. Available themes:`, availableThemes)
      return
    }
    setTheme(newTheme)
    localStorage.setItem('theme', newTheme)
    applyTheme(newTheme)
  }

  const value = {
    theme,
    availableThemes,
    toggleTheme,
    setTheme: setThemeMode,
    isDark: theme === 'dark' || theme === 'black'
  }

  return (
    <ThemeContext.Provider value={value}>
      {children}
    </ThemeContext.Provider>
  )
}
