/* Main SCSS entry point - Modular structure with Tailwind CSS v3 + Flowbite */

/* Import base SCSS modules before Tailwind */
@import "./styles/scss/base/_variables.scss";
@import "./styles/scss/base/_base.scss";
@import "./styles/scss/base/_components.scss";

/* Import Tailwind CSS v3 and Flowbite after base styles */
@tailwind base;
@tailwind components;
@tailwind utilities;

/* Import SCSS modules after Tailwind */
@use "./styles/scss/container/_main.scss";
@use "./styles/scss/container/_navbar.scss";
@use "./styles/scss/container/_footer.scss";
@use "./styles/scss/components/_home.scss";
@use "./styles/scss/components/_streaming.scss";
@use "./styles/scss/components/_about.scss";
@use "./styles/scss/components/_terms.scss";
@use "./styles/scss/components/_privacy.scss";
@use "./styles/scss/components/_updates.scss";
@use "./styles/scss/components/_admin.scss";
@use "./styles/scss/components/_error.scss";
@use "./styles/scss/utils/_theme-selector.scss";
@use "./styles/scss/utils/_pwa.scss";

