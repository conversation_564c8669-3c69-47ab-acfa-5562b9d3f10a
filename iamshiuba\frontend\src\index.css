@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    font-family: 'Inter', system-ui, sans-serif;
  }

  body {
    @apply bg-white dark:bg-dark-900 text-dark-900 dark:text-dark-50;
    @apply transition-colors duration-300;
  }

  * {
    @apply border-dark-200 dark:border-dark-700;
  }
}

@layer components {
  .btn {
    @apply px-4 py-2 rounded-lg font-medium transition-all duration-200;
    @apply focus:outline-none focus:ring-2 focus:ring-offset-2;
  }

  .btn-primary {
    @apply bg-primary-600 text-white hover:bg-primary-700;
    @apply focus:ring-primary-500;
  }

  .btn-secondary {
    @apply bg-dark-200 text-dark-900 hover:bg-dark-300;
    @apply dark:bg-dark-700 dark:text-dark-100 dark:hover:bg-dark-600;
    @apply focus:ring-dark-500;
  }

  .btn-danger {
    @apply bg-red-600 text-white hover:bg-red-700;
    @apply focus:ring-red-500;
  }

  .input {
    @apply w-full px-3 py-2 border rounded-lg;
    @apply bg-white dark:bg-dark-800;
    @apply border-dark-300 dark:border-dark-600;
    @apply focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent;
    @apply placeholder-dark-400 dark:placeholder-dark-500;
  }

  .card {
    @apply bg-white dark:bg-dark-800 rounded-lg shadow-lg;
    @apply border border-dark-200 dark:border-dark-700;
  }

  .navbar {
    @apply bg-white/80 dark:bg-dark-900/80 backdrop-blur-md;
    @apply border-b border-dark-200 dark:border-dark-700;
  }

  .loading-spinner {
    @apply animate-spin rounded-full border-2 border-dark-300;
    @apply border-t-primary-600 dark:border-dark-600 dark:border-t-primary-400;
  }
}

@layer utilities {
  .text-gradient {
    @apply bg-gradient-to-r from-primary-600 to-purple-600 bg-clip-text text-transparent;
  }

  .glass {
    @apply bg-white/10 backdrop-blur-md border border-white/20;
  }

  .animate-fade-in {
    animation: fadeIn 0.5s ease-in-out;
  }

  .animate-slide-up {
    animation: slideUp 0.3s ease-out;
  }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  @apply bg-dark-100 dark:bg-dark-800;
}

::-webkit-scrollbar-thumb {
  @apply bg-dark-300 dark:bg-dark-600 rounded-full;
}

::-webkit-scrollbar-thumb:hover {
  @apply bg-dark-400 dark:bg-dark-500;
}

/* CSS Variables for theme support */
:root {
  --background-primary: #ffffff;
  --background-secondary: #f8fafc;
  --text-primary: #1e293b;
  --text-secondary: #64748b;
  --accent-color: #ef4444;
}

[data-theme="dark"] {
  --background-primary: #0f172a;
  --background-secondary: #1e293b;
  --text-primary: #f1f5f9;
  --text-secondary: #94a3b8;
  --accent-color: #ef4444;
}

[data-theme="black"] {
  --background-primary: #000000;
  --background-secondary: #111111;
  --text-primary: #ffffff;
  --text-secondary: #888888;
  --accent-color: #ef4444;
}

[data-theme="red"] {
  --background-primary: #fef2f2;
  --background-secondary: #fee2e2;
  --text-primary: #7f1d1d;
  --text-secondary: #991b1b;
  --accent-color: #dc2626;
}

@layer components {
  /* Home page specific styles */
  #home {
    @apply justify-center items-center;
  }

  .hero-section {
    @apply animate-[fade-in_1s_ease-in-out] py-16 overflow-hidden mx-auto md:max-w-[80%];
  }

  .hero-wrapper {
    @apply flex flex-col lg:flex-row items-center;
  }

  .hero-content {
    @apply mb-2.5;
  }

  .hero-content p {
    @apply text-lg mt-4 text-start;
  }

  .hero-buttons {
    @apply mt-10 flex flex-col space-y-2 sm:flex-row sm:space-y-0 sm:space-x-4;
  }

  .hero-buttons i {
    @apply mr-2;
  }

  .hBtn {
    @apply inline-flex items-center px-6 py-3 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors duration-300 font-medium;
  }

  .floating-image {
    @apply animate-[float_6s_ease-in-out_infinite] w-full md:w-1/2 mx-auto mt-30;
  }

  /* Highlight section styles */
  #highlight-section {
    @apply md:max-w-[80%] lg:max-w-[60%] mx-auto my-15;
  }

  #featured-card {
    @apply bg-[var(--background-secondary)] rounded-3xl shadow p-6;
  }

  #card-header {
    @apply mb-4 text-center;
  }

  #card-header h2 {
    @apply text-3xl font-bold text-[var(--text-primary)];
  }

  #card-header p {
    @apply text-lg text-[var(--text-secondary)];
  }

  #highlightContainer {
    @apply mb-4;
  }

  #highlightContainer iframe {
    @apply rounded-lg aspect-video w-full;
  }

  .highlight-wrapper {
    @apply w-full rounded-lg shadow;
  }

  .video-container {
    @apply relative aspect-video rounded-t-lg;
  }

  .video-container iframe {
    @apply absolute top-0 left-0 w-full h-full rounded-t-lg;
  }

  .placeholder-content {
    @apply absolute top-0 left-0 w-full h-full flex flex-col items-center justify-center bg-black/80 text-white p-5 text-center;
  }

  .placeholder-icon {
    @apply text-5xl text-red-600 mb-4;
  }

  .placeholder-content p {
    @apply mb-6 text-lg;
  }

  .load-video-btn {
    @apply bg-red-600 hover:bg-red-700 text-white px-6 py-3 rounded-full transition-all duration-300 transform hover:scale-105 flex items-center;
  }

  .load-video-btn i {
    @apply mr-2;
  }

  .highlight-loading {
    @apply flex flex-col items-center justify-center p-10 text-center;
    min-height: 250px;
  }

  .loading-spinner {
    @apply text-4xl text-red-600 mb-4;
  }

  .highlight-loading p {
    @apply text-[var(--text-secondary)];
  }

  .highlight-error {
    @apply flex flex-col items-center justify-center p-10 text-center min-h-[250px];
  }

  .error-icon {
    @apply text-4xl text-red-600 mb-4;
  }

  .highlight-error p {
    @apply text-[var(--text-secondary)] mb-6;
  }

  .retry-btn {
    @apply bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded transition-colors flex items-center;
  }

  .retry-btn i {
    @apply mr-2;
  }

  .button-container {
    @apply text-center;
  }

  .button-container button {
    @apply inline-flex items-center px-5 py-2.5 w-full justify-center text-sm font-medium text-center text-white bg-red-700 hover:bg-red-800 dark:bg-red-600 dark:hover:bg-red-700 rounded-lg focus:ring-4 focus:outline-none focus:ring-red-300 dark:focus:ring-red-800;
  }

  .button-container button i {
    @apply mr-2;
  }

  .button-container button p {
    @apply text-lg font-medium;
  }

  /* Social proof section styles */
  #social-proof {
    @apply pb-5;
  }

  #statsContainer {
    @apply flex flex-row justify-center flex-wrap gap-2.5;
  }

  .stats-card {
    @apply text-center transition duration-300 ease-in-out bg-[var(--background-secondary)] text-[var(--text-primary)] my-auto p-8 rounded-3xl shadow-lg lg:max-w-70 md:max-w-50 w-full;
  }

  .stats-card i {
    @apply text-red-600 text-5xl;
  }

  .counter {
    @apply text-4xl font-bold my-4;
  }

  /* Footer specific styles */
  .theme-button {
    @apply p-2 rounded-lg transition-all duration-200 hover:bg-gray-100 dark:hover:bg-gray-700;
  }

  .theme-button.active {
    @apply bg-red-100 text-red-600 dark:bg-red-900 dark:text-red-400;
  }

  .f-link {
    @apply text-[var(--text-secondary)] hover:text-red-600 transition-colors duration-200;
  }

  .langItem .fi {
    @apply transition-all duration-300 ease-in-out cursor-pointer;
  }

  .langItem .fi:hover {
    @apply saturate-100 transform scale-110;
  }

  .langItem .fi.active {
    @apply saturate-100 transform scale-125;
  }
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

