<!DOCTYPE html>
<html lang="pt-BR">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta
      name="description"
      content="Explore the music and videos of <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, a talented artist."
    />
    <meta
      name="keywords"
      content="iamshiuba, music, videos, musician, entertainment, artist, singles, music videos"
    />
    <meta name="theme-color" content="#ff0000" />
    <title>IamSHIUBA</title>

    <link
      rel="icon"
      type="image/svg+xml"
      href="/img/is_web.svg"
      sizes="any"
    />
    <link
      rel="apple-touch-icon"
      href="/img/icons/192x.png"
    />
    <link
      rel="manifest"
      href="/manifest.json"
    />

    <link rel="dns-prefetch" href="https://cdnjs.cloudflare.com" />
    <link rel="dns-prefetch" href="https://cdn.jsdelivr.net" />
    <link rel="dns-prefetch" href="https://fonts.googleapis.com" />
    <link rel="dns-prefetch" href="https://fonts.gstatic.com" />

    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.6.0/css/all.min.css"
      integrity="sha512-Kc323vGBEqzTmouAECnVceyQqyqdsSiqLQISBL29aUW4U/M7pSPA/gEUZQqv1cwx4OnYxTxve5UMg5GT6L4JJg=="
      crossorigin="anonymous"
      referrerpolicy="no-referrer"
    />
    <link
      rel="stylesheet"
      href="https://cdn.jsdelivr.net/gh/lipis/flag-icons@7.2.3/css/flag-icons.min.css"
    />

    <meta property="og:title" content="IamSHIUBA" />
    <meta
      property="og:description"
      content="Explore the music and videos of IamSHIUBA, a talented artist."
    />
    <meta
      property="og:image"
      content="/img/iamshiuba_web.svg"
    />
    <meta property="og:url" content="https://iamshiuba.com" />
    <meta name="twitter:card" content="summary_large_image" />
    <script type="module" crossorigin src="/assets/index-DSwCMQtc.js"></script>
    <link rel="modulepreload" crossorigin href="/assets/vendor-1zw1pNgy.js">
    <link rel="modulepreload" crossorigin href="/assets/router-25aGOnra.js">
    <link rel="modulepreload" crossorigin href="/assets/utils-biXqt5p3.js">
    <link rel="stylesheet" crossorigin href="/assets/index-C65ZSTrX.css">
  </head>

  <body>
    <div id="root"></div>

    <!-- External Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/flowbite@3.1.2/dist/flowbite.min.js"></script>
  </body>
</html>
